{"24HoursInGameTimePassed": "게임 시간(24시간)이 확인되었습니다.", "abandonedCabins": "버려진 오두막", "abandonedMilitaryBase": "버려진 군사기지", "abandonedSupermarket": "버려진 슈퍼마켓", "addPlayerCap": "플레이어 추가", "addSwitchCap": "스위치 추가", "afkCap": "자리비움", "airfield": "비행장", "alarmHaveNotBeenTriggeredYet": "The alarm {alarm} have not been triggered yet.", "alias": "<PERSON><PERSON>", "aliasAlreadyExist": "Alias already exist.", "aliasIndexCouldNotBeFound": "Alias index could not be found.", "aliasWasAdded": "<PERSON><PERSON> was added.", "aliasWasRemoved": "<PERSON><PERSON> was removed.", "aliases": "Aliases", "all": "all", "allTeammatesAreDead": "모든 팀원들이 사망하였습니다.", "alreadySubscribedToItem": "이미 {name}을 사용하고 있습니다.", "ampersand": "앰퍼샌드", "andMorePlayers": "... 그리고 {number}명의 플레이어가 있습니다.", "any": "Any", "apostrophe": "아포스트로피", "arcticResearchBase": "북극 연구 기지", "asterisk": "애스터리스크", "asteriskCctvDesc": "* 의 뜻은 각 서버에서의 맵마다 다른 숫자 코드가 필요하다는 것을 의미합니다.", "atLocation": "{location}에 있습니다.", "atSign": "골뱅이", "autoDayCap": "자동-낮", "autoNightCap": "자동-밤", "autoOffAnyOnlineCap": "AUTO-OFF-ANY-ONLINE", "autoOffCap": "AUTO-OFF", "autoOffProximityCap": "AUTO-OFF-PROXIMITY", "autoOnAnyOnlineCap": "AUTO-ON-ANY-ONLINE", "autoOnCap": "AUTO-ON", "autoOnProximityCap": "AUTO-ON-PROXIMITY", "autoSettingCap": "자동 설정: ", "automaticallyTurnBackOnOff": "{time}에서 {status}이(가) 자동으로 변경되었습니다.", "automaticallyTurningBackOnOff": "{device}을(를) 자동으로 {status}(으)로 변경되었습니다.", "autoturret": "자동 터렛", "badGateway": "잘못된 게이트웨이: {error}", "banditCamp": "벤딧 캠프", "baseIsUnderAttack": "당신의 베이스가 공격받고 있습니다!", "battlemetricsApiRequestFailed": "Battlemetrics API Request Failed: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics Server {server} failed to update.", "battlemetricsGlobalLoginCap": "GLOBAL LOGIN", "battlemetricsGlobalLogoutCap": "GLOBAL LOGOUT", "battlemetricsGlobalNameChangesCap": "GLOBAL NAME CHANGES", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "Battlemetrics instance is missing id and name.", "battlemetricsInstanceCouldNotBeFound": "Battlemetrics Instance for {id} could not be found.", "battlemetricsOnlinePlayers": "Battlemetrics Online Players", "battlemetricsPlayersLogin": "Battlemetrics Players Login", "battlemetricsPlayersLogout": "Battlemetrics Players Logout", "battlemetricsPlayersNameChanged": "Battlemetrics Players Name Changed", "battlemetricsServerNameChanged": "Battlemetrics Server Name Changed", "battlemetricsServerNameChangesCap": "SERVER NAME CHANGES", "battlemetricsTrackerNameChangesCap": "TRACKER NAME CHANGES", "battlemetricsTrackerPlayerNameChanged": "Battlemetrics Tracker Player Name Changed", "blacklist": "Blacklist", "boomBox": "붐 박스", "bot": "bot", "broadcaster": "방송", "buttonValueChange": "Button Interaction - VerifyId: {id}, Value: {value}.", "buy": "buy", "calculated": "Calculated", "cargoAt": "{location} 에서", "cargoLeavingMapAt": "화물선이 {location} 에서 떠나고 있습니다.", "cargoLocatedAt": "화물선은 지금 {location} 에 있습니다.", "cargoNotCurrentlyOnMap": "화물선이 지도에 나타나지 않았습니다.", "cargoShipDetectedSetting": "화물선이 탐지되면 알림을 보내줍니다.", "cargoShipDockingAtHarbor": "Cargo ship just docked at the Harbor at {location}", "cargoShipDockingAtHarborSetting": "When Cargo Ship is docked at a harbor, send a notification.", "cargoShipEgressSetting": "화물선이 탈출단계일때 알림을 보내줍니다.", "cargoShipEntersEgressStage": "화물선은 {location}에서 떠나고 있습니다.", "cargoShipEntersMap": "화물선이 {location}에서 진입합니다.", "cargoShipLeftHarbor": "Cargo ship just left the Harbor at {location}", "cargoShipLeftMap": "화물선이 방금 {location}에서 떠났습니다.", "cargoShipLeftSetting": "화물선이 지도에서 떠났을때 알림을 보내줍니다.", "cargoShipLocated": "화물선은 지금 {location}에 있습니다.", "cargoship": "화물선", "ceilingLight": "천장 조명", "channelNameActivity": "활동", "channelNameAlarms": "알람", "channelNameCommands": "명령어", "channelNameEvents": "이벤트", "channelNameInformation": "정보", "channelNameServers": "서버", "channelNameSettings": "설정", "channelNameStorageMonitors": "보관 측정장치", "channelNameSwitchGroups": "스위치-그룹", "channelNameSwitches": "스위치", "channelNameTeamchat": "팀 채팅", "channelNameTrackers": "추적기", "chinook47": "치누크 47", "chinook47DetectedSetting": "치누크 47이 지도에서 탐지되면 알림을 보내줍니다.", "chinook47EntersMap": "치누크 47이 {location}에서 발견되었습니다.", "chinook47LeftMap": "치누크 47이 {location}에서 떠났습니다.", "chinook47Located": "치누크 47은 {location}에 있습니다.", "chinook47NotOnMap": "치누크 47은 현재 지도에서 발견되지 않았습니다.", "christmasLights": "크리스마스 조명", "circumflex": "곡절 부호", "clanTag": "클랜 태그", "codes": "코드", "colon": "콜론", "comma": "쉼표", "commandCap": "명령어", "commandDelaySetting": "명령어 딜레이를 설정합니다.", "commandNotPossibleDiscord": "디스코드에서 명령어를 실행할 수 없습니다.", "commandSyntaxAdd": "add", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "alive", "commandSyntaxArmored": "강화 합금", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "connection", "commandSyntaxConnections": "connections", "commandSyntaxCraft": "craft", "commandSyntaxDeath": "death", "commandSyntaxDeaths": "deaths", "commandSyntaxDecay": "부식", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "events", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "language", "commandSyntaxLarge": "large", "commandSyntaxLeader": "leader", "commandSyntaxList": "list", "commandSyntaxMarker": "marker", "commandSyntaxMarkers": "markers", "commandSyntaxMarket": "market", "commandSyntaxMetal": "철", "commandSyntaxMute": "mute", "commandSyntaxNote": "note", "commandSyntaxNotes": "notes", "commandSyntaxOff": "off", "commandSyntaxOffline": "offline", "commandSyntaxOn": "on", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "player", "commandSyntaxPlayers": "players", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "recycle", "commandSyntaxRemove": "remove", "commandSyntaxResearch": "research", "commandSyntaxSearch": "search", "commandSyntaxSend": "send", "commandSyntaxSmall": "small", "commandSyntaxStack": "stack", "commandSyntaxStatus": "status", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "돌", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "team", "commandSyntaxTime": "time", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendor", "commandSyntaxTwig": "기초 토대", "commandSyntaxUnmute": "unmute", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "upkeep", "commandSyntaxUptime": "uptime", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "나무", "commandsAlarmDesc": "스마트 알람 조작.", "commandsAlarmEditDesc": "스마트 알람의 속성을 수정합니다.", "commandsAlarmEditIdDesc": "스마트 알람의 고유ID 입니다.", "commandsAlarmEditImageDesc": "스마트 알람의 이미지를 설정합니다.", "commandsAliasAddAliasDesc": "The alias to use.", "commandsAliasAddDesc": "Add an alias.", "commandsAliasAddValueDesc": "The command/sequence of characters.", "commandsAliasDesc": "Create an alias for a command/sequence of characters.", "commandsAliasRemoveDesc": "Remove an alias.", "commandsAliasRemoveIndexDesc": "The index of the alias to remove.", "commandsAliasShowDesc": "Show all registered aliases.", "commandsBlacklistAddDesc": "Add user to the blacklist.", "commandsBlacklistDesc": "Blacklist a user from using the bot.", "commandsBlacklistDiscordUserDesc": "The discord user.", "commandsBlacklistRemoveDesc": "Remove user from the blacklist.", "commandsBlacklistShowDesc": "Show blacklisted users.", "commandsBlacklistSteamidDesc": "The steamid of the user.", "commandsCctvDesc": "파밍지역에 있는 CCTV 코드를 표시합니다.", "commandsCraftDesc": "Display the cost to craft an item.", "commandsCraftQuantityDesc": "The quantity of items to craft.", "commandsCredentialsAddDesc": "FCM 자격 증명을 추가합니다.", "commandsCredentialsDesc": "사용자 계정에 대한 FCM 자격 증명을 설정/해제합니다.", "commandsCredentialsRemoveDesc": "FCM 자격 증명을 제거합니다.", "commandsCredentialsRemoveSteamIdDesc": "제거할 FCM 자격 증명의 스팀 아이디 입니다.", "commandsCredentialsSetHosterDesc": "FCM 자격 증명의 호스트를 설정합니다.", "commandsCredentialsSetHosterSteamIdDesc": "FCM 자격 증명 호스트의 스팀 아이디 입니다.", "commandsCredentialsShowDesc": "현재 등록된 FCM 자격 증명을 확인합니다.", "commandsDecayDesc": "Display the decay time of an item.", "commandsDespawnDesc": "Display the despawn time of an item.", "commandsHelpCommandList": "명령어 리스트", "commandsHelpDesc": "도움말 메시지를 표시합니다.", "commandsHelpHowToCredentials": "자격 증명 가이드", "commandsHelpHowToPairServer": "서버 페어링 가이드", "commandsItemDesc": "Get the details of an item.", "commandsLeaderDesc": "팀 구성원에게 리더를 부여하거나/지급합니다.", "commandsLeaderMemberDesc": "팀 구성원들의 이름입니다.", "commandsMapAllDesc": "지역 이름과 표시가 모두 포함된 지도를 가져옵니다.", "commandsMapCleanDesc": "지도를 가져옵니다.", "commandsMapDesc": "현재 연결된 서버의 맵 이미지를 가져옵니다.", "commandsMapMarkersDesc": "표시를 포함한 지도를 가져옵니다.", "commandsMapMonumentsDesc": "각 지역 이름을 포함한 지도를 가져옵니다.", "commandsMarketDesc": "게임 내 운영되는 자동 판매기", "commandsMarketListDesc": "자동 판매기에서 구독한 아이템을 표시합니다.", "commandsMarketOrderDesc": "The order type.", "commandsMarketSearchDesc": "자동 판매기에서 아이템을 검색합니다.", "commandsMarketSubscribeDesc": "자동 판매기에서 아이템을 구독합니다.", "commandsMarketUnsubscribeDesc": "자동 판매기의 아이템 구독을 취소합니다.", "commandsPlayersBattlemetricsIdDesc": "The Battlemetrics ID of the server (default: The connected server).", "commandsPlayersDesc": "Battlemetrics에서 플레이어 정보를 가져옵니다.", "commandsPlayersNameDesc": "Search for a player on Battlemetrics based on player name.", "commandsPlayersPlayerIdDesc": "Search for a player on Battlemetrics based on player id.", "commandsPlayersPlayerIdPlayerIdDesc": "The player id of the player.", "commandsPlayersStatusDesc": "Search for players that are online/offline/any.", "commandsRecycleDesc": "Display the output of recycling an item.", "commandsRecycleQuantityDesc": "The quantity of items to recycle.", "commandsRecycleRecyclerTypeDesc": "The recycler type (recycler, shredder, safe-zone-recycler).", "commandsResearchDesc": "Display the cost to research an item.", "commandsResetAlarmsDesc": "알람 채널을 재설정 합니다.", "commandsResetDesc": "디스코드 채널을 재설정 합니다.", "commandsResetInformationDesc": "정보 채널을 재설정 합니다.", "commandsResetServersDesc": "서버 채널을 재설정 합니다.", "commandsResetSettingsDesc": "설정 채널을 재설정 합니다", "commandsResetStorageMonitorsDesc": "보관 측정장치의 채널을 재설정 합니다.", "commandsResetSwitchesDesc": "스위치/스위치 그룹채널을 재설정 합니다.", "commandsResetTrackersDesc": "추적기 채널을 재설정 합니다.", "commandsRoleClearDesc": "역할 지우기 (모든 사용자가 rustplusplus 카테고리 채널들을 볼 수 있도록 허용)", "commandsRoleDesc": "Rustplusplus 카테고리 및 채널 내용을 볼 수 있는 특정 역할을 설정/해제 합니다.", "commandsRoleSetDesc": "역할을 설정합니다.", "commandsRoleSetRoleDesc": "Rustplusplus 채널 역할을 볼 수 있습니다.", "commandsStackDesc": "Display the stack size of an item.", "commandsStoragemonitorDesc": "보관 측정장치에 대한 작업", "commandsStoragemonitorEditDesc": "보관 측정장치의 속성을 편집합니다.", "commandsStoragemonitorEditIdDesc": "보관 측정장치의 ID입니다.", "commandsStoragemonitorEditImageDesc": "보관 측정장치의 이미지를 설정합니다.", "commandsSwitchDesc": "스마트 스위치에 대한 작업.", "commandsSwitchEditDesc": "스마트 스위치의 속성을 편집합니다.", "commandsSwitchEditIdDesc": "스마트 스위치의 ID입니다.", "commandsSwitchEditImageDesc": "스마트 스위치의 이미지를 설정합니다.", "commandsUpkeepDesc": "Display the upkeep cost of an item.", "commandsUptimeBotDesc": "Display uptime of bot.", "commandsUptimeDesc": "Display uptime of the bot and server.", "commandsUptimeServerDesc": "Display uptime of server.", "commandsVoiceBotJoinedVoice": "<PERSON> Bot has joined the Voicechannel", "commandsVoiceBotLeftVoice": "The <PERSON><PERSON> has left the Voicechannel", "commandsVoiceDesc": "봇 음성 명령어", "commandsVoiceFemale": "여성", "commandsVoiceFemaleDescription": "음성 보이스를 여성으로 설정합니다.", "commandsVoiceGenderDesc": "음성 보이스를 남성으로 설정합니다.", "commandsVoiceJoin": "Joining voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceJoinDesc": "Joins the Voicechannel", "commandsVoiceLeave": "Leaving voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceLeaveDesc": "Leaves the Voicechannel", "commandsVoiceMale": "남성", "commandsVoiceMaleDescription": "Sets the voiceactor gender to Male", "commandsVoiceNotInVoice": "You are not in a voicechannel", "connect": "서버 아이피/포트", "connectCap": "연결", "connected": "Connected", "connectedCap": "연결됨", "connectedToServer": "서버에 연결됨.", "connectingCap": "연결중", "connectingToServer": "서버에 연결중...", "connectionEvents": "Connection Events", "connectionRefusedTo": "연결이 거부되었습니다: {id}.", "connectionsCap": "연결", "couldNotAddStepTracers": "Could not add step tracers.", "couldNotAppendMapMarkers": "지도에 표시를 추가할 수 없습니다.rustplus 정보 인스턴스가 설정되지 않았습니다.", "couldNotAppendMapMonuments": "지도에 지역표시를 추가할 수 없습니다. rustplus 정보 인스턴스가 설정되지 않앗습니다.", "couldNotAppendMapTracers": "지도에 추적기를 추가할 수 없습니다. rustplus 정보 오브젝트가 아직 설정되지 않았습니다.", "couldNotConnectTo": "{id} 에 연결할 수 없습니다.", "couldNotCreateCategory": "{name} 카테고리를 생성할 수 없습니다.", "couldNotCreateTextChannel": "{name} 텍스트 채널을 생성할 수 없습니다.", "couldNotDeferInteraction": "인스턴스를 수행할 수 없습니다..", "couldNotDeleteCategory": "Could not delete category: {categoryId}", "couldNotDeleteChannel": "Could not delete channel: {channelId}", "couldNotDeleteMessage": "{message} 메시지를 삭제할 수 없습니다.", "couldNotFindAnyPlayers": "플레이어를 찾을 수 없습니다.", "couldNotFindCategory": "{category} 카테고리를 찾을 수 없습니다.", "couldNotFindChannel": "{channel} 채널을 찾을 수 없습니다.", "couldNotFindCraftDetails": "Could not find craft details for {name}.", "couldNotFindDecayDetails": "Could not find decay details for {name}.", "couldNotFindDespawnDetails": "Could not find despawn details for {name}.", "couldNotFindGuild": "{guildId} 길드를 찾을 수 없습니다.", "couldNotFindLanguage": "{language} 언어를 찾을 수 없습니다.", "couldNotFindMessage": "{message} 메시지를 찾을 수 없습니다.", "couldNotFindPlayer": "{name} 플레이어를 찾을 수 없습니다.", "couldNotFindPlayerId": "Could not find player with id {id}.", "couldNotFindRecycleDetails": "Could not find recycle details for {name}.", "couldNotFindResearchDetails": "Could not find research details for {name}.", "couldNotFindRole": "{roleId} 역할을 찾을 수 없습니다.", "couldNotFindStackDetails": "Could not find stack details for {name}.", "couldNotFindTeammate": "{name} 팀원을 찾을 수 없습니다.", "couldNotFindUpkeepDetails": "Could not find upkeep details for {name}.", "couldNotFindUser": "{userId} 사용자 ID를 찾을 수 없습니다.", "couldNotGetChannelWithId": "{id} 인 채널을 가져올 수 없습니다.", "couldNotIdentifyMember": "{name} 팀원을 식별할 수 없습니다.", "couldNotPerformBulkDelete": "{channel} 채널에서 bulkDelete를 수행할 수 없습니다.", "couldNotPerformMessageDelete": "메시지를 삭제할 수 없습니다.", "couldNotPerformMessagesFetch": "{channel} 채널에서 메시지를 가져올 수 없습니다.", "couldNotRegisterSlashCommands": "{guildId} 길드에 슬래시 명령어를 등록할 수 없습니다. ", "couldNotSetParent": "{channelId} 채널에 대해 상위 채널을 설정할 수 없습니다.", "craft": "Craft", "crate": "상자", "createGroupCap": "스위치 그룹 만들기", "createTrackerCap": "추적기 만들기", "credentialsAddedSuccessfully": "{steamId}에 대한 FCM 자격 증명이 추가되었습니다.", "credentialsAlreadyRegistered": "steamId에 대한 FCM 자격 증명: {steamId}가 이미 등록되었습니다.", "credentialsCannotStartLiteAlreadyHoster": "steamId에 대한 FCM 자격 증명을 시작할 수 없습니다: {steamId} 이미 등록됨.", "credentialsDoNotExist": "steamId에 대한 FCM 자격 증명: {steamId}가 없습니다.", "credentialsHosterNotSetForGuild": "길드 {id}에 FCM 자격 증명 호스트가 설정되지 않았습니다.호스트를 설정하세요.", "credentialsNotRegistered": "steamId에 대한 FCM 자격 증명: {steamId}가 등록되지 않았습니다.", "credentialsNotRegisteredForGuild": "길드에 FCM 자격 증명이 등록되지 않았습니다: {id}, FCM-Listener를 시작할 수 없습니다.", "credentialsRemovedSuccessfully": "steamId에 대한 FCM 자격 증명: {steamId}가 제거되었습니다.", "credentialsSetHosterSuccessfully": "{steamId}가 FCM 자격 증명 호스트로 설정되었습니다.", "currencySign": "통화 기호", "currentCommandDelay": "명령어 지연시간: {delay}초", "currentItemHp": "The current HP of the item.", "currentPrefixPlaceholder": "시작 명령어: {prefix}", "customCommand": "사용자 지정 명령어", "customTimerEditCargoShipEgressLabel": "화물선 탈출 시간 (초단위):", "customTimerEditCrateOilRigUnlockLabel": "석유 굴착지 잠금 상자 잠금 해제 시간 (초단위):", "customTimerEditDesc": "사용자 지정 타이머 편집", "customTimersCap": "사용자 지정 타이머", "dash": "대시", "dayOfWipe": "{day} 일", "deathCap": "사망", "decay": "Decay", "decayTimeForItem": "Decay time for {item} is {time}.", "decayingCap": "부식 중", "deleteUnreachableDevicesCap": "DELETE UNREACHABLE DEVICES", "despawnTime": "Despawn Time", "despawnTimeOfItem": "Despawn time of {item} is {time}.", "deviceIsAlreadyOnOff": "{device} is already {status}.", "deviceIsCurrentlyOnOff": "{device} 은(는) 현재 {status} 입니다.", "deviceWasTurnedOnOff": "{device} 이(가) {status} 으로 설정되었습니다.", "disabledCap": "비활성화", "discoFloor": "디스코 바닥", "disconnectCap": "연결 해제", "disconnected": "Disconnected", "disconnectedCap": "연결 끊김", "disconnectedFromServer": "서버와의 연결이 끊어졌습니다.", "discordCap": "디스코드", "discordUsers": "Discord Users", "displayInformationBattlemetricsAllOnlinePlayers": "Should all online players from Battlemetrics be displayed in the information channel?", "displayingMap": "{mapName} 맵을 표시하는 중입니다.", "displayingOnlinePlayers": "온라인 플레이어를 표시합니다.", "distanceDirectionGrid": "{distance}m 방향 {direction}° [{grid}]", "doorController": "문 제어기", "dot": "점", "eastOfGrid": "지도의 동쪽", "editCap": "수정", "editing": "Editing", "editingOf": "Editing of {entity}", "egressInTime": "Egress in {time} at {location}.", "eight": "8", "elevator": "엘리베이터", "empty": "비어있음", "enabledCap": "활성화", "entityId": "Entity ID", "equalsSign": "등호", "errorCap": "에러", "errorExecutingCommand": "이 명령어를 실행하는 동안 오류가 발생했습니다!", "eventCap": "이벤트", "eventInfo": "이벤트 정보", "exclamationMark": "느낌표", "failedToScrapeProfileName": "프로필 이름을 가져오지 못했습니다: {link}.", "failedToScrapeProfilePicture": "프로필 사진을 가져오지 못했습니다: {link}.", "fcmCredentials": "FCM 자격 증명", "fcmListenerStartHost": "FCM-수신기:호스트(봇 소유자)가 5초내로 로드 됩니다. 길드아이디: {guildId}, 스팀아이디: {steamId}.", "fcmListenerStartLite": "FCM-수신기:FCM 자격 증명에 저장된 플레이어가 5초내로 로드 됩니다. 길드아이디: {guildId}, 스팀아이디: {steamId}.", "ferryTerminal": "여객선 터미널", "fishingVillage": "어촌", "five": "5", "four": "4", "giantExcavatorPit": "거대 굴착기", "greaterThanSign": "부등식", "groupAddSwitchDesc": "{group}에 스위치 추가", "groupRemoveSwitchDesc": "{group}에서 스위치 제거", "harbor": "항구", "hasBeenAliveLongest": "{name}이(가) 가장 오래 생존했습니다({time}).", "hash": "해시 기호", "hbhfSensor": "HBHF 센서", "heart": "하트", "heater": "히터", "heavyScientistCalledSetting": "석유 굴착지에 헤비 과학자를 호출하면, 알림을 보내줍니다.", "heavyScientistsCalledLarge": "헤비 과학자들이 {location}에 있는 대형 석유 굴착지로 호출되었습니다.", "heavyScientistsCalledSmall": "헤비 과학자들이 {location}에 있는 소형 석유 굴착지로 호출되었습니다.", "hideTrademark": "트레이드마크 숨기기", "hoster": "호스트", "hp": "HP", "hpExceedMax": "Hp {hp} is exceeding max of {max}.", "hqmQuarry": "고품질 금속 채석장", "ignoreSetAvatar": "프로필 설정", "ignoreSetNickname": "닉네임 설정", "ignoreSetUsername": "사용자 이름 설정", "inGameBotMessagesMuted": "게임 내 봇 메시지가 음소거되었습니다.", "inGameBotMessagesUnmuted": "게임 내 봇 메시지가 음소거해제 되었습니다.", "inGameCap": "인-게임", "inGameEventInfo": "게임 내 이벤트 정보 알림", "inGameTeamNotificationsSetting": "게임 내 팀 동료 알림", "inGameTime": "게임 내 시간: {time}.", "index": "Index", "infoCap": "정보", "inside": "Inside", "interactionEditReplyFailed": "상호 작용 편집 응답 실패: {error}", "interactionInvalidChannel": "잘못된 채널의 상호 작용입니다.", "interactionReplyFailed": "상호 작용 응답 실패: {error}", "interactionUpdateFailed": "상호 작용 업데이트 실패: {error}", "invalidBattlemetricsId": "Invalid Battlemetrics ID.", "invalidGuildOrChannel": "길드 또는 채널이 잘못되었습니다.", "invalidHpInterval": "Invalid HP interval {hp}.", "invalidId": "잘못된 ID: {id}.", "invalidStructureType": "Invalid Structure type {type}.", "invalidSubcommand": "잘못된 하위 명령입니다.", "invalidTimeDistance": "잘못된 시간 타입: {distance}, 이전 시간: {prevTime}, 새로운 시간: {newTime}", "isDecaying": "{device}가 부식되고 있습니다!", "isNoLongerConnected": "{device}이(가) 더 이상 연결되어 있지 않습니다!", "item": "아이템", "itemAvailableInVendingMachine": "{items}이(가) [{location}]에 있는 자동 판매기에서 발견 되었습니다.", "itemAvailableNotifyInGameSetting": "자동 판매기에서 구독 목록의 항목이 발견 되면 게임 내에 알림을 보내시겠습니까?", "junkyard": "폐차장", "justSubscribedToItem": "{name}을 구독했습니다.", "languageCode": "언어 코드: {code}", "languageLangNotSupported": "{language} 언어는 지원되지 않습니다.", "languageNotSupported": "언어가 지원되지 않습니다.", "largeBarn": "큰 헛간", "largeFishingVillage": "대형 어촌", "largeOilRig": "대형 석유 굴착지", "largeWoodBox": "큰 나무 상자", "lastTrigger": "Last Trigger", "launchSite": "발사장", "leaderAlreadyLeader": "{name}은(는) 이미 팀 리더입니다.", "leaderCommandIsDisabled": "리더 명령어는 설정에서 비활성화 되었습니다.", "leaderCommandOnlyWorks": "리더 명령어는 현재 리더가 {name}인 경우에만 작동합니다.", "leaderTransferred": "팀 리더는 {name}(으)로 변경되었습니다.", "leavingMapAt": "{location} 에서 떠나가고 있습니다.", "lessThanSign": "부등식", "lighthouse": "등대", "linkCap": "링크", "location": "위치", "lockedCrateLargeOilRigUnlocked": "{location}의 대형 석유 굴착지에서 잠긴 상자가 잠금 해제되었습니다.", "lockedCrateOilRigUnlockedSetting": "석유 굴착지에서 잠긴 상자가 잠금 해제되면 알림을 보냅니다.", "lockedCrateSmallOilRigUnlocked": "{location}에 있는 소형 굴착지의 잠긴 상자가 잠금 해제되었습니다.", "logDiscordCommand": "Discord Command - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logDiscordMessage": "Discord Message - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logInGameCommand": "{type} - Command: {command}, User: {user}.", "logInGameMessage": "Message: {message}, User: {user}", "logSmartSwitchGroupValueChange": "Smart Switch Group - Value: {value}.", "logSmartSwitchValueChange": "Smart Switch - Value: {value}.", "loggedInAs": "로그인: {name}", "makeSureApplicationsCommandsEnabled": "초대 URL을 생성할 때 applications.commands가 선택되어 있는지 확인합니다.", "map": "지도", "mapSalt": "지도 솔트", "mapSeed": "지도 시드", "mapSize": "지도 크기", "mapWipeDetectedNotifySetting": "맵 초기화가 감지되면 {group}에게 알릴까요?", "markerAdded": "[{location}]에 {name} 마커가 추가되었습니다.", "markerDoesNotExist": "{name} 마커가 없습니다.", "markerLocation": "[{location}]의 마커 {name} 이(가) {direction}° 방향의 {player}에서 {distance}m 떨어져 있습니다.", "markerRemoved": "[{location}]에 있는 {name} 마커가 제거되었습니다.", "message": "메시지", "messageCap": "MESSAGE", "messageDeletedIn30": "이 메시지는 30초 후에 삭제됩니다.", "messageEditFailed": "메시지 편집 실패: {error}", "messageReplyFailed": "메시지 응답 실패: {error}", "messageSendFailed": "메시지 전송 실패: {error}", "messageWasSent": "메시지가 전송되었습니다.", "militaryTunnel": "군사 터널", "miningOutpost": "광산 전초 기지", "missileSilo": "미사일 격납고", "missingArguments": "인수가 없습니다.", "missingPermission": "이 작업을 수행할 수 있는 권한이 없습니다.", "missingTimerMessage": "타이머 메시지가 없습니다.", "modalValueChange": "Modal Interaction - VerifyId: {id}, Value: {value}.", "more": "더", "morePlayers": "{players}명 뒤에 {number}명이 더 있습니다.", "mutedCap": "비활성화", "name": "이름", "nameChangeHistory": "이름 변경 내역", "new": "New", "newVendingMachine": "새로운 자동 판매기가 {location}에 생성되었습니다.", "newsCap": "새로운", "noActiveTimers": "활성화된 타이머가 없습니다.", "noCommandDelay": "명령어 지연이 없습니다.", "noCommunicationSmartSwitch": "스마트 스위치와 통신할 수 없습니다: {name}", "noData": "데이터 없음", "noDataOnLargeOilRig": "대형 굴착지에 대한 데이터가 없습니다.", "noDataOnSmallOilRig": "소형 굴착지에 대한 데이터가 없습니다.", "noDelayCap": "딜레이 없음", "noItemFound": "자동 판매기에서 항목을 찾을 수 없습니다...", "noItemWithIdFound": "아이디가 {id} 인 항목을 찾을 수 없습니다.", "noItemWithNameFound": "이름이 {name} 인 항목을 찾을 수 없습니다.", "noNameIdGiven": "'name' 또는 'id'가 지정되지 않았습니다.", "noOneIsAfk": "자리비움 상태인 플레이어가 없습니다.", "noOneIsOffline": "오프라인 플레이어가 없습니다.", "noOneIsOnline": "온라인 플레이어가 없습니다.", "noRegisteredConnectionEvents": "등록된 연결 이벤트가 없습니다.", "noRegisteredConnectionEventsUser": "{user}의 등록된 연결 이벤트가 없습니다.", "noRegisteredDeathEvents": "등록된 사망 이벤트가 없습니다.", "noRegisteredDeathEventsUser": "{user}의 등록된 사망 이벤트가 없습니다.", "noRegisteredEvents": "No registered events yet.", "noRegisteredMarkers": "등록된 마커가 없습니다.", "noSavedNotes": "저장된 노트가 없습니다.", "noToolCupboardWereFound": "도구함을 찾을 수 없습니다.", "none": "없음", "northEast": "북동쪽", "northOfGrid": "지도의 북쪽", "northWest": "북서쪽", "notAValidOrderType": "{order} is not a valid order type.", "notActive": "활성화되지 않았습니다.", "notConnectedToRustServer": "현재 러스트 서버에 연결되어 있지 않습니다..", "notExistInSubscription": "{name}항목이 구독 목록에 없습니다.", "notFoundCap": "찾을 수 없음", "notPartOfRole": "{role} 역할에 속하지 않으므로 봇 명령을 실행할 수 없습니다.", "notShowingCap": "표시되지 않음", "noteCap": "노트", "noteIdDoesNotExist": "노트 아이디: {id} 이(가) 없습니다.", "noteIdInvalid": "노트 아이디가 잘못되었습니다.", "noteIdWasRemoved": "노트 아이디: {id} 이(가) 제거되었습니다.", "noteSaved": "노트가 저장되었습니다.", "offCap": "꺼짐", "offline": "Offline", "offlineTime": "Offline time", "oilRig": "석유 굴착지", "old": "Old", "onCap": "켜짐", "one": "1", "online": "Online", "onlineTime": "Online time", "onlyOneInTeam": "당신은 팀의 유일한 팀원입니다.", "outpost": "전초기지", "outside": "Outside", "oxumsGasStation": "오슘 주유소", "pairing": "페어링", "patrolHelicopter": "공격 헬기", "patrolHelicopterDestroyedSetting": "공격 헬기가 파괴되면 알림을 보내줍니다.", "patrolHelicopterDetectedSetting": "공격 헬기가 감지되면 알림을 보내줍니다.", "patrolHelicopterEntersMap": "공격 헬기가 {location}에서 진입합니다.", "patrolHelicopterLeftMap": "공격 헬기가 방금 {location}에서 지도를 떠났습니다.", "patrolHelicopterLeftSetting": "공격 헬기가 지도를 벗어나면, 알림을 보내줍니다.", "patrolHelicopterLocatedAt": "공격 헬기는 현재 {location}에 있습니다.", "patrolHelicopterNotCurrentlyOnMap": "공격 헬기는 현재 지도에서 발견되지 않았습니다.", "patrolHelicopterTakenDown": "공격 헬기가 {location}에서 격추되었습니다.", "percentSign": "백분율", "pipe": "수직선", "playerHasBeenAliveFor": "{name}이(가) {time}동안 살아있는 상태입니다.", "playerId": "Player ID", "playerJoinedTheTeam": "{name}이(가) 팀에 가입했습니다.", "playerJustConnected": "{name}이(가) 방금 연결되었습니다.", "playerJustConnectedTo": "{name}이(가) 방금 {server}에 연결되었습니다.", "playerJustConnectedTracker": "{tracker} 에 있는 {name} 이 온라인 상태가 되었습니다.", "playerJustDied": "{name}이(가) {location}에서 방금 사망했습니다.", "playerJustDisconnected": "{name}의 연결이 끊겼습니다.", "playerJustDisconnectedFrom": "{name}이(가) {server}에서 연결이 끊겼습니다.", "playerJustDisconnectedTracker": "{tracker} 에 있는 {name} 이 오프라인 상태가 되었습니다.", "playerJustReturned": "{name}이(가) 방금 온라인 상태가 되었습니다 ({time}).", "playerJustWentAfk": "{name}이(가) 방금 자리비움 상태가 되었습니다.", "playerLeftTheTeam": "{name}이(가) 팀을 떠났습니다.", "playerNotPairedWithServer": "{name}이(가) 페어링 되지 않아서 리더 명령이 작동하지 않습니다.", "players": "플레이어", "playersSearch": "Players Search", "plusSign": "더하기", "populationPlayers": "서버인원: ({current}/{max}) 플레이어.", "populationQueue": "{number}명의 플레이어가 대기열에 있습니다.", "powerPlant": "발전소", "profile": "Profile", "proxLocation": "{name}은(는) {caller}에서 {direction}° [{location}]방향으로 {distance}m 떨어져 있습니다.", "quantity": "수량", "questionMark": "물음표", "ranch": "농장", "ratelimited": "속도제한", "reconnectingCap": "재연결중", "reconnectingToServer": "서버에 다시 연결하는 중...", "recycle": "Recycle", "recycleCap": "재활용", "recycler": "Recycler", "remain": "레프트", "removePlayerCap": "플레이어 제거", "removeSwitchCap": "스위치 제거", "removedSubscribeItem": "{name} 항목이 구독에서 제거되었습니다.", "research": "Research", "researchTable": "Research Table", "resetSuccess": "디스코드를 성공적으로 재설정했습니다.", "responseContainError": "응답에 값이 {error} 인 오류 속성이 포함되어 있습니다.", "responseIsEmpty": "응답이 비어 있습니다.", "responseIsUndefined": "응답이 정의되지 않았습니다.", "responseTimeout": "응답을 기다리는 동안 시간이 초과되었습니다.", "resultRecycling": "재활용 결과", "roleCleared": "rustPlusPlus 사용권한을 가진 역할이 초기화 되었습니다.", "roleSet": "rustPlusPlus 사용권한을 가진 역할이 {name} 으로 설정되었습니다.", "rustMonument": "러스트 파밍지역", "rustplusOperational": "RUSTPLUS 작동.", "safe-zone-recycler": "Safe Zone Recycler", "samsite": "지대공 미사일 발사대", "satelliteDish": "위성 안테나", "scrap": "Scrap", "searchResult": "항목 검색 결과: **{name}**", "second": "{second}초", "secondCommandDelay": "{second}초의 명령 지연", "seconds": "{seconds}초", "secondsCommandDelay": "{seconds}초의 명령어 지연", "selectInGamePrefixSetting": "게임 내 시작 명령어 선택:", "selectLanguageExtendSetting": "**/reset discord**를 실행하여 새로 설정된 언어를 로드하십시오.", "selectLanguageSetting": "봇에서 사용할 언어 선택:", "selectMenuValueChange": "Select Menu Interaction - VerifyId: {id}, Value: {value}.", "selectTrademarkSetting": "모든 게임 내 메시지에 표시할 상표를 선택합니다.", "sell": "sell", "semicolon": "세미콜론", "sentTextToSpeech": "TTS를 보냅니다.", "server": "서버", "serverId": "Server ID", "serverInfo": "서버 정보", "serverInvalid": "서버에 대한 연결이 잘못된 것 같습니다. 서버에 다시 연결해 보십시오.", "serverJustOffline": "서버가 방금 오프라인 상태가 되었습니다.", "serverJustOnline": "서버가 방금 온라인 상태가 되었습니다.", "serverStatus": "Server Status", "serviceUnavailable": "서비스를 사용할 수 없음: {error}", "setBotLanguage": "봇 언어를 {language} (으)로 설정합니다.", "seven": "7", "sewerBranch": "하수 분기점", "shouldBotBeMutedSetting": "게임 내에서 봇을 음소거 하시겠습니까?", "shouldCommandsEnabledSetting": "게임 내 명령어를 활성화 하시겠습니까?", "shouldLeaderCommandEnabledSetting": "리더 명령어를 활성화 하시겠습니까?", "shouldLeaderCommandOnlyForPairedSetting": "리더 명령어 권한을 서버와 페어링된 사람들에게 제공하시겠습니까?\n(FCM자격증명에 등록되어 있어야 합니다.) ", "shouldSmartAlarmNotifyNotConnectedSetting": "스마트 알람을 러스트 서버에서 오프라인 이여도 알림을 보내시겠습니까?", "shouldSmartAlarmsNotifyInGameSetting": "스마트 알람을 게임 내 채팅으로 활성화 하시겠습니까?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Should Smart Switches and Smart Switch Groups notify In-Game when they are changed from discord?", "showingBlacklist": "Showing the blacklist.", "showingSubscriptionList": "구독 목록을 표시합니다.", "shredder": "Shredder", "sirenLight": "사이렌 조명", "six": "6", "slash": "빗금", "slashCommandInteraction": "Slash Command Interaction - Guild: {guild}, Channel: {channel}, User: {user}, Command: {command}, VerifyId: {id}.", "slashCommandValueChange": "Slash Command Interaction - VerifyId: {id}, Value: {value}.", "slashCommandsSuccessRegister": "길드에 대한 응용 프로그램 명령을 등록했습니다. {guildId}.", "slots": "슬롯", "smallOilRig": "소형 석유 굴착지", "smartAlarm": "스마트 알람", "smartAlarmEditSuccess": "스마트 알람 {name} 을(를) 수정했습니다.", "smartAlarmNotifyExtendSetting": "- 이러한 알람 알림은 게임 내 스마트 알람에 주어진 제목과 메시지를 사용합니다.\n- 이러한 스마트 경보는 디스코드 알람 채널에서 사용할 수 없습니다.", "smartDeviceNotFound": "{device}을(를) 찾을 수 없습니다! 제거되었거나 {user} 이(가) 디바이스에 액세스할 수 없습니다.", "smartSwitch": "스마트 스위치", "smartSwitchAutoDay": "스마트 스위치는 낮에만 활성화됩니다.", "smartSwitchAutoNight": "스마트 스위치는 밤에만 활성화됩니다.", "smartSwitchAutoOff": "Smart Switch will automatically go inactive during update cycle.", "smartSwitchAutoOffAnyOnline": "Smart Switch will automatically go inactive if any teammate is online.", "smartSwitchAutoOffProximity": "Smart Switch will automatically go inactive if teammate is in proximity.", "smartSwitchAutoOn": "Smart Switch will automatically go active during update cycle.", "smartSwitchAutoOnAnyOnline": "Smart Switch will automatically go active if any teammate is online.", "smartSwitchAutoOnProximity": "Smart Switch will automatically go active if teammate is in proximity.", "smartSwitchEditProximityLabel": "Proximity Setting (meters):", "smartSwitchEditSuccess": "스마트 스위치 {name} 을(를) 수정했습니다.", "smartSwitchNormal": "스마트 스위치는 정상적으로 작동합니다.", "smilyFace": "스마일 이모티콘", "somethingWrongWithConnection": "연결에 문제가 발생했습니다.", "southEast": "남동쪽", "southOfGrid": "지도의 남쪽", "southWest": "남서쪽", "sprinkler": "스프링쿨러", "stackSize": "Stack Size", "stackSizeOfItem": "Stack size of {item} is {quantity}x.", "status": "상태", "statusNotConnectedToServer": "**상태** `서버에 연결되지 않았습니다!`", "statusNotElectronicallyConnected": "**상태** `전기가 연결되지 않았습니다!`", "statusNotFound": "**상태**: 찾을 수 없음", "steamId": "SteamID", "stoneQuarry": "돌 채석장", "storageMonitor": "보관 측정장치", "storageMonitorEditSuccess": "보관 측정장치 {name}을(를) 수정했습니다.", "streamerMode": "Streamer Mode", "subscribeToChangesBattlemetrics": "Subscribe to different changes on Battlemetrics.", "subscriptionList": "구독 목록", "subscriptionListEmpty": "아이템 구독 목록이 비어 있습니다.", "sulfurQuarry": "유황 채석장", "switches": "스위치", "teamMember": "팀 맴버", "teamMemberInfo": "팀 맴버 정보", "theDome": "돔", "theIdOfTheItem": "The id of the item.", "theNameOfTheItem": "The name of the item.", "theNameOfThePlayer": "The name of the player.", "three": "3", "tilde": "물결표", "time": "시간", "timeBeforeCargoEntersEgress": "{location} 에 있는 화물선이 탈출 단계에 들어가기 전까지 {time} 남았습니다.", "timeBeforeCrateAtLargeOilRigUnlocks": "대형 석유 굴착지({location})에서 잠긴 상자가 잠금 해제되기 전까지 {time} 남았습니다", "timeBeforeCrateAtSmallOilRigUnlocks": "소형 석유 굴착지({location})에서 잠긴 상자가 잠금 해제되기 전까지 {time} 남았습니다.", "timeCap": "시간", "timeFormatInvalid": "시간 형식이 잘못되었습니다.", "timeLeftTimer": "{id}: 남은 시간: {time}, 메시지: {message}", "timeSinceAlarmWasTriggered": "The alarm {alarm} was triggered {time} ago.", "timeSinceCargoLeft": "화물선이 지도에서 떠난 이후 {time} 지났습니다.", "timeSinceChinook47OnMap": "치누크 47이 지도에서 발견된 이후 {time} 지났습니다.", "timeSinceHeavyScientistsOnLarge": "대형 석유 굴착지에서 헤비 과학자들을 부른 이후 {time} 지났습니다.", "timeSinceHeavyScientistsOnSmall": "소형 석유 굴착지에서 헤비 과학자들을 부른 이후 {time} 지났습니다.", "timeSinceLast": "마지막 이후 {time} 지났습니다.", "timeSinceLastEvent": "마지막 이벤트 이후 {time} 지났습니다.", "timeSinceLastSinceDestroyedLong": "{time1} since the last Patrol Helicopter was on the map, {time2} since it last got downed{location}.", "timeSinceLastSinceDestroyedShort": "{time1} since last.\n{time2} since destroyed{location}.", "timeSincePatrolHelicopterWasOnMap": "공격 헬기가 지도에 표시된 이후 {time} 지났습니다.", "timeSinceTravelingVendorWasOnMap": "{time} since the Traveling Vendor was on the map.", "timeSinceWipe": "초기화 후 {time} 지났습니다.", "timeTill": "{event}:남은시간", "timeTillDaylight": "해가 뜨기 전까지 {time} 남았습니다.", "timeTillNightfall": "해가 지기 전까지 {time} 남았습니다.", "timeTillStructureDecay": "{time} before {type} wall decay.", "timeUntilUnlocksAt": "{location}에서 잠금 해제될 때까지 {time} 남았습니다.", "timer": "타이머: {message}", "timerIdDoesNotExist": "타이머 아이디: {id} 이(가) 없습니다.", "timerIdInvalid": "타이머 아이디가 잘못되었습니다.", "timerRemoved": "타이머 아이디: {id} 이(가) 제거되었습니다.", "timerSet": "{time} 으로 타이머가 설정되었습니다.", "tokensDidNotReplenish": "토큰이 제 시간에 입력되지 않았습니다.", "toolCupboard": "도구함", "total": "Total", "tracker": "Tracker", "trackerAddPlayerDesc": "{tracker} 에 플레이어 추가", "trackerRemovePlayerDesc": "{tracker} 에서 플레이어 제거", "trademarkShownBeforeMessage": "메시지 앞에 {trademark}가 표시됩니다.", "trainYard": "기차 차고지", "travelingVendor": "Traveling Vendor", "travelingVendorDetectedSetting": "When the Traveling Vendor is detected, send a notification.", "travelingVendorHaltedAt": "The Traveling Vendor stopped at {location}.", "travelingVendorHaltedSetting": "When the Traveling Vendor stops moving, send a notification.", "travelingVendorLeftSetting": "When the Traveling Vendor left the map, send a notification.", "travelingVendorLocatedAt": "The Traveling Vendor is located at {location}.", "travelingVendorLeftMap": "The Traveling Vendor just left the map at {location}.", "travelingVendorNotCurrentlyOnMap": "The Traveling Vendor is not currently on the map.", "travelingVendorResumedAt": "The Traveling Vendor resumed moving at {location}.", "travelingVendorSpawnedAt": "The Traveling Vendor spawned at {location}.", "turnOffCap": "끄기", "turnOnCap": "켜기", "turningGroupOnOff": "스위치 {group} {status} 전환", "two": "2", "type": "타입", "unavailable": "사용할 수 없습니다", "underscore": "밑줄", "underwater": "Underwater", "underwaterLab": "수중 연구실", "unhandledRejection": "처리되지 않은 거부: {error}", "unknown": "알 수 없는", "unknownInteraction": "알 수 없는 상호 작용...", "unmutedCap": "활성화", "updateCap": "UPDATE", "upkeep": "유지 시간", "upkeepForItem": "Upkeep for {item} is {cost}.", "userAddedToBlacklist": "{user} was added to blacklist.", "userAlreadyInBlacklist": "{user} already in blacklist.", "userButtonInteraction": "Button Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Button Interaction - VerifyId: {id} SUCCESS", "userJustConnected": "{name} 이(가) 방금 연결되었습니다.", "userModalInteraction": "Modal Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Modal Interaction - VerifyId: {id} SUCCESS", "userNotInBlacklist": "{user} not in blacklist.", "userNotRegistered": "{user} 이(가) 등록되지 않았습니다.", "userPartOfBlacklist": "VerifyId: {id}, {user} is part of the blacklist.", "userPartOfBlacklistDiscord": "Blacklisted User! Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "userPartOfBlacklistInGame": "Blacklisted User! User: {user}, Message: {message}.", "userRemovedFromBlacklist": "{user} was removed from blacklist.", "userSaid": "{user} 대화, {text}", "userSelectMenuInteraction": "Select Menu Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Select Menu Interaction - VerifyId: {id} SUCCESS", "userTurnedOnOffSmartSwitchFromDiscord": "{user} turned Smart Switch {name} {status} from discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} turned Smart Switch Group {name} {status} from discord.", "value": "Value", "vendingMachine": "자동 판매기", "vendingMachineDetectedSetting": "새 자동 판매기가 감지되면 알림을 보냅니다.", "voiceCap": "음성", "warningCap": "경고", "waterTreatmentPlant": "정수 처리장", "websiteCap": "웹 사이트", "websocketClosedBeforeConnection": "연결이 설정되기 전에 WebSocket이 닫혔습니다.", "westOfGrid": "지도 서쪽", "wipe": "초기화", "wipeDetected": "서버 초기화가 감지되었습니다!", "yield": "Yield", "youAreAlreadyLeader": "당신은 이미 팀 리더 입니다.", "youAreNotPairedWithServer": "서버와 페어링되지 않았기 때문에 리더 명령어가 작동하지 않습니다."}