/*
    Copyright (C) 2022 <PERSON> (alexemanuelol)

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

    https://github.com/alexemanuelol/rustplusplus

*/

const DiscordTools = require('../discordTools/discordTools.js');
const PermissionHandler = require('../handlers/permissionHandler.js');

module.exports = async (client, guild) => {
    const instance = client.getInstance(guild.id);

    let category = undefined;
    if (instance.channelId.category !== null) {
        category = DiscordTools.getCategoryById(guild.id, instance.channelId.category);
    }
    if (category === undefined) {
        category = await DiscordTools.addCategory(guild.id, 'rustplusplus');
        if (category) {
            instance.channelId.category = category.id;
            client.setInstance(guild.id, instance);
        } else {
            client.log(client.intlGet(null, 'errorCap'),
                client.intlGet(null, 'couldNotCreateCategory', { name: 'rustplusplus' }), 'error');
            return null;
        }
    }

    if (category) {
        const perms = PermissionHandler.getPermissionsReset(client, guild, false);

        try {
            await category.permissionOverwrites.set(perms);
        }
        catch (e) {
            /* Ignore */
        }
    }

    return category;
};