{"24HoursInGameTimePassed": "Прошло 24 часа игрового времени.", "abandonedCabins": "Заброшенные хижины", "abandonedMilitaryBase": "Заброшенная военная база", "abandonedSupermarket": "Супермаркет", "addPlayerCap": "ДОБАВИТЬ ИГРОКА", "addSwitchCap": "ДОБАВИТЬ ПЕРЕКЛЮЧАТЕЛЬ", "afkCap": "AFK", "airfield": "Аэропорт", "alarmHaveNotBeenTriggeredYet": "Сигнализация {alarm} ещё не срабатывала.", "alias": "Псевдоним", "aliasAlreadyExist": "Псевдоним уже существует", "aliasIndexCouldNotBeFound": "Индекс псевдонимов не найден.", "aliasWasAdded": "Псевдоним добавлен.", "aliasWasRemoved": "Псевдоним удален.", "aliases": "Псевдонимы", "all": "все", "allTeammatesAreDead": "Все ваши товарищи по команде мертвы.", "alreadySubscribedToItem": "Подписка на предмет ({name}) уже оформлена.", "ampersand": "Ам<PERSON>е<PERSON><PERSON><PERSON><PERSON>д", "andMorePlayers": "... и еще {number} игроков.", "any": "Любой", "apostrophe": "Апостроф", "arcticResearchBase": "Арктическая научная станция", "asterisk": "Звездочка", "asteriskCctvDesc": "* означает, что вам нужен код, отличающийся для каждой карты", "atLocation": "На {location}.", "atSign": "Собака", "autoDayCap": "АВТО-ДЕНЬ", "autoNightCap": "АВТО-НОЧЬ", "autoOffAnyOnlineCap": "АВТО-ВЫКЛ-ЛЮБОЙ-ОНЛАЙН", "autoOffCap": "АВТО-ВЫКЛ", "autoOffProximityCap": "АВТО-ВЫКЛ-ДИСТАНЦИЯ", "autoOnAnyOnlineCap": "АВТО-ВКЛ-ЛЮБОЙ-ОНЛАЙН", "autoOnCap": "АВТО-ВКЛ", "autoOnProximityCap": "АВТО-ВКЛ-ДИСТАНЦИЯ", "autoSettingCap": "Автоматическая настройка: ", "automaticallyTurnBackOnOff": " Автоматически ({status}) через {time}.", "automaticallyTurningBackOnOff": "Группа ({device}) автоматически ({status}).", "autoturret": "Турель", "badGateway": "Bad Gateway: {error}", "banditCamp": "<PERSON>а<PERSON><PERSON><PERSON>ь бандитов", "baseIsUnderAttack": "Ваша база атакована!", "battlemetricsApiRequestFailed": "Запрос Battlemetrics не удался: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Не удалось обновить Battlemetrics Server {server}.", "battlemetricsGlobalLoginCap": "ПОДКЛЮЧЕНИЕ К СЕРВЕРУ", "battlemetricsGlobalLogoutCap": "ОТКЛЮЧЕНИЕ ОТ СЕРВЕРА", "battlemetricsGlobalNameChangesCap": "СМЕНА НИКА (ГЛОБАЛ)", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "Экземпляр Battlemetrics отсутствует идентификатор и имя.", "battlemetricsInstanceCouldNotBeFound": "Экземпляр Battlemetrics для {id} не найден.", "battlemetricsOnlinePlayers": "Онл<PERSON><PERSON>н игроки (Battlemetrics)", "battlemetricsPlayersLogin": "Игроки зашедшие на сервер (Battlemetrics)", "battlemetricsPlayersLogout": "Игроки покинувшие сервер (Battlemetrics)", "battlemetricsPlayersNameChanged": "Изменено имя игрока (Battlemetrics)", "battlemetricsServerNameChanged": "Имя сервера изменено (Battlemetrics) ", "battlemetricsServerNameChangesCap": "ИЗМЕНЕНИЯ НАЗВАНИЯ СЕРВЕРА", "battlemetricsTrackerNameChangesCap": "ИЗМЕНЕНИЯ НАЗВАНИЯ ТРЕКЕРА", "battlemetricsTrackerPlayerNameChanged": "Изменено имя отслеживаемого игрока (Battlemetrics) ", "blacklist": "Черный Список", "boomBox": "Магнитофон", "bot": "бот", "broadcaster": "Радиопередатчик", "buttonValueChange": "Взаимодействие кнопки - VerifyId: {id}, Значение: {value}.", "buy": "купить", "calculated": "Рассчитано", "cargoAt": "В {location}.", "cargoLeavingMapAt": "Грузовой корабль покидает карту в {location}.", "cargoLocatedAt": "Грузовой корабль находится в {location}.", "cargoNotCurrentlyOnMap": "Грузовой корабль в настоящее время не на карте.", "cargoShipDetectedSetting": "Уведомление при обнаружении Грузового корабля.", "cargoShipDockingAtHarbor": "Грузовой корабль только что прибыл в порт на {location}", "cargoShipDockingAtHarborSetting": "Уведомление когда грузовой корабль прибудет в порт.", "cargoShipEgressSetting": "Уведомление когда Грузовой корабль начнёт уплывать.", "cargoShipEntersEgressStage": "Грузовой корабль ({location}) начинает покидать карту.", "cargoShipEntersMap": "Грузовой корабль появился на карте ({location}).", "cargoShipLeftHarbor": "Грузовой корабль только что покинул порт в {location}", "cargoShipLeftMap": "Грузовой корабль покинул карту ({location}).", "cargoShipLeftSetting": "Уведомление когда Грузовой корабль покинет карту.", "cargoShipLocated": "Грузовой корабль расположен ({location}).", "cargoship": "Грузовой корабль", "ceilingLight": "Потолочный светильник", "channelNameActivity": "активность", "channelNameAlarms": "сигналы тревоги", "channelNameCommands": "команды", "channelNameEvents": "события", "channelNameInformation": "информация", "channelNameServers": "сервера", "channelNameSettings": "настройки", "channelNameStorageMonitors": "Мониторы хранилища", "channelNameSwitchGroups": "группы переключателей", "channelNameSwitches": "переключатели", "channelNameTeamchat": "командный чат", "channelNameTrackers": "трекеры", "chinook47": "Chinook 47", "chinook47DetectedSetting": "Уведомление при обнаружении Chinook 47.", "chinook47EntersMap": "Chinook 47 появился на карте ({location}), чтобы сбросить запертый ящик.", "chinook47LeftMap": "Chinook 47 покидает карту ({location}).", "chinook47Located": "Chinook 47 расположен на {location}.", "chinook47NotOnMap": "Chinook 47 в настоящее время не на карте.", "christmasLights": "Рождественская лента", "circumflex": "Циркумфлекс", "clanTag": "Клановый Тэг", "codes": "Коды", "colon": "Двоеточие", "comma": "Запятая", "commandCap": "КОМАНДА", "commandDelaySetting": "Должна ли быть задержка команды? Сколько?", "commandNotPossibleDiscord": "Командование через discord невозможно.", "commandSyntaxAdd": "add", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "alive", "commandSyntaxArmored": "бронированный", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "connection", "commandSyntaxConnections": "connections", "commandSyntaxCraft": "создание", "commandSyntaxDeath": "death", "commandSyntaxDeaths": "deaths", "commandSyntaxDecay": "распад", "commandSyntaxDespawn": "разрушение", "commandSyntaxEvents": "события", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "language", "commandSyntaxLarge": "large", "commandSyntaxLeader": "leader", "commandSyntaxList": "list", "commandSyntaxMarker": "marker", "commandSyntaxMarkers": "markers", "commandSyntaxMarket": "market", "commandSyntaxMetal": "металл", "commandSyntaxMute": "mute", "commandSyntaxNote": "note", "commandSyntaxNotes": "notes", "commandSyntaxOff": "выкл", "commandSyntaxOffline": "offline", "commandSyntaxOn": "вкл", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "player", "commandSyntaxPlayers": "игроки", "commandSyntaxPop": "население", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "Переработка", "commandSyntaxRemove": "remove", "commandSyntaxResearch": "Исследование", "commandSyntaxSearch": "search", "commandSyntaxSend": "send", "commandSyntaxSmall": "small", "commandSyntaxStack": "стак", "commandSyntaxStatus": "статус", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "камень", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "команда", "commandSyntaxTime": "time", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "торговец", "commandSyntaxTwig": "солома", "commandSyntaxUnmute": "unmute", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "upkeep", "commandSyntaxUptime": "апт<PERSON><PERSON>м", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "дерево", "commandsAlarmDesc": "Операции для Умных сигнализаций.", "commandsAlarmEditDesc": "Редактирование свойств умной сигнализации.", "commandsAlarmEditIdDesc": "Укажите ID умной сигнализации.", "commandsAlarmEditImageDesc": "Установите изображение, которое лучше всего представляет умную сигнализацию.", "commandsAliasAddAliasDesc": "Псевдоним для использования.", "commandsAliasAddDesc": "Добавить псевдоним.", "commandsAliasAddValueDesc": "Команда/последовательность символов.", "commandsAliasDesc": "Создайте псевдоним для команды/последовательности символов.", "commandsAliasRemoveDesc": "Удалить псевдоним.", "commandsAliasRemoveIndexDesc": "Индекс псевдонимов для удаления.", "commandsAliasShowDesc": "Показать все зарегистрированные псевдонимы.", "commandsBlacklistAddDesc": "Добавляет пользователя в чёрный список.", "commandsBlacklistDesc": "Черный список пользователей с использованием бота.", "commandsBlacklistDiscordUserDesc": "Пользователь дискорда.", "commandsBlacklistRemoveDesc": "Удаляет пользователя из черного списка.", "commandsBlacklistShowDesc": "Показать пользователей в черном списке.", "commandsBlacklistSteamidDesc": "Steamid пользователя.", "commandsCctvDesc": "Отображение кодов для камер на Монументах", "commandsCraftDesc": "Отображение стоимости для создания предмета.", "commandsCraftQuantityDesc": "Количество предметов для создания.", "commandsCredentialsAddDesc": "Добавить учетные данные FCM.", "commandsCredentialsDesc": "Установите/очистите учетные данные FCM для пользователя.", "commandsCredentialsRemoveDesc": "Удалить учетные данные FCM.", "commandsCredentialsRemoveSteamIdDesc": "Укажите SteamID учетных данных FCM для удаления.", "commandsCredentialsSetHosterDesc": "Установите лидера для учетных данных FCM.", "commandsCredentialsSetHosterSteamIdDesc": "SteamId лидера FCM даных.", "commandsCredentialsShowDesc": "Показать в настоящее время зарегистрированные учетные данные FCM.", "commandsDecayDesc": "Отображение время исчезновения предмета.", "commandsDespawnDesc": "Отображение времени исчезновения элемента.", "commandsHelpCommandList": "Список команд", "commandsHelpDesc": "Показать информацию о боте.", "commandsHelpHowToCredentials": "Как зарегистрировать учетные данные", "commandsHelpHowToPairServer": "Как связать бота с сервером Rust", "commandsItemDesc": "Получить информацию о предмете.", "commandsLeaderDesc": "Дать или взять руководство команды.", "commandsLeaderMemberDesc": "Имя члена команды.", "commandsMapAllDesc": "Получить карту со всеми монументами и маркерами.", "commandsMapCleanDesc": "Получить чистую карту.", "commandsMapDesc": "Получить изображение карты сервера, подключенного в данный момент.", "commandsMapMarkersDesc": "Получить карту со всеми маркерами.", "commandsMapMonumentsDesc": "Получить карту с названиями монументов.", "commandsMarketDesc": "Операции для Торговых автоматов.", "commandsMarketListDesc": "Показать список подписок.", "commandsMarketOrderDesc": "Тип заказа.", "commandsMarketSearchDesc": "Поиск товара в Торговых автоматах.", "commandsMarketSubscribeDesc": "Подписаться на товар в разделе Торговые автоматы.", "commandsMarketUnsubscribeDesc": "Отписаться от товара в разделе Торговые автоматы.", "commandsPlayersBattlemetricsIdDesc": "ID Battlemetrics сервера (по умолчанию: подключенный сервер).", "commandsPlayersDesc": "Получить информацию игрока/игроков основываясь на Battlemetrics.", "commandsPlayersNameDesc": "Искать в Battlemetrics по нику игрока.", "commandsPlayersPlayerIdDesc": "Найдите  в Battlemetrics по id игрока.", "commandsPlayersPlayerIdPlayerIdDesc": "ID игрока.", "commandsPlayersStatusDesc": "Поиск игроков, которые онлайн/оффлайн/любых.", "commandsRecycleDesc": "Отобразить выход после переработки предмета.", "commandsRecycleQuantityDesc": "Количество предметов для переработки.", "commandsRecycleRecyclerTypeDesc": "Тип переработчика (перераб<PERSON><PERSON>чик, измельчитель, переработчик безопасной зоны).", "commandsResearchDesc": "Отображение стоимости для исследования предмета.", "commandsResetAlarmsDesc": "Сбросить тревожный канал.", "commandsResetDesc": "Перезагрузка каналов Discord", "commandsResetInformationDesc": "Сбросить информационный канал.", "commandsResetServersDesc": "Сбросить канал серверов.", "commandsResetSettingsDesc": "Сбросить настройки канала.", "commandsResetStorageMonitorsDesc": "Сбросить канал мониторов хранилища.", "commandsResetSwitchesDesc": "Сбросить переключатели и переключить каналы групп.", "commandsResetTrackersDesc": "Сбросить канал трекеров.", "commandsRoleClearDesc": "Очистите роль (чтобы все могли видеть каналы бота).", "commandsRoleDesc": "Установить/очистить определенную роль, которая сможет увидеть контент категории rustplusplus.", "commandsRoleSetDesc": "Добавить роль (которая сможет видеть каналы бота).", "commandsRoleSetRoleDesc": "Укажите роль, которая будет видеть каналы бота.", "commandsStackDesc": "Отобразить размер пачки предмета.", "commandsStoragemonitorDesc": "Операции на Мониторах хранения.", "commandsStoragemonitorEditDesc": "Редактирование свойств монитора хранения.", "commandsStoragemonitorEditIdDesc": "Укажите ID монитора хранения.", "commandsStoragemonitorEditImageDesc": "Установите изображение, которое лучше всего представляет монитор хранения.", "commandsSwitchDesc": "Операции на умных выключателях.", "commandsSwitchEditDesc": "Редактирование свойств Умного выключателя.", "commandsSwitchEditIdDesc": "Укажите ID умного выключателя.", "commandsSwitchEditImageDesc": "Установите изображение, которое лучше всего представляет это устройство.", "commandsUpkeepDesc": "Отобразить стоимость предмета.", "commandsUptimeBotDesc": "Отоб<PERSON><PERSON><PERSON><PERSON><PERSON>ь аптайм бота.", "commandsUptimeDesc": "Отобр<PERSON><PERSON><PERSON><PERSON>ь аптайм бота и сервера.", "commandsUptimeServerDesc": "Отоб<PERSON><PERSON><PERSON><PERSON><PERSON>ь аптайм сервера.", "commandsVoiceBotJoinedVoice": "Бот присоединился к голосовому каналу", "commandsVoiceBotLeftVoice": "Бот покинул голосовой канал", "commandsVoiceDesc": "Голосовые команды", "commandsVoiceFemale": "Женский", "commandsVoiceFemaleDescription": "Устанавливает женский голос для бота.", "commandsVoiceGenderDesc": "Выберите пол голоса бота.", "commandsVoiceJoin": "Вступление в голосовой канал {name} с ID {id} в гильдию {guild}", "commandsVoiceJoinDesc": "Присоединиться к голосовому каналу", "commandsVoiceLeave": "Покинул голосовой канал {name} с ID {id} в гильдии {guild}", "commandsVoiceLeaveDesc": "Покидает голосовой канал", "commandsVoiceMale": "Мужской", "commandsVoiceMaleDescription": "Устанавливает мужской голос для бота.", "commandsVoiceNotInVoice": "Вы не в голосовом канале.", "connect": "Ip сервера", "connectCap": "ПОДКЛЮЧИТЬСЯ", "connected": "Подключено", "connectedCap": "ПОДКЛЮЧЕНО", "connectedToServer": "ПОДКЛЮЧЕН К СЕРВЕРУ.", "connectingCap": "ПОДКЛЮЧЕНИЕ", "connectingToServer": "ПОДКЛЮЧЕНИЕ К СЕРВЕРУ...", "connectionEvents": "События подключения", "connectionRefusedTo": "Отказано в соединение к: {id}.", "connectionsCap": "СОЕДИНЕНИЯ", "couldNotAddStepTracers": "Не удалось добавить отслеживатели шагов.", "couldNotAppendMapMarkers": "Не удалось добавить маркеры карты, экземпляр Rustplus Info не установлен.", "couldNotAppendMapMonuments": "Не удалось добавить монументы карты, экземпляр Rustplus Info не установлен.", "couldNotAppendMapTracers": "Не удалось добавить трекеры карты, экземпляр rustplus info не установлен.", "couldNotConnectTo": "Не удалось подключиться к: {id}.", "couldNotCreateCategory": "Не удалось создать категорию: {name}", "couldNotCreateTextChannel": "Не удалось создать канал: {name}", "couldNotDeferInteraction": "Не удалось отложить взаимодействие.", "couldNotDeleteCategory": "Не удалось удалить категорию: {categoryId}", "couldNotDeleteChannel": "Не удалось удалить канал: {channelId}", "couldNotDeleteMessage": "Не удалось удалить сообщение: {message}", "couldNotFindAnyPlayers": "Не удалось найти игроков.", "couldNotFindCategory": "Не удалось найти категорию: {category}", "couldNotFindChannel": "Не удалось найти канал: {channel}", "couldNotFindCraftDetails": "Не удалось найти сведения о создании для {name}.", "couldNotFindDecayDetails": "Не удалось найти сведения о задержки {name}.", "couldNotFindDespawnDetails": "Не удалось найти сведения об исчезновении {name}.", "couldNotFindGuild": "Не удалось найти guildID: {guildId}", "couldNotFindLanguage": "Не удалось найти язык: {language}", "couldNotFindMessage": "Не удалось найти сообщение {message}", "couldNotFindPlayer": "Не удалось найти игрок {name}.", "couldNotFindPlayerId": "Не удалось найти игрока с id {id}.", "couldNotFindRecycleDetails": "Не удалось найти сведения о переработке для {name}.", "couldNotFindResearchDetails": "Не удалось найти сведения о исследовании для {name}.", "couldNotFindRole": "Не удалось найти роль: {roleId}", "couldNotFindStackDetails": "Не удалось найти сведения об пачке {name}.", "couldNotFindTeammate": "Не удалось найти товарища по команде: {name}.", "couldNotFindUpkeepDetails": "Не удалось найти стоимость обслуживания {name}.", "couldNotFindUser": "Не удалось найти пользователя: {userId}", "couldNotGetChannelWithId": "Не удалось получить канал с ID: {id}.", "couldNotIdentifyMember": "Не удалось найти члена команды: {name}.", "couldNotPerformBulkDelete": "Не удалось выполнить bulkDelete на канале: {channel}", "couldNotPerformMessageDelete": "Не удалось удалить сообщение.", "couldNotPerformMessagesFetch": "Не удалось выполнить сообщения, на канале: {channel}", "couldNotRegisterSlashCommands": "Не смог зарегистрировать команды для guild: {guildId}. ", "couldNotSetParent": "Не удалось установить parent для канала: {channelId}", "craft": "Кра<PERSON><PERSON>", "crate": "Crate", "createGroupCap": "СОЗДАТЬ ГРУППУ ПЕРЕКЛЮЧАТЕЛЕЙ", "createTrackerCap": "СОЗДАТЬ ТРЕКЕР", "credentialsAddedSuccessfully": "Учетные данные FCM для steamId: {steamId}, были успешно добавлены!", "credentialsAlreadyRegistered": "Учетные данные FCM для SteamId: {steamId} уже зарегистрированы!", "credentialsCannotStartLiteAlreadyHoster": "Невозможно запустить FCM Listener Lite для steamId: {steamId}, так как он уже лидер!", "credentialsDoNotExist": "Учетные данные FCM для SteamID: {steamId} не существует.", "credentialsHosterNotSetForGuild": "Лидер FCM данных, для guild {id} не установлен. Пожалуйста укажите его.", "credentialsNotRegistered": "Учетные данные FCM для steamId: {steamId} не зарегистрированы!", "credentialsNotRegisteredForGuild": "Учетные данные FCM не зарегистрированы для guild: {id}, поэтому не может запустить FCM-Listener.", "credentialsRemovedSuccessfully": "Учетные данные FCM для SteamID: {steamId} успешно удалены!", "credentialsSetHosterSuccessfully": "FCM данные для лидера SteamID: {steamId}, были успешно применены.", "currencySign": "Валютный знак", "currentCommandDelay": "Текущая задержка: {delay} секунд.", "currentItemHp": "Текущий показатель здоровья предмета.", "currentPrefixPlaceholder": "Текущий префикс: {prefix}", "customCommand": "Пользовательская команда", "customTimerEditCargoShipEgressLabel": "Время отхода Грузового Корабля (сек):", "customTimerEditCrateOilRigUnlockLabel": "Время открытия ящика на Нефтяной Вышке (сек):", "customTimerEditDesc": "Редактирование таймеров", "customTimersCap": "НАСТРОЙКА ТАЙМЕРОВ", "dash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayOfWipe": "{day} День", "deathCap": "СМЕРТЬ", "decay": "Гниение", "decayTimeForItem": "Время исчезновения {item} - {time}. ", "decayingCap": "РАСПАДАЕТСЯ", "deleteUnreachableDevicesCap": "УДАЛЕНИЕ НЕДОСТУПНЫХ УСТРОЙСТВ", "despawnTime": "Время разрушения", "despawnTimeOfItem": "Время исчезновения {item} - {time}.", "deviceIsAlreadyOnOff": "{device} уже является {status}.", "deviceIsCurrentlyOnOff": "Устройство: ({device}) сейчас {status}.", "deviceWasTurnedOnOff": "Устройство: ({device}) сейчас {status}.", "disabledCap": "ВЫКЛЮЧЕНО", "discoFloor": "Диско-танцпол", "disconnectCap": "ОТКЛЮЧИТЬСЯ", "disconnected": "Отключён", "disconnectedCap": "ОТКЛЮЧЕНИЕ", "disconnectedFromServer": "ОТКЛЮЧЕНИЕ ОТ СЕРВЕРА.", "discordCap": "DISCORD", "discordUsers": "Пользователи Discord", "displayInformationBattlemetricsAllOnlinePlayers": "Должны ли все игроки из Battlemetrics отображаться в информационном канале?", "displayingMap": "Отображение {mapName} карты.", "displayingOnlinePlayers": "Отображение онлайн игроков.", "distanceDirectionGrid": "{distance}м в направлении {direction}° [{grid}].", "doorController": "Контроллер двери", "dot": "Точка", "eastOfGrid": "справа от", "editCap": "РЕДАКТИРОВАТЬ", "editing": "Редактирование", "editingOf": "Редактировать {entity}", "egressInTime": "Выход в {time} в {location}.", "eight": "Восемь", "elevator": "<PERSON>и<PERSON><PERSON>", "empty": "Пусто", "enabledCap": "ВКЛЮЧЕНО", "entityId": "ID объекта", "equalsSign": "Равно", "errorCap": "ОШИБКА", "errorExecutingCommand": "Во время выполнения этой команды произошла ошибка!", "eventCap": "СОБЫТИЕ", "eventInfo": "Информация о событиях", "exclamationMark": "Восклицательный знак", "failedToScrapeProfileName": "Не удалось отсканировать имя профиля: '{link}'.", "failedToScrapeProfilePicture": "Не удалось отсканировать изображение профиля: '{link}'.", "fcmCredentials": "FCM данные", "fcmListenerStartHost": "FCM-listener Host  запустится через 5 секунд для guildId: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "FCM-listener Lite запустится через 5 секунд для guildId: {guildId}, steamId: {steamId}.", "ferryTerminal": "Паромный терминал", "fishingVillage": "Рыбацкая деревня", "five": "Пять", "four": "Четыре", "giantExcavatorPit": "Гигантский экскаватор", "greaterThanSign": "Знак больше", "groupAddSwitchDesc": "Добавить переключатель в {group}", "groupRemoveSwitchDesc": "Удалить переключатель из {group}", "harbor": "Порт", "hasBeenAliveLongest": "{name} живёт дольше всех ({time}).", "hash": "Решётка", "hbhfSensor": "HBHF-Дат<PERSON>ик", "heart": "Сердце", "heater": "Обогреватель", "heavyScientistCalledSetting": "Уведомление при активации запертого ящика на Нефтяной Вышке.", "heavyScientistsCalledLarge": "Тяжелые ученые были вызваны на большую нефтяную вышку ({location}).", "heavyScientistsCalledSmall": "Тяжелые ученые были вызваны на малую нефтяную вышку ({location}).", "hideTrademark": "Скрыть марку.", "hoster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hp": "HP", "hpExceedMax": "Hp {hp} превышает максимальный размер {max}.", "hqmQuarry": "МВК карьер", "ignoreSetAvatar": "Игнорируется setAvatar", "ignoreSetNickname": "Игнорируется SetnickName", "ignoreSetUsername": "Игнорируется setusername", "inGameBotMessagesMuted": "Внутриигровые сообщения бота отключены.", "inGameBotMessagesUnmuted": "Внутриигровые сообщения бота включены.", "inGameCap": "В ИГРЕ", "inGameEventInfo": "Информация об игре", "inGameTeamNotificationsSetting": "Уведомления о товарищах по команде.", "inGameTime": "Игровое время: {time}.", "index": "Ин<PERSON><PERSON><PERSON>с", "infoCap": "ИНФО", "inside": "Внутри", "interactionEditReplyFailed": "Взаимодействие с редактированием не удалось: {error}", "interactionInvalidChannel": "Взаимодействие с каналом не удалось.", "interactionReplyFailed": "Взаимодействие с ответом не удалось: {error}", "interactionUpdateFailed": "Взаимодействие с обновлением не удалось: {error}", "invalidBattlemetricsId": "Неверный Battlemetrics ID.", "invalidGuildOrChannel": "Неверная guild или канал.", "invalidHpInterval": "Недопустимый HP интервал {hp}.", "invalidId": "Неверный ID: {id}.", "invalidStructureType": "Недопустимый тип структуры {type}.", "invalidSubcommand": "Неверная подкоманда.", "invalidTimeDistance": "Неверная разница во времени: {distance}, предыдущая: {prevTime}, нынешняя: {newTime}", "isDecaying": "{device} начинает гнить!", "isNoLongerConnected": "{device} больше не подключен к электричеству!", "item": "Предмет", "itemAvailableInVendingMachine": "Только что {items} стал доступен в торговом автомате [{location}].", "itemAvailableNotifyInGameSetting": "Когда предмет из списка подписки становится доступным в торговом автомате, уведомлять об этом в игре?", "junkyard": "Свалке", "justSubscribedToItem": "Вы подписались на предмет: ({name}).", "languageCode": "Языковой код: {code}", "languageLangNotSupported": "Язык {language} не поддерживается.", "languageNotSupported": "Язык не поддерживается.", "largeBarn": "Бо<PERSON><PERSON><PERSON><PERSON>й амбар", "largeFishingVillage": "Большая рыбацкая деревня", "largeOilRig": "Большая нефтяная вышка", "largeWoodBox": "Бол<PERSON><PERSON><PERSON>й ящик", "lastTrigger": "Последнее срабатывание", "launchSite": "Космодром", "leaderAlreadyLeader": "{name} уже лидер команды.", "leaderCommandIsDisabled": "Команда лидера отключена в настройках.", "leaderCommandOnlyWorks": "Команда лидера работает только в том случае, если нынешний лидер {name}.", "leaderTransferred": "Руководство команды было передано {name}.", "leavingMapAt": "Выход в {location}.", "lessThanSign": "Знак меньше", "lighthouse": "Маяк", "linkCap": "ССЫЛКА", "location": "Местоположение", "lockedCrateLargeOilRigUnlocked": "Запертый ящик на большой нефтяной вышке ({location}) был разблокирован.", "lockedCrateOilRigUnlockedSetting": "Уведомление при открытии запертого ящика на Нефтяной вышке.", "lockedCrateSmallOilRigUnlocked": "Запертый ящик на Маленькой Нефтяной Вышке ({location}) был разблокирован.", "logDiscordCommand": "Discord  Command - Guild: {guild}, Канал: {channel}, Пользователь: {user}, Сообщение: {message}.", "logDiscordMessage": "Discord Message  - Guild: {guild}, Канал: {channel}, Пользователь: {user}, Сообщение: {message}.", "logInGameCommand": "{type} - Команда: {command}, Пользователь: {user}.", "logInGameMessage": "Сообщение: {message}, Пользователь: {user}", "logSmartSwitchGroupValueChange": "Группа умных переключателей - Значение: {value}.", "logSmartSwitchValueChange": "Умный Переключатель - Значение: {value}.", "loggedInAs": "Вошел в систему как: {name}", "makeSureApplicationsCommandsEnabled": "Убедитесь, что при создании URL приглашения бота, вы установили флажок applications.commands.", "map": "Название карты", "mapSalt": "Salt карты", "mapSeed": "Seed карты", "mapSize": "<PERSON><PERSON> карты", "mapWipeDetectedNotifySetting": "При обнаружении новой карты, отправлять уведомление {group}?", "markerAdded": "Метка {name} на [{location}] добавлена.", "markerDoesNotExist": "Мет<PERSON><PERSON> {name} не существует.", "markerLocation": "Метка {name} на [{location}] в {distance}m от {player} направление {direction}°.", "markerRemoved": "Метка {name} на [{location}] удалена.", "message": "Сообщение", "messageCap": "СООБЩЕНИЕ", "messageDeletedIn30": "Данное сообщение удалится через 30 секунд.", "messageEditFailed": "Не удалось отредактировать сообщение: {error}", "messageReplyFailed": "Не удалось ответить на сообщение: {error}", "messageSendFailed": "Не удалось отправить сообщение: {error}", "messageWasSent": "Сообщение отправлено.", "militaryTunnel": "Военный тоннель", "miningOutpost": "Склад", "missileSilo": "Ракетная пусковая шахта", "missingArguments": "Отсутствует аргумент.", "missingPermission": "У вас нет на это разрешения.", "missingTimerMessage": "Отсутствует сообщение таймера.", "modalValueChange": "Модальное взаимодействие - VerifyId: {id}, Значение: {value}.", "more": "ещё", "morePlayers": "{players} ... ещё {number}.", "mutedCap": "УВЕДОМЛЕНИЯ ОТКЛЮЧЕНЫ", "name": "Имя", "nameChangeHistory": "История изменения имени", "new": "Новый", "newVendingMachine": "Новый торговый автомат, расположен в {location}.", "newsCap": "NEWS", "noActiveTimers": "Нет активных таймеров.", "noCommandDelay": "Нет задержки команды.", "noCommunicationSmartSwitch": "Не удалось установить связь с умным выключателем: {name}", "noData": "Нет данных.", "noDataOnLargeOilRig": "Пока нет зарегистрированных событий о большой Нефтяной вышке.", "noDataOnSmallOilRig": "Пока нет зарегистрированных событий о маленькой Нефтяной вышке.", "noDelayCap": "БЕЗ ЗАДЕРЖКИ", "noItemFound": "Товар не удалось найти ни в одном торговом автомате...", "noItemWithIdFound": "Не удалось найти элемент с id {id} .", "noItemWithNameFound": "Не удалось найти предмет {name}.", "noNameIdGiven": "Не удалось найти ни 'name', ни 'id'.", "noOneIsAfk": "Никого нет в  AFK.", "noOneIsOffline": "Никого нет в offline.", "noOneIsOnline": "Никого нет в online.", "noRegisteredConnectionEvents": "Пока нет зарегистрированных событий подключения.", "noRegisteredConnectionEventsUser": "Нет зарегистрированных событий подключения для {user}.", "noRegisteredDeathEvents": "Пока нет зарегистрированных случаев смерти.", "noRegisteredDeathEventsUser": "Нет зарегистрированных случаев смерти для {user}.", "noRegisteredEvents": "Пока нет зарегистрированных событий.", "noRegisteredMarkers": "Нет сохраненных маркеров.", "noSavedNotes": "Нет сохраненных заметок.", "noToolCupboardWereFound": "Нет подключенных шкафов.", "none": "Отсутствует", "northEast": "сверху справа", "northOfGrid": "сверху от", "northWest": "сверху слева", "notAValidOrderType": "{order} не является допустимым типом ордеров.", "notActive": "Не активен.", "notConnectedToRustServer": "В настоящее время не подключен к серверу rust.", "notExistInSubscription": "Предмет ({name}) не находится в списке подписок.", "notFoundCap": "NOT FOUND", "notPartOfRole": "Чтобы использовать команды бота, у вас должна быть роль {role}.", "notShowingCap": "Не показывать", "noteCap": "пометка", "noteIdDoesNotExist": "Заметка: {id} не найдена.", "noteIdInvalid": "Идентификатор заметки недействителен.", "noteIdWasRemoved": "Заметка: {id} удалена.", "noteSaved": "Заметка сохранена.", "offCap": "ВЫКЛ", "offline": "Не в сети", "offlineTime": "Оф<PERSON><PERSON><PERSON><PERSON>н режим", "oilRig": "Нефтяной Вышке", "old": "Старый", "onCap": "ВКЛ", "one": "Одна", "online": "В сети", "onlineTime": "Онл<PERSON>йн режим", "onlyOneInTeam": "Вы единственный в команде.", "outpost": "Город", "outside": "Снаружи", "oxumsGasStation": "Заправка", "pairing": "сопряжение", "patrolHelicopter": "Патрульный вертолёт", "patrolHelicopterDestroyedSetting": "Уведомление когда патрульный вертолёт был уничтожен.", "patrolHelicopterDetectedSetting": "Уведомление когда патрульный вертолёт был обнаружен.", "patrolHelicopterEntersMap": "Патрульный вертолет появился ({location}).", "patrolHelicopterLeftMap": "Патрульный вертолет только что покинул карту ({location}).", "patrolHelicopterLeftSetting": "Уведомление когда патрульный вертолет покинет карту.", "patrolHelicopterLocatedAt": "Патрульный вертолет расположен ({location}).", "patrolHelicopterNotCurrentlyOnMap": "Патрульный вертолет в настоящее время находится не на карте.", "patrolHelicopterTakenDown": "Патрульный вертолет был сбит ({location}).", "percentSign": "Процент", "pipe": "Палка", "playerHasBeenAliveFor": "{name} прожил {time}.", "playerId": "ID игрока", "playerJoinedTheTeam": "{name} присоединился к команде.", "playerJustConnected": "{name} подключился.", "playerJustConnectedTo": "{name} подключился к {server}.", "playerJustConnectedTracker": "{name} подключил<PERSON>я, из трекера {tracker}.", "playerJustDied": "{name} погиб в {location}.", "playerJustDisconnected": "{name} отключился.", "playerJustDisconnectedFrom": "{name} отключился от {server}.", "playerJustDisconnectedTracker": "{name} отключил<PERSON>я, из трекера {tracker}.", "playerJustReturned": "{name} вернулся, был AFK({time}).", "playerJustWentAfk": "{name} AFK.", "playerLeftTheTeam": "{name} покинул команду.", "playerNotPairedWithServer": "Команда лидера не работает, потому что {name} не связано с сервером.", "players": "Игроки", "playersSearch": "Поиск игрока", "plusSign": "Плюс", "populationPlayers": "Население: ({current}/{max}) игро<PERSON>ов.", "populationQueue": "{number} находятся в очереди.", "powerPlant": "Электростанция", "profile": "Профиль", "proxLocation": "{name} в {distance}m от {caller} направление {direction}° [{location}]", "quantity": "Количество", "questionMark": "Вопросительный знак", "ranch": "Ранчо", "ratelimited": "RATELIMITED", "reconnectingCap": "ПЕРЕПОДКЛЮЧЕНИЕ", "reconnectingToServer": "ПЕРЕПОДКЛЮЧЕНИЕ К СЕРВЕРУ...", "recycle": "Переработка", "recycleCap": "ПЕРЕРАБОТАТЬ", "recycler": "Переработчик", "remain": "осталось", "removePlayerCap": "УДАЛИТЬ ИГРОКА", "removeSwitchCap": "УДАЛИТЬ ПЕРЕКЛЮЧАТЕЛЬ", "removedSubscribeItem": "Предмет ({name}) был убран из подписки.", "research": "Исследование", "researchTable": "Исследовательский Стол", "resetSuccess": "Discord успешно перезагружен.", "responseContainError": "Не удалось установить связь с умным устройством: {error}.", "responseIsEmpty": "Ответ пуст.", "responseIsUndefined": "Ответ не определен.", "responseTimeout": "Достигнут таймаут при ожидании ответа.", "resultRecycling": "Результат переработки", "roleCleared": "роли доступа к боту были очищены.", "roleSet": "доступ к боту был выдан для роли: {name}.", "rustMonument": "Монумент", "rustplusOperational": "RUST+ ПОДКЛЮЧЕН.", "safe-zone-recycler": "Переработчик безопасной зоны", "samsite": "Зенитная турель", "satelliteDish": "Спутниковая тарелка", "scrap": "лом", "searchResult": "Результаты поиска для элемента: **{name}**", "second": "{second} секунда", "secondCommandDelay": "Задержка команды {second} секунда.", "seconds": "{seconds} секунды", "secondsCommandDelay": "Задержка команды {seconds} секунды.", "selectInGamePrefixSetting": "Выберите, какой префикс будет для использования команды:", "selectLanguageExtendSetting": "Введите команду **/reset discord**, чтобы применить настройки нового языка.", "selectLanguageSetting": "Выберите, какой язык будет использовать бот:", "selectMenuValueChange": "Выберите меню взаимодействия - VerifyId: {id}, Значение: {value}.", "selectTrademarkSetting": "Выберите, какую марку следует отображать в каждом игровом сообщении.", "sell": "продать", "semicolon": "Точка с запятой", "sentTextToSpeech": "Сообщение отправлено в Discord.", "server": "server", "serverId": "Server ID", "serverInfo": "Информация о сервере", "serverInvalid": "Похоже, что соединение с сервером недействительно. Попробуйте повторно подключиться к серверу.", "serverJustOffline": "Сервер выключен.", "serverJustOnline": "Сервер включен.", "serverStatus": "Статус сервера", "serviceUnavailable": "Сервис недоступен: {error}", "setBotLanguage": "Выбрать язык: {language}.", "seven": "Семь", "sewerBranch": "Канализационный отвод", "shouldBotBeMutedSetting": "Заглушить бота в игровом чате?", "shouldCommandsEnabledSetting": "Разрешить использование команд в игре?", "shouldLeaderCommandEnabledSetting": "Следует ли включить команду лидера?", "shouldLeaderCommandOnlyForPairedSetting": "Должна ли команда лидера работать только для людей, которые связаны с сервером?", "shouldSmartAlarmNotifyNotConnectedSetting": "Должна ли умная сигнализация уведомлять, даже если она не настроена на подключенном сервере?", "shouldSmartAlarmsNotifyInGameSetting": "Должна ли сигнализация уведомлять в игре?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Должны ли переключатели и группы переключаетелей уведомлять в игре об их изменении из Discord?", "showingBlacklist": "Отображение чёрного списка.", "showingSubscriptionList": "Показание списка подписки.", "shredder": "Измельчитель", "sirenLight": "Сирена", "six": "Шесть", "slash": "Слэш", "slashCommandInteraction": "Модальное взаимодействие - Гильдия: {guild}, Канал: {channel}, Пользователь: {user}, CustomId: {command}, VerifyId: {id}.", "slashCommandValueChange": "Взаимодействие команды слэш - VerifyId: {id}, Значение: {value}.", "slashCommandsSuccessRegister": "GuildId: {guildId} успешно зарегистрирован.", "slots": "Слоты", "smallOilRig": "Малая нефтяная вышка", "smartAlarm": "Сигнализация", "smartAlarmEditSuccess": "Настройки умной сигнализации {name} изменены.", "smartAlarmNotifyExtendSetting": "- Эти уведомления о сигналах тревоги будут использовать название и сообщение, присвоенные умной сигнализацией.\n- Эта сигнализация может быть недоступна в текстовом канале alarms в Discord", "smartDeviceNotFound": "{device} не удалось найти! Либо он уничтожен, либо {user} потерял доступ к этому устройству.", "smartSwitch": "Переключатель", "smartSwitchAutoDay": "Переключатель будет активен только днём.", "smartSwitchAutoNight": "Переключатель будет активен только ночью.", "smartSwitchAutoOff": "Во время цикла обновления Переключатель будет автоматически выключен.", "smartSwitchAutoOffAnyOnline": "Авто.выкл, если кто-то из команды находится в сети.", "smartSwitchAutoOffProximity": "Авто.выкл, если кто-то из команды находится поблизости.", "smartSwitchAutoOn": "Во время цикла обновления Переключатель будет автоматически включен.", "smartSwitchAutoOnAnyOnline": "Авто.вкл, если кто-то из команды находится в сети.", "smartSwitchAutoOnProximity": "Авто.вкл, если кто-то из команды находится поблизости.", "smartSwitchEditProximityLabel": "Настройка дистанции (метры):", "smartSwitchEditSuccess": "Переключатель ({name}) успешно отредактирован.", "smartSwitchNormal": "Переключатель работает как обычно.", "smilyFace": "Смайлик", "somethingWrongWithConnection": "Что-то пошло не так с подключением.", "southEast": "снизу справа", "southOfGrid": "снизу от", "southWest": "снизу слева", "sprinkler": "Разбрызгиватель", "stackSize": "Размеры стопки", "stackSizeOfItem": "Размер стака {item} равен {quantity}", "status": "Статус", "statusNotConnectedToServer": "**СТАТУС** `НЕ ПОДКЛЮЧЁН К СЕРВЕРУ!`", "statusNotElectronicallyConnected": "**СТАТУС** `НЕ ПОДКЛЮЧЁН К ЭЛЕКТРИЧЕСТВУ!`", "statusNotFound": "**СТАТУС**: НЕ НАЙДЕН!", "steamId": "SteamID", "stoneQuarry": "Каменный карьер", "storageMonitor": "Монитор хранения", "storageMonitorEditSuccess": "Монитор хранения: ({name}) успешно отредактирован.", "streamerMode": "Режим Стримера", "subscribeToChangesBattlemetrics": "Подпишитесь на различные изменения в Battlemetric.", "subscriptionList": "Список подписок", "subscriptionListEmpty": "Список подписок пуст.", "sulfurQuarry": "Серный карьер", "switches": "Переключатель", "teamMember": "Члены команды", "teamMemberInfo": "Информация о команде", "theDome": "Сфера", "theIdOfTheItem": "Id предмета.", "theNameOfTheItem": "Название предмета.", "theNameOfThePlayer": "Ник игрока.", "three": "Три", "tilde": "Тильде", "time": "Время", "timeBeforeCargoEntersEgress": "Корабль в {location}. Осталось {time} до того как корабль покинет карту.", "timeBeforeCrateAtLargeOilRigUnlocks": "Че<PERSON>ез {time} на Большая Нефтяной Вышке({location}) откроется запертый ящик.", "timeBeforeCrateAtSmallOilRigUnlocks": "Че<PERSON>ез {time} на Маленькой Нефтяной Вышке({location}) откроется запертый ящик.", "timeCap": "TIME", "timeFormatInvalid": "Формат времени неверный.", "timeLeftTimer": "{id}: Та<PERSON>мер через: {time}, сообщение: {message}", "timeSinceAlarmWasTriggered": "Бу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {alarm} сработал {time} назад.", "timeSinceCargoLeft": "Грузовой корабль покинул карту {time} назад.", "timeSinceChinook47OnMap": "Последний Chinook 47 был {time} назад.", "timeSinceHeavyScientistsOnLarge": "Большая нефтяная вышка была активна {time} назад.", "timeSinceHeavyScientistsOnSmall": "Маленькая нефтяная вышка была активна {time} назад.", "timeSinceLast": "Прошло {time} с последней активности.", "timeSinceLastEvent": "Прошло {time} с последней активности.", "timeSinceLastSinceDestroyedLong": "C момента последнего вертолёта прошло {time1}. Вертолёт сбили {time2} назад.", "timeSinceLastSinceDestroyedShort": "Был {time1} назад. \nСбит {time2} назад.", "timeSincePatrolHelicopterWasOnMap": "Патрульный вертолёт был {time} назад.", "timeSinceTravelingVendorWasOnMap": "{time} прошло с появления Странствующего Торговца на карте.", "timeSinceWipe": "С последнего вайпа прошло {time}.", "timeTill": "Время до {event}", "timeTillDaylight": "Че<PERSON>ез {time} будет рассвет.", "timeTillNightfall": "Че<PERSON>ез {time} будет закат.", "timeTillStructureDecay": "{time} до распада стены {type}.", "timeUntilUnlocksAt": "Ящик откроется через {time} на {location}.", "timer": "Таймер: {message}.", "timerIdDoesNotExist": "Таймер ID: {id} не существует.", "timerIdInvalid": "Таймер ID является недействительным.", "timerRemoved": "Таймер ID: {id} был удален.", "timerSet": "Таймер установлен на {time}.", "tokensDidNotReplenish": "Tocens вовремя не пополнялся.", "toolCupboard": "<PERSON><PERSON><PERSON><PERSON> c инструментами", "total": "Всего", "tracker": "Трекер", "trackerAddPlayerDesc": "Добавить игрока в {tracker}", "trackerRemovePlayerDesc": "Удалить игрока из {tracker}", "trademarkShownBeforeMessage": "{trademark} будет отображаться перед сообщениями.", "trainYard": "Железнодорожное депо", "travelingVendor": "Странствующий Торговец", "travelingVendorDetectedSetting": "Уведомление при появлении Странствующего Торговца.", "travelingVendorHaltedAt": "Странствующий Торговец остановился на {location}.", "travelingVendorHaltedSetting": "Уведомление при остановке Странствующего Торговца.", "travelingVendorLeftSetting": "Уведомление когда Странствующий Торговец покинет карту.", "travelingVendorLocatedAt": "Странствующий Торговец расположен на {location}.", "travelingVendorLeftMap": "Странствующий Торговец только что покинул карту на {location}.", "travelingVendorNotCurrentlyOnMap": "Странствующего Торговца сейчас нет на карте.", "travelingVendorResumedAt": "Странствующий Торговец продолжил движение на {location}.", "travelingVendorSpawnedAt": "Странствующий Торговец появился на {location}.", "turnOffCap": "ВЫКЛЮЧИТЬ", "turnOnCap": "ВКЛЮЧИТЬ", "turningGroupOnOff": "Управляемая группа: {group} ({status}).", "two": "Две", "type": "Тип", "unavailable": "Недоступно", "underscore": "Подчеркивание", "underwater": "Подводный", "underwaterLab": "Подводная лаборатория", "unhandledRejection": "Непринятый отказ: {error}", "unknown": "Неизвестно", "unknownInteraction": "Неизвестное взаимодействие...", "unmutedCap": "УВЕДОМЛЕНИЯ ВКЛЮЧЕНЫ", "updateCap": "ОБНОВИТЬ", "upkeep": "Содержание", "upkeepForItem": "Стоимость обслуживания {item} составляет {cost}.", "userAddedToBlacklist": "{user} добавлен в чёрный список.", "userAlreadyInBlacklist": "{user} уже в чёрном списке.", "userButtonInteraction": "Кнопка взаимодействия - Гильдия: {guild}, Канал: {channel}, Пользователь: {user}, CustomId: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Кнопка взаимодействия - VerifyId: {id} УСПЕШНО", "userJustConnected": "{name} подключился.", "userModalInteraction": "Модальное взаимодействие - Гильдия: {guild}, Канал: {channel}, Пользователь: {user}, CustomId: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Модальное взаимодействие - VerifyId: {id} УСПЕШНО", "userNotInBlacklist": "{user} не в чёрном списке.", "userNotRegistered": "{user} не зарегистрирован.", "userPartOfBlacklist": "VerifyId: {id}, {user} является частью черного списка.", "userPartOfBlacklistDiscord": "Игрок в черный список! Гильдия: {guild}, Канал: {channel}, Пользователь: {user}, Сообщение: {message}.", "userPartOfBlacklistInGame": "Пользователь в черном списке! Пользователь: {user}, Сообщение: {message}.", "userRemovedFromBlacklist": "{user} был удален из чёрного списка.", "userSaid": "{user} сказал, {text}", "userSelectMenuInteraction": "Выберите Меню Взаимодействие - Гильдия: {guild}, Канал: {channel}, Пользователь: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Выберите меню взаимодействия - VerifyId: {id} УСПЕШНО", "userTurnedOnOffSmartSwitchFromDiscord": "{user} {status}  переключатель {name} через Discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} {status} группу переключателей {name} через Discord.", "value": "Значение", "vendingMachine": "Торговый автомат", "vendingMachineDetectedSetting": "Уведомление при обнаружении нового Торгового Автомата.", "voiceCap": "ГОЛОС", "warningCap": "ВНИМАНИЕ", "waterTreatmentPlant": "Очистные сооружения", "websiteCap": "САЙТ", "websocketClosedBeforeConnection": "WebSocket был закрыт до установления соединения.", "westOfGrid": "слева от", "wipe": "вайп", "wipeDetected": "Об<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Wipe!", "yield": "Урожай", "youAreAlreadyLeader": "Вы уже лидер.", "youAreNotPairedWithServer": "Команда лидера не работает, потому что вы не подключены к серверу."}