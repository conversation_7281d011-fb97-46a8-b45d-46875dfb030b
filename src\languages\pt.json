{"24HoursInGameTimePassed": "<PERSON><PERSON> se passaram 24 horas no tempo do jogo.", "abandonedCabins": "Cabines Abandonadas", "abandonedMilitaryBase": "Base Militar Abandonada", "abandonedSupermarket": "Supermercado Abandonado", "addPlayerCap": "<PERSON><PERSON><PERSON><PERSON>", "addSwitchCap": "Adicionar Interruptor", "afkCap": "AFK", "airfield": "Aeródromo", "alarmHaveNotBeenTriggeredYet": "O alarme {alarm} ainda não foi acionado.", "alias": "<PERSON><PERSON>", "aliasAlreadyExist": "<PERSON> alias <PERSON><PERSON>.", "aliasIndexCouldNotBeFound": "<PERSON><PERSON><PERSON> de alias n<PERSON> pôde ser encontrado.", "aliasWasAdded": "<PERSON> alias foi adicionado.", "aliasWasRemoved": "<PERSON> alias foi removido.", "aliases": "Aliases", "all": "todas", "allTeammatesAreDead": "Todos os seus colegas de equipa estão mortos.", "alreadySubscribedToItem": "Já subscrito ao item {name}.", "ampersand": "E comercial", "andMorePlayers": "... e mais {number} jogadores.", "any": "<PERSON>ual<PERSON>", "apostrophe": "<PERSON>pós<PERSON><PERSON>", "arcticResearchBase": "Base de Investigação do Árctico", "asterisk": "Asterisco", "asteriskCctvDesc": "*'s significa que você precisa de um código numérico que é diferente para cada mapa", "atLocation": "Em {location}.", "atSign": "<PERSON><PERSON>", "autoDayCap": "AUTO-DIA", "autoNightCap": "AUTO-NOITE", "autoOffAnyOnlineCap": "AUTO-DESLIGAR-ONLINE", "autoOffCap": "AUTO-DESLIGAR", "autoOffProximityCap": "AUTO-DESLIGAR-PROXIMIDADE", "autoOnAnyOnlineCap": "AUTO-LIGAR-ONLINE", "autoOnCap": "AUTO-LIGAR", "autoOnProximityCap": "AUTO-LIGAR-PROXIMIDADE", "autoSettingCap": "DEFINIÇÕES AUTOMATICAS: ", "automaticallyTurnBackOnOff": " Automaticamente voltar a {status} em {time}.", "automaticallyTurningBackOnOff": "Voltar {device} automaticamente para {status}.", "autoturret": "Torreta Automática", "badGateway": "Gateway inválido: {error}", "banditCamp": "Acampamento de Bandidos", "baseIsUnderAttack": "A sua base está a ser atacada!", "battlemetricsApiRequestFailed": "Solicitação da API Battlemetrics falhou: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Falha a atualizar o servidor Battlemetrics {server}.", "battlemetricsGlobalLoginCap": "LOGIN GLOBAL", "battlemetricsGlobalLogoutCap": "LOGOUT GLOBAL", "battlemetricsGlobalNameChangesCap": "MUDANÇAS DE NOMES GLOBAIS", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "Está a faltar o ID e Nome no pedido da Battlemetrics.", "battlemetricsInstanceCouldNotBeFound": "O pedido da Battlemetrics para o {id} não foi encontrado.", "battlemetricsOnlinePlayers": "Jogadores Online no Battlemetrics", "battlemetricsPlayersLogin": "<PERSON><PERSON> de Jogadores Battlemetrics", "battlemetricsPlayersLogout": "Logout de Jogadores Battlemetrics", "battlemetricsPlayersNameChanged": "Mudança de nome de Jogadores Battlemetrics", "battlemetricsServerNameChanged": "Mudança de nome de Servidor Battlemetrics", "battlemetricsServerNameChangesCap": "MUDANÇA DE NOME DO SERVIDOR", "battlemetricsTrackerNameChangesCap": "MUDANÇA DE NOME DO RASTREADOR", "battlemetricsTrackerPlayerNameChanged": "Mudança de nome do Rastreador de Jogadores Battlemetrics", "blacklist": "Lista Negra", "boomBox": "<PERSON><PERSON><PERSON>", "bot": "bot", "broadcaster": "Transmissor", "buttonValueChange": "Interação de Botão - VerifyId: {id}, Valor: {value}.", "buy": "comprar", "calculated": "Calculado", "cargoAt": "Em {location}.", "cargoLeavingMapAt": "O Navio de Carga vai sair do mapa em {location}.", "cargoLocatedAt": "O Navio de Carga está em {location}.", "cargoNotCurrentlyOnMap": "O Navio de Carga não está no mapa.", "cargoShipDetectedSetting": "Quando o Navio de Carga for detetado, envia uma notificação.", "cargoShipDockingAtHarbor": "O Navio de Carga acabou de ancorar no Cais em {location}", "cargoShipDockingAtHarborSetting": "Quando o Navio de Carga estiver ancorado no Cais, envia uma notificação.", "cargoShipEgressSetting": "Quando o Navio de Carga entrar em estágio de saída, envia uma notificação.", "cargoShipEntersEgressStage": "O Navio de Carga deve estar no estágio de saída em {location}.", "cargoShipEntersMap": "O Navio de Carga entrou no mapa em {location}.", "cargoShipLeftHarbor": "O Navio de Carga acabou de desancorar do Cais em {location}", "cargoShipLeftMap": "O Navio de Carga saiu do mapa em {location}.", "cargoShipLeftSetting": "Quando o Navio de Carga sair do mapa, envia uma notificação.", "cargoShipLocated": "O Navio de Carga está em {location}.", "cargoship": "<PERSON><PERSON>", "ceilingLight": "Luz de Teto", "channelNameActivity": "atividade", "channelNameAlarms": "alarmes", "channelNameCommands": "comandos", "channelNameEvents": "eventos", "channelNameInformation": "informação", "channelNameServers": "servid<PERSON>", "channelNameSettings": "definições", "channelNameStorageMonitors": "monitoresArmazenamento", "channelNameSwitchGroups": "gruposInterruptores", "channelNameSwitches": "interruptores", "channelNameTeamchat": "ChatDeEquipa", "channelNameTrackers": "rast<PERSON><PERSON>", "chinook47": "Chinook 47", "chinook47DetectedSetting": "<PERSON>uan<PERSON> um Chinook 47 entra no mapa, envia uma notificação.", "chinook47EntersMap": "O Chinook 47 entra no mapa em {location} para largar uma Caixa Bloqueada.", "chinook47LeftMap": "O Chinook 47 saiu do mapa em {location}.", "chinook47Located": "O Chinook 47 está em {location}.", "chinook47NotOnMap": "O Chinook 47 não está no mapa.", "christmasLights": "<PERSON>zes de Natal", "circumflex": "Circunflexo", "clanTag": "Tag do Clã", "codes": "Có<PERSON><PERSON>", "colon": "<PERSON><PERSON> pontos", "comma": "<PERSON><PERSON><PERSON><PERSON>", "commandCap": "COMANDO", "commandDelaySetting": "Deverá haver um atraso no comando? Quanto tempo?", "commandNotPossibleDiscord": "Comando não é possível através do Discord.", "commandSyntaxAdd": "adicionar", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "vivo", "commandSyntaxArmored": "<PERSON>ado", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "<PERSON><PERSON><PERSON>", "commandSyntaxConnections": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxCraft": "criar", "commandSyntaxDeath": "morte", "commandSyntaxDeaths": "mortes", "commandSyntaxDecay": "decay", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "eventos", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "idioma", "commandSyntaxLarge": "grande", "commandSyntaxLeader": "<PERSON><PERSON><PERSON>", "commandSyntaxList": "lista", "commandSyntaxMarker": "marcador", "commandSyntaxMarkers": "marcadores", "commandSyntaxMarket": "mercado", "commandSyntaxMetal": "metal", "commandSyntaxMute": "silenciar", "commandSyntaxNote": "nota", "commandSyntaxNotes": "notas", "commandSyntaxOff": "desligado", "commandSyntaxOffline": "offline", "commandSyntaxOn": "ligado", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "jogador", "commandSyntaxPlayers": "jogadores", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "reciclar", "commandSyntaxRemove": "remover", "commandSyntaxResearch": "pesquisa", "commandSyntaxSearch": "pesquisar", "commandSyntaxSend": "enviar", "commandSyntaxSmall": "pequeno", "commandSyntaxStack": "pilha", "commandSyntaxStatus": "estado", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "pedra", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "equipa", "commandSyntaxTime": "hora", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendedor", "commandSyntaxTwig": "twig", "commandSyntaxUnmute": "unmute", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "upkeep", "commandSyntaxUptime": "uptime", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "wood", "commandsAlarmDesc": "Operações nos Alarmes Inteligentes.", "commandsAlarmEditDesc": "<PERSON><PERSON> as propried<PERSON> de um Alarme Inteligente.", "commandsAlarmEditIdDesc": "O ID do Alarme Inteligente.", "commandsAlarmEditImageDesc": "Definir a imagem que melhor representa o Alarme Inteligente.", "commandsAliasAddAliasDesc": "O alias a ser usado.", "commandsAliasAddDesc": "<PERSON><PERSON><PERSON><PERSON> um alias.", "commandsAliasAddValueDesc": "O comando/sequência de caracteres.", "commandsAliasDesc": "Crie um alias para um comando/sequência de caracteres.", "commandsAliasRemoveDesc": "Remover um alias.", "commandsAliasRemoveIndexDesc": "O índice do alias a ser removido.", "commandsAliasShowDesc": "<PERSON>rar todos os alias registad<PERSON>.", "commandsBlacklistAddDesc": "Adicionar utilizador à lista negra.", "commandsBlacklistDesc": "Adicionar um utilizador a lista negra de utilização do bot.", "commandsBlacklistDiscordUserDesc": "O utilizador do discord.", "commandsBlacklistRemoveDesc": "Remover um utilizador da lista negra.", "commandsBlacklistShowDesc": "Mostrar utilizadores na lista negra.", "commandsBlacklistSteamidDesc": "A steamid do utilizador.", "commandsCctvDesc": "Exibir códigos CCTV para um monumento", "commandsCraftDesc": "Exibir o custo para criar um item.", "commandsCraftQuantityDesc": "A quantidade de itens a serem criados.", "commandsCredentialsAddDesc": "Adicionar credenciais FCM.", "commandsCredentialsDesc": "Definir/Lim<PERSON> as Credenciais FCM para a conta do usuário.", "commandsCredentialsRemoveDesc": "Remover Credenciais FCM.", "commandsCredentialsRemoveSteamIdDesc": "SteamId das Credenciais FCM para remover.", "commandsCredentialsSetHosterDesc": "Definir o hospedeiro das Credenciais FCM.", "commandsCredentialsSetHosterSteamIdDesc": "SteamId do hospedeiro das credenciais FCM.", "commandsCredentialsShowDesc": "Mostra as Credenciais FCM registadas atualmente.", "commandsDecayDesc": "Exibe o tempo de decomposição de um item.", "commandsDespawnDesc": "Exibe o tempo de desaparecimento de um item.", "commandsHelpCommandList": "Lista de Comandos", "commandsHelpDesc": "<PERSON><PERSON><PERSON> men<PERSON> de a<PERSON>.", "commandsHelpHowToCredentials": "Como registrar as credenciais", "commandsHelpHowToPairServer": "Como emparelhar o Bot com o servidor Rust", "commandsItemDesc": "Obter os detalhes de um item.", "commandsLeaderDesc": "Dar ou retirar a liderança de/para um membro da equipa.", "commandsLeaderMemberDesc": "O nome do membro da equipa.", "commandsMapAllDesc": "Obter o mapa, incluindo nomes de monumentos e marcadores.", "commandsMapCleanDesc": "Obter o mapa limpo.", "commandsMapDesc": "Obter a imagem do mapa do servidor atualmente conectado.", "commandsMapMarkersDesc": "Obter o mapa incluindo marcadores.", "commandsMapMonumentsDesc": "Obter o mapa incluindo marcadores.", "commandsMarketDesc": "Operações para Máquinas de Venda.", "commandsMarketListDesc": "Exibir a lista de subscrição.", "commandsMarketOrderDesc": "O tipo de pedido.", "commandsMarketSearchDesc": "Procurar por um item nas Máquinas de Venda.", "commandsMarketSubscribeDesc": "Subscrever um item na Máquina de Venda.", "commandsMarketUnsubscribeDesc": "Cancelar subscrição de um item na Máquina de Venda.", "commandsPlayersBattlemetricsIdDesc": "O ID do Battlemetrics do servidor (padrão: O servidor conectado).", "commandsPlayersDesc": "Obter informações do jogador/jogador com base no Battlemetrics.", "commandsPlayersNameDesc": "Procurar um jogador no Battlemetrics com base no nome do jogador.", "commandsPlayersPlayerIdDesc": "Procurar um jogador no Battlemetrics com base no ID do jogador.", "commandsPlayersPlayerIdPlayerIdDesc": "O ID do jogador.", "commandsPlayersStatusDesc": "Procurar por jogadores que estão online/offline/qualquer.", "commandsRecycleDesc": "Exibir os recursos da reciclagem de um item.", "commandsRecycleQuantityDesc": "A quantidade de itens para reciclar.", "commandsRecycleRecyclerTypeDesc": "O tipo de reciclador (reciclador, triturador ferro-velho, reciclador zona segura).", "commandsResearchDesc": "Exibe o custo para pesquisar um item.", "commandsResetAlarmsDesc": "Repor o canal de alarmes.", "commandsResetDesc": "Repor canais do Discord.", "commandsResetInformationDesc": "Repor canal de informações.", "commandsResetServersDesc": "Repor canal de servidores.", "commandsResetSettingsDesc": "Repor canal de configurações.", "commandsResetStorageMonitorsDesc": "Repor canal de monitores de armazenamento.", "commandsResetSwitchesDesc": "Repor canal de interruptores e Grupos de interruptores.", "commandsResetTrackersDesc": "Repor canal de rastreadores.", "commandsRoleClearDesc": "Limpar o cargo (para permitir que todos vejam os canais do rustplusplus).", "commandsRoleDesc": "Definir/Limpar o cargo específico que será capaz de ver o conteúdo da categoria do rustplusplus.", "commandsRoleSetDesc": "Definir o cargo.", "commandsRoleSetRoleDesc": "O cargo para que os canais do rustplusplus estarão visíveis.", "commandsStackDesc": "<PERSON><PERSON><PERSON> o tamanho da pilha de um item.", "commandsStoragemonitorDesc": "Operações nos monitores de armazenamento.", "commandsStoragemonitorEditDesc": "<PERSON><PERSON> as propriedades de um Monitor de Armazenamento.", "commandsStoragemonitorEditIdDesc": "O ID do Monitor de Armazenamento.", "commandsStoragemonitorEditImageDesc": "Definir a imagem que melhor representa o Monitor de Armazenamento.", "commandsSwitchDesc": "Operações nos Interruptores Inteligentes.", "commandsSwitchEditDesc": "<PERSON><PERSON> as propriedades de um interruptor inteligente.", "commandsSwitchEditIdDesc": "O ID do Interruptor Inteligente.", "commandsSwitchEditImageDesc": "Definir a imagem que melhor representa o Interruptor Inteligente.", "commandsUpkeepDesc": "Exibir o custo de manutenção de um item.", "commandsUptimeBotDesc": "Exibir tempo de atividade do bot.", "commandsUptimeDesc": "Exibir tempo de atividade do bot e do servidor.", "commandsUptimeServerDesc": "Exibir tempo de atividade do servidor.", "commandsVoiceBotJoinedVoice": "O bot juntou-se ao canal de voz", "commandsVoiceBotLeftVoice": "O bot saiu do canal de voz", "commandsVoiceDesc": "Comandos de Voz do Bot", "commandsVoiceFemale": "Feminino", "commandsVoiceFemaleDescription": "Definir o gênero do ator para Feminino", "commandsVoiceGenderDesc": "Definir o gênero do ator de voz.", "commandsVoiceJoin": "A entrar no canal de voz {name} com o ID {id} na guilda {guild}", "commandsVoiceJoinDesc": "Junta-se ao canal de voz", "commandsVoiceLeave": "A sair do canal de voz {name} com o ID {id} na guilda {guild}", "commandsVoiceLeaveDesc": "Sai do canal de voz", "commandsVoiceMale": "<PERSON><PERSON><PERSON><PERSON>", "commandsVoiceMaleDescription": "Definir o gênero do ator para Masculino", "commandsVoiceNotInVoice": "Você não está num canal de voz", "connect": "Conectar", "connectCap": "CONECTAR", "connected": "Conectado", "connectedCap": "CONECTADO", "connectedToServer": "CONECTADO AO SERVIDOR.", "connectingCap": "A CONECTAR", "connectingToServer": "A CONECTAR AO SERVIDOR...", "connectionEvents": "Eventos de conexão", "connectionRefusedTo": "Conexão recusada a: {id}.", "connectionsCap": "CONEXÕES", "couldNotAddStepTracers": "Não foi possível adicionar rastro de passos.", "couldNotAppendMapMarkers": "Não foi possível adicionar marcadores de mapa, o pedido do rustplus não está definido.", "couldNotAppendMapMonuments": "Não foi possível adicionar monumentos do mapa, o pedido do rustplus não está definido.", "couldNotAppendMapTracers": "Não foi possível adicionar rastros do mapa, o pedido do rustplus não está definido.", "couldNotConnectTo": "Não foi possível conectar a: {id}.", "couldNotCreateCategory": "Não foi possível criar a categoria: {name}", "couldNotCreateTextChannel": "Não foi possível criar o canal de texto: {name}", "couldNotDeferInteraction": "Não foi possível adiar a interação.", "couldNotDeleteCategory": "Não foi possível apagar categoria: {categoryId}", "couldNotDeleteChannel": "Não foi possível apagar o canal: {channelId}", "couldNotDeleteMessage": "Não foi possível apagar a mensagem: {message}", "couldNotFindAnyPlayers": "Não foi possível encontrar nenh<PERSON> jogador.", "couldNotFindCategory": "Não foi possível encontrar a categoria: {category}", "couldNotFindChannel": "Não foi possível encontrar o canal: {channel}", "couldNotFindCraftDetails": "Não foi possível encontrar detalhes de criação para {name}.", "couldNotFindDecayDetails": "Não foi possível encontrar detalhes de decomposição para {name}.", "couldNotFindDespawnDetails": "Não foi possível encontrar detalhes de desaparecimento para {name}.", "couldNotFindGuild": "Não foi possível encontrar a guilda: {guildId}", "couldNotFindLanguage": "Não foi possível encontrar o idioma: {language}", "couldNotFindMessage": "Não foi possível encontrar a mensagem {message}", "couldNotFindPlayer": "Não foi possível encontrar o jogador {name}.", "couldNotFindPlayerId": "Não foi possível encontrar o jogador com o id {id}.", "couldNotFindRecycleDetails": "Não foi possível encontrar detalhes da reciclagem para {name}.", "couldNotFindResearchDetails": "Não foi possível encontrar detalhes de pesquisa para {name}.", "couldNotFindRole": "Não foi possível encontrar o cargo: {roleId}", "couldNotFindStackDetails": "Não foi possível encontrar detalhes de pilha para {name}.", "couldNotFindTeammate": "Não foi possível encontrar o colega de equipa: {name}.", "couldNotFindUpkeepDetails": "Não foi possível encontrar detalhes de manutenção para {name}.", "couldNotFindUser": "Não foi possível encontrar o utilizador: {userId}", "couldNotGetChannelWithId": "Não foi possível obter o canal com id: {id}.", "couldNotIdentifyMember": "Não foi possível identificar o membro da equipa: {name}.", "couldNotPerformBulkDelete": "Não foi possível executar remoção em massa no canal: {channel}", "couldNotPerformMessageDelete": "Não foi possível apagar a mensagem.", "couldNotPerformMessagesFetch": "Não foi possível realizar a pesquisa de mensagens no canal: {channel}", "couldNotRegisterSlashCommands": "Não foi possível registar comandos Barra para a guilda: {guildId}. ", "couldNotSetParent": "Could not set parent for channel: {channelId}", "craft": "<PERSON><PERSON><PERSON>", "crate": "Caixa", "createGroupCap": "CRIAR GRUPO", "createTrackerCap": "CRIAR RASTREADOR", "credentialsAddedSuccessfully": "Credenciais FCM adicionadas com sucesso para o steamId: {steamId}!", "credentialsAlreadyRegistered": "Credenciais FCM para o steamId: {steamId} já estão registadas!", "credentialsCannotStartLiteAlreadyHoster": "Não é possível iniciar o FCM Listener Lite para steamId: {steamId}. J<PERSON> hospedeiro.", "credentialsDoNotExist": "Credenciais FCM para o steamId: {steamId} não existe.", "credentialsHosterNotSetForGuild": "O hospedeiro das credenciais FCM não está definido para guild {id}, por favor defina um hospedeiro.", "credentialsNotRegistered": "Credenciais FCM para o steamId: {steamId} não está registado!", "credentialsNotRegisteredForGuild": "As credenciais FCM não estão registradas para a guilda: {id}, não pode iniciar o FCM-listener.", "credentialsRemovedSuccessfully": "Credenciais FCM para o steamId: {steamId} foram removidas com sucesso!", "credentialsSetHosterSuccessfully": "O hospedeiro das Credenciais FCM foi adicionado com sucesso para o steamId: {steamId}.", "currencySign": "<PERSON><PERSON>", "currentCommandDelay": "Atraso atual do comando: {delay} segundos.", "currentItemHp": "A vida atual do item.", "currentPrefixPlaceholder": "Prefixo Atual: {prefix}", "customCommand": "Comando Personalizado", "customTimerEditCargoShipEgressLabel": "Tempo de saída do Navio de Carga (segundos):", "customTimerEditCrateOilRigUnlockLabel": "Tempo de desbloqueio Caixa da Oil (segundo):", "customTimerEditDesc": "A editar os temporizadores personalizados", "customTimersCap": "TEMPORIZADORES PERSONALIZADOS", "dash": "Traço", "dayOfWipe": "Dia {day}", "deathCap": "MORTE", "decay": "Decomposição", "decayTimeForItem": "Tempo de decomposição de {item} é {time}.", "decayingCap": "DECOMPONDO", "deleteUnreachableDevicesCap": "REMOVER DISPOSITIVOS NÃO ACESSÍVEIS", "despawnTime": "Tempo para desaparecer", "despawnTimeOfItem": "O tempo para desaparecer {item} é {time}.", "deviceIsAlreadyOnOff": "{device} já está {status}.", "deviceIsCurrentlyOnOff": "{device} está {status}.", "deviceWasTurnedOnOff": "{device} estava {status}.", "disabledCap": "DESATIVADO", "discoFloor": "Chão de Discoteca", "disconnectCap": "DESCONECTAR", "disconnected": "Desconectado", "disconnectedCap": "DESCONECTADO", "disconnectedFromServer": "DESCONECTADO DO SERVIDOR.", "discordCap": "DISCORD", "discordUsers": "Utilizadores do Discord", "displayInformationBattlemetricsAllOnlinePlayers": "Devem todos os jogadores online do Battlemetrics serem exibidos no canal de informação?", "displayingMap": "Exibindo o mapa {mapName}.", "displayingOnlinePlayers": "Exibindo jogadores online.", "distanceDirectionGrid": "{distance}m na direção {direction}° [{grid}].", "doorController": "Controlador de Porta", "dot": "Ponto", "eastOfGrid": "<PERSON>ste da grade", "editCap": "EDITAR", "editing": "A editar", "editingOf": "A editar {entity}", "egressInTime": "A sair em {time} por {location}.", "eight": "<PERSON><PERSON>", "elevator": "Elevador", "empty": "<PERSON><PERSON><PERSON>", "enabledCap": "ATIVADO", "entityId": "ID da entidade", "equalsSign": "Sinal de igual", "errorCap": "ERRO", "errorExecutingCommand": "Ocorreu um erro ao executar este comando!", "eventCap": "EVENTO", "eventInfo": "Informação do Evento", "exclamationMark": "Ponto de exclamação", "failedToScrapeProfileName": "Falha ao analisar o nome de perfil: {link}.", "failedToScrapeProfilePicture": "Falha ao analisar a imagem de perfil: {link}.", "fcmCredentials": "Credenciais do FCM", "fcmListenerStartHost": "O Host FCM-listener iniciará em 5 segundos para o guildId: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "O Host FCM-listener Lite iniciará em 5 segundos para o guildId: {guildId}, steamId: {steamId}.", "ferryTerminal": "Terminal de Ferry", "fishingVillage": "Vila de Pesca", "five": "Cinco", "four": "Quatro", "giantExcavatorPit": "Tubo de escavador Gigante", "greaterThanSign": "<PERSON>al de maior que", "groupAddSwitchDesc": "Adicionar Interruptor a {group}", "groupRemoveSwitchDesc": "Remover Interruptor de {group}", "harbor": "Cais", "hasBeenAliveLongest": "{name} esteve vivo durante mais tempo ({time}).", "hash": "<PERSON>", "hbhfSensor": "Sensor HBHF", "heart": "Coração", "heater": "<PERSON><PERSON><PERSON><PERSON>é<PERSON>", "heavyScientistCalledSetting": "Quando os Cientistas Pesados são chamados à Plataforma de Petróleo, envia uma notificação.", "heavyScientistsCalledLarge": "Cientistas pesados foram chamados para a Plataforma de Petróleo Grande em {location}.", "heavyScientistsCalledSmall": "Cientistas pesados foram chamados para a Plataforma de Petróleo em {location}.", "hideTrademark": "Ocultar marca.", "hoster": "<PERSON><PERSON><PERSON><PERSON>", "hp": "Vida", "hpExceedMax": "A vida {hp} excede o máximo de {max}.", "hqmQuarry": "Pedreira HQM", "ignoreSetAvatar": "<PERSON><PERSON><PERSON><PERSON>", "ignoreSetNickname": "setNickname Ignorado", "ignoreSetUsername": "setUsername Ignorado", "inGameBotMessagesMuted": "Mensagens do bot silenciadas no jogo.", "inGameBotMessagesUnmuted": "Mensagens do bot deixaram de ser silenciadas no jogo.", "inGameCap": "IN-GAME", "inGameEventInfo": "Informação do evento no jogo", "inGameTeamNotificationsSetting": "Notificações de companheiros no jogo.", "inGameTime": "Horas do jogo: {time}.", "index": "<PERSON><PERSON><PERSON>", "infoCap": "INFO", "inside": "<PERSON><PERSON>", "interactionEditReplyFailed": "Falha na resposta à edição da interação: {error}", "interactionInvalidChannel": "Interação de um canal inválido.", "interactionReplyFailed": "Resposta de interação falhou: {error}", "interactionUpdateFailed": "Atualização da interação falhou: {error}", "invalidBattlemetricsId": "ID da Battlemetrics invalido.", "invalidGuildOrChannel": "Guilda ou canal inválido.", "invalidHpInterval": "Intervalo de Vida {hp} inválido.", "invalidId": "ID Inválido: {id}.", "invalidStructureType": "Categoria de estrutura {type} inválido.", "invalidSubcommand": "Subcomando inválido.", "invalidTimeDistance": "Distância de tempo inválida: {distance}, anterior: {prevTime}, novo: {newTime}", "isDecaying": "{device} está a decompor!", "isNoLongerConnected": "{device} já não está conectado eletricamente!", "item": "<PERSON><PERSON>", "itemAvailableInVendingMachine": "{items} acabou de ficar disponível numa Máquina de Venda Automática em [{location}].", "itemAvailableNotifyInGameSetting": "Quando um item da lista de subscrição ficar disponível numa Máquina de Venda, notificar no jogo?", "junkyard": "Ferro-velho", "justSubscribedToItem": "Subscreveu o item {name}.", "languageCode": "Código de idioma: {code}", "languageLangNotSupported": "O idioma {language} não é suportado.", "languageNotSupported": "O idioma não é suportado.", "largeBarn": "<PERSON><PERSON><PERSON>", "largeFishingVillage": "Vila de Pesca Grande", "largeOilRig": "Plataforma de Petróleo Grande", "largeWoodBox": "Caixa de Madeira Grande", "lastTrigger": "Last Trigger", "launchSite": "Sitio de Lançamento", "leaderAlreadyLeader": "{name} j<PERSON> <PERSON> o líder da equipa.", "leaderCommandIsDisabled": "O comando Líder está desativado nas configurações.", "leaderCommandOnlyWorks": "O comando de líder só funciona se o líder atual for {name}.", "leaderTransferred": "A liderança da equipa foi transferida para {name}.", "leavingMapAt": "Saindo em {location}.", "lessThanSign": "Sinal menor que", "lighthouse": "Farol", "linkCap": "LINK", "location": "Localização", "lockedCrateLargeOilRigUnlocked": "A caixa bloqueada da Plataforma de Petróleo Grande em {location} foi desbloqueada.", "lockedCrateOilRigUnlockedSetting": "Quando uma Caixa Bloqueada na Plataforma de Petróleo é desbloqueada, envia uma notificação.", "lockedCrateSmallOilRigUnlocked": "A caixa bloqueada da Plataforma de Petróleo em {location} foi desbloqueada.", "logDiscordCommand": "Comando do Discord - Guilda: {guild}, Canal: {channel}, Utilizador: {user}, Mensagem: {message}.", "logDiscordMessage": "Mensagem do Discord - Guilda: {guild}, Canal: {channel}, Utilizador: {user}, Mensagem: {message}.", "logInGameCommand": "{type} - Comando: {command}, Utilizador: {user}.", "logInGameMessage": "Mensagem: {message}, Utilizador: {user}", "logSmartSwitchGroupValueChange": "Grupo de Interruptores Inteligentes - Valor: {value}.", "logSmartSwitchValueChange": "Interruptor Inteligente - Valor: {value}.", "loggedInAs": "CONECTADO COMO: {name}", "makeSureApplicationsCommandsEnabled": "Certifique-se de que o applications.commands está marcado ao criar o URL de convite.", "map": "Mapa", "mapSalt": "Sal do Mapa", "mapSeed": "Semente do Mapa", "mapSize": "Tamanho do mapa", "mapWipeDetectedNotifySetting": "Quando o Wipe do Mapa for detectada, {group} deve ser notificado?", "markerAdded": "O marcador {name} em [{location}] foi adicionado.", "markerDoesNotExist": "O Marcador {name} não existe.", "markerLocation": "O marcador {name} em [{location}] está a {distance}m de {player} na direção {direction}°.", "markerRemoved": "O marcador {name} em [{location}] foi removido.", "message": "Mensagem", "messageCap": "MENSAGEM", "messageDeletedIn30": "Esta mensagem será apagada em 30 segundos.", "messageEditFailed": "Edição de mensagem falhou: {error}", "messageReplyFailed": "Resposta à mensagem falhou: {error}", "messageSendFailed": "<PERSON><PERSON> de mensagem falhou: {error}", "messageWasSent": "A mensagem foi enviada.", "militaryTunnel": "<PERSON><PERSON><PERSON>", "miningOutpost": "Posto de Mineração", "missileSilo": "Silo de Mísseis", "missingArguments": "Argumentos em falta.", "missingPermission": "Você não tem permissão para fazer isto.", "missingTimerMessage": "Falta a mensagem do temporizador.", "modalValueChange": "Interação Modal - VerifyId: {id}, Valor: {value}.", "more": "mais", "morePlayers": "{players} ...{number} mais.", "mutedCap": "SILENCIADO", "name": "Nome", "nameChangeHistory": "Histórico de alterações de nome", "new": "Novo", "newVendingMachine": "Nova Máquina de Venda, localizada em {location}.", "newsCap": "Novidades", "noActiveTimers": "Sem temporizadores ativos.", "noCommandDelay": "Sem atraso de comando.", "noCommunicationSmartSwitch": "Não foi possível comunicar com o Interruptor Inteligente: {name}", "noData": "Sem dados.", "noDataOnLargeOilRig": "Sem dados atuais na Plataforma de Petróleo Grande.", "noDataOnSmallOilRig": "Sem dados atuais na Plataforma de Petróleo.", "noDelayCap": "SEM ATRASO", "noItemFound": "O item não foi encontrado em nenhuma Máquina de Venda...", "noItemWithIdFound": "Nenhum item com o id {id} foi encontrado.", "noItemWithNameFound": "Nenhum item com o nome {name} foi encontrado.", "noNameIdGiven": "Nenhum 'nome' ou 'id' foi fornecido.", "noOneIsAfk": "Não está ninguém AFK.", "noOneIsOffline": "Não está ninguém desconectado.", "noOneIsOnline": "Não está ninguém conectado.", "noRegisteredConnectionEvents": "Não há eventos de conexão registados ainda.", "noRegisteredConnectionEventsUser": "Não há eventos de conexão registados ainda para {user}.", "noRegisteredDeathEvents": "Não há eventos de morte registados ainda.", "noRegisteredDeathEventsUser": "Não há eventos de morte registados ainda para {user}.", "noRegisteredEvents": "Não há eventos registados ainda.", "noRegisteredMarkers": "Não há marcadores registados.", "noSavedNotes": "Não há notas guardadas.", "noToolCupboardWereFound": "Nenhum monitor de Armário de Ferramentas foi encontrado.", "none": "<PERSON><PERSON><PERSON>", "northEast": "Nordeste", "northOfGrid": "Norte da grelha", "northWest": "Noroeste", "notAValidOrderType": "{order} não é um tipo de pedido válido.", "notActive": "Não activo.", "notConnectedToRustServer": "Não está conectado a um servidor rust no momento.", "notExistInSubscription": "O item {name} não existe na lista de subscrição.", "notFoundCap": "NÃO ENCONTRADO", "notPartOfRole": "Você não faz parte do cargo {role}, portanto não pode executar comandos do bot.", "notShowingCap": "NÃO MOSTRAR", "noteCap": "NOTA", "noteIdDoesNotExist": "ID da Nota: {id} não existe.", "noteIdInvalid": "O ID da nota é inválido.", "noteIdWasRemoved": "ID da Nota: {id} foi removido.", "noteSaved": "Nota gravada.", "offCap": "DESLIGADO", "offline": "Desconectado", "offlineTime": "Tempo desconectado", "oilRig": "Plataforma de Petróleo", "old": "Antigo", "onCap": "LIGADO", "one": "Um", "online": "Conectado", "onlineTime": "Tempo conectado", "onlyOneInTeam": "Você é o único na equipa.", "outpost": "<PERSON><PERSON>", "outside": "Lado de Fora", "oxumsGasStation": "Posto de Gasolina Oxum", "pairing": "a emparelhar", "patrolHelicopter": "Helicóptero de Patrulha", "patrolHelicopterDestroyedSetting": "Quando o Helicóptero de Patrulha for destruído, envia uma notificação.", "patrolHelicopterDetectedSetting": "Quando o Helicóptero de Patrulha for detetado, envia uma notificação.", "patrolHelicopterEntersMap": "O Helicóptero de Patrulha entrou no mapa em {location}.", "patrolHelicopterLeftMap": "O Helicóptero de Patrulha saiu do mapa em {location}.", "patrolHelicopterLeftSetting": "Quando o Helicóptero de Patrulha sair do mapa, envia uma notificação.", "patrolHelicopterLocatedAt": "O Helicóptero de Patrulha está em {location}.", "patrolHelicopterNotCurrentlyOnMap": "O Helicóptero de Patrulha não está no mapa.", "patrolHelicopterTakenDown": "O Helicóptero de Patrulha foi abatido em {location}.", "percentSign": "Sinal de Percentagem", "pipe": "Barra vertical", "playerHasBeenAliveFor": "{name} esteve vivo por {time}.", "playerId": "ID do Jogador", "playerJoinedTheTeam": "{name} junt<PERSON>-se à equipa.", "playerJustConnected": "{name} acabou de conectar.", "playerJustConnectedTo": "{name} acabou de conectar-se a {server}.", "playerJustConnectedTracker": "{name} acabou de conectar-se ao rastreador {tracker}.", "playerJustDied": "{name} acabou de morrer em {location}.", "playerJustDisconnected": "{name} acabou de desconectar.", "playerJustDisconnectedFrom": "{name} acabou de desconectar-se de {server}.", "playerJustDisconnectedTracker": "{name} acabou de desconectar-se do rastreador {tracker}.", "playerJustReturned": "{name} acabou de regressar ({time}).", "playerJustWentAfk": "{name} acabou de ficar AFK.", "playerLeftTheTeam": "{name} sa<PERSON> da equipa.", "playerNotPairedWithServer": "O comando Líder não funciona porque o {name} não está emparelhado com o servidor.", "players": "<PERSON><PERSON><PERSON>", "playersSearch": "Pesquisa de jogadores", "plusSign": "<PERSON><PERSON>", "populationPlayers": "População: ({current}/{max}) jogadores.", "populationQueue": "{number} jogadores na fila.", "powerPlant": "Central Elétrica", "profile": "Perfil", "proxLocation": "{name} está a {distance}m de {caller} na direção {direction}° [{location}]", "quantity": "Quantidade", "questionMark": "Ponto de Interrogação", "ranch": "<PERSON><PERSON><PERSON>", "ratelimited": "RATELIMITED", "reconnectingCap": "A RECONECTAR", "reconnectingToServer": "A RECONECTAR AO SERVIDOR...", "recycle": "Reciclar", "recycleCap": "RECICLAR", "recycler": "Reciclador", "remain": "restantes", "removePlayerCap": "REMOVER JOGADOR", "removeSwitchCap": "REMOVER INTERRUPTOR", "removedSubscribeItem": "O item {name} foi removido da subscrição.", "research": "<PERSON><PERSON><PERSON><PERSON>", "researchTable": "Mesa de Pesquisa", "resetSuccess": "Discord reposto com sucesso.", "responseContainError": "A resposta contém propriedade de erro com o valor: {error}.", "responseIsEmpty": "A resposta está vazia.", "responseIsUndefined": "A resposta não está definida.", "responseTimeout": "Tempo limite atingido enquanto aguardava resposta.", "resultRecycling": "Resultad<PERSON> da reciclagem", "roleCleared": "O cargo do rustplusplus foi removido.", "roleSet": "O cargo do rustplusplus foi definido para {name}.", "rustMonument": "Monumento do Rust", "rustplusOperational": "RUSTPLUS OPERACIONAL.", "safe-zone-recycler": "Reciclador Z<PERSON>gu<PERSON>", "samsite": "Local de Sistema Anti-Aéreo", "satelliteDish": "Antena Parabólica", "scrap": "Sucata", "searchResult": "Resultado da pesquisa para o item: **{name}**", "second": "{second} segundos", "secondCommandDelay": "{second} segundos de atraso do comando.", "seconds": "{seconds} segundos", "secondsCommandDelay": "{seconds} segundos de atraso do comando.", "selectInGamePrefixSetting": "Selecione o prefixo de comando que deve ser usado no jogo:", "selectLanguageExtendSetting": "Certifique-se de executar o **/reset discord** para carregar com sucesso o novo idioma.", "selectLanguageSetting": "Selecione o idioma que o bot usa:", "selectMenuValueChange": "Selecione a Interação do Menu - VerifyId: {id}, Valor: {value}.", "selectTrademarkSetting": "Selecione qual marca registada que deve ser mostrada em cada mensagem no jogo.", "sell": "vender", "semicolon": "Ponto e vírgula", "sentTextToSpeech": "Texto-Para-Fala enviado.", "server": "servidor", "serverId": "ID do Servidor", "serverInfo": "Informação do Servidor", "serverInvalid": "A conexão com o servidor parece ser inválida. Tente re-emparelhar ao servidor.", "serverJustOffline": "O servidor acabou de desconectar-se.", "serverJustOnline": "O servidor acabou de conectar-se.", "serverStatus": "Estado do Servidor", "serviceUnavailable": "Serviço Indisponível: {error}", "setBotLanguage": "Define o idioma do bot para: {language}.", "seven": "<PERSON><PERSON>", "sewerBranch": "<PERSON><PERSON> E<PERSON>goto", "shouldBotBeMutedSetting": "O bot deve ser silenciado dentro do jogo?", "shouldCommandsEnabledSetting": "Os comandos dentro do jogo devem ser habilitados?", "shouldLeaderCommandEnabledSetting": "O comando de líder deve ser ativado?", "shouldLeaderCommandOnlyForPairedSetting": "O comando de líder deve funcionar somente para pessoas que estão emparelhadas com o servidor?", "shouldSmartAlarmNotifyNotConnectedSetting": "O Alarmes Inteligentes deve notificar mesmo que não estejam configurados no servidor de rust conectado?", "shouldSmartAlarmsNotifyInGameSetting": "O Alarmes Inteligentes deve notificar no jogo?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Os Interruptores Inteligentes e os Grupos de Interruptores Inteligentes devem notificar no jogo quando são alterados pelo discord?", "showingBlacklist": "A mostrar a lista negra.", "showingSubscriptionList": "A mostrar a lista de subscrição.", "shredder": "Triturador", "sirenLight": "<PERSON><PERSON>", "six": "<PERSON><PERSON>", "slash": "Barr<PERSON>", "slashCommandInteraction": "Slash Command Interaction - Guild: {guild}, Channel: {channel}, User: {user}, Command: {command}, VerifyId: {id}.", "slashCommandValueChange": "Slash Command Interaction - VerifyId: {id}, Value: {value}.", "slashCommandsSuccessRegister": "Comandos de aplicação registados com sucesso para a guilda: {guildId}.", "slots": "<PERSON><PERSON><PERSON>", "smallOilRig": "Plataforma de Petróleo", "smartAlarm": "Alarme Inteligente", "smartAlarmEditSuccess": "Alarme Inteligente {name} foi editado com sucesso.", "smartAlarmNotifyExtendSetting": "- Estas notificações de alarme usarão o título e a mensagem dada ao Alarme Inteligente dentro do jogo.\n- Estes Alarmes Inteligentes podem não estar disponíveis no canal de texto de alarmes no discord.", "smartDeviceNotFound": "{device} não foi encontrado! Ou foi destruído, ou {user} perdeu o acesso ao Armário de Ferramentas.", "smartSwitch": "Interruptor Inteligente", "smartSwitchAutoDay": "O Interruptor Inteligente só estará ativo durante o dia.", "smartSwitchAutoNight": "O Interruptor Inteligente só estará ativo durante a noite.", "smartSwitchAutoOff": "O Interruptor Inteligente ficará inativo automaticamente durante o ciclo de atualização.", "smartSwitchAutoOffAnyOnline": "O Interruptor Inteligente ficará inativo automaticamente se algum colega de equipa estiver online.", "smartSwitchAutoOffProximity": "O Interruptor Inteligente ficará inativo automaticamente se o colega de equipe estiver perto.", "smartSwitchAutoOn": "O Interruptor Inteligente ficará ativo automaticamente durante o ciclo de atualização.", "smartSwitchAutoOnAnyOnline": "O Interruptor Inteligente ficará ativo automaticamente se algum colega de equipa estiver online.", "smartSwitchAutoOnProximity": "O Interruptor Inteligente ficará ativo automaticamente se o colega de equipe estiver perto.", "smartSwitchEditProximityLabel": "Configuração de Proximidade (metros):", "smartSwitchEditSuccess": "Interruptor Inteligente {name} foi editado com sucesso.", "smartSwitchNormal": "O Interruptor Inteligente funciona normalmente.", "smilyFace": "Cara <PERSON>rri<PERSON>e", "somethingWrongWithConnection": "Ocorreu um problema com a conexão.", "southEast": "Sudeste", "southOfGrid": "Sul da grelha", "southWest": "Sudoeste", "sprinkler": "<PERSON><PERSON><PERSON>", "stackSize": "<PERSON><PERSON><PERSON>", "stackSizeOfItem": "O tamanho da pilha de {item} é {quantity}x.", "status": "Estado", "statusNotConnectedToServer": "**ESTADO** `NÃO CONECTADO AO SERVIDOR!`", "statusNotElectronicallyConnected": "**ESTADO** `NÃO ESTÁ ELETRICAMENTE CONECTADO!`", "statusNotFound": "**ESTADO**: NÃO ENCONTRADO", "steamId": "SteamID", "stoneQuarry": "<PERSON><PERSON><PERSON><PERSON>", "storageMonitor": "Monitor de Armazenamento", "storageMonitorEditSuccess": "Monitor de Armazenamento {name} foi editado com sucesso.", "streamerMode": "Modo Streamer", "subscribeToChangesBattlemetrics": "Subscrever diferentes mudanças no Battlemetrics.", "subscriptionList": "Lista de Subscrições", "subscriptionListEmpty": "Lista de subscrições de itens está vazia.", "sulfurQuarry": "Pedreira de Enxofre", "switches": "Interruptores", "teamMember": "Membro da Equipa", "teamMemberInfo": "Informação do Membro da Equipa", "theDome": "A Cúpula", "theIdOfTheItem": "O ID do item.", "theNameOfTheItem": "O nome do item.", "theNameOfThePlayer": "O nome do jogador.", "three": "<PERSON><PERSON><PERSON><PERSON>", "tilde": "Til", "time": "<PERSON><PERSON>", "timeBeforeCargoEntersEgress": "{time} antes do Navio de Carga em {location} entrar no estágio de saída.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} até a Caixa Bloqueada na Plataforma de Petróleo Grande ({location}) desbloquear.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} até a Caixa Bloqueada na Plataforma de Petróleo ({location}) desbloquear.", "timeCap": "HORA", "timeFormatInvalid": "Formato de hora inválido.", "timeLeftTimer": "{id}: <PERSON><PERSON> restante: {time}, Mensagem: {message}", "timeSinceAlarmWasTriggered": "O alarme {alarm} foi disparado há {time}.", "timeSinceCargoLeft": "{time} desde quando o Navio de Carga saiu do mapa.", "timeSinceChinook47OnMap": "{time} desde quando o <PERSON><PERSON><PERSON> 47 esteve no mapa.", "timeSinceHeavyScientistsOnLarge": "{time} desde a última vez que os Cientistas Pesados foram chamados para a Plataforma de Petróleo Grande.", "timeSinceHeavyScientistsOnSmall": "{time} desde a última vez que os Cientistas Pesados foram chamados para a Plataforma de Petróleo.", "timeSinceLast": "{time} desde o último.", "timeSinceLastEvent": "{time} desde o último evento.", "timeSinceLastSinceDestroyedLong": "{time1} desde a última vez que o Helicóptero de Patrulha esteve no mapa, {time2} desde a última vez que foi derrubado{location}.", "timeSinceLastSinceDestroyedShort": "{time1} desde o último.\n{time2} desde que foi destruido{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} desde quando o Helicóptero de Patrulha esteve no mapa.", "timeSinceTravelingVendorWasOnMap": "{time} desde quando o Vendedor Ambulante esteve no mapa.", "timeSinceWipe": "{time} desde o wipe.", "timeTill": "Tempo até {event}", "timeTillDaylight": "{time} antes do amanhecer.", "timeTillNightfall": "{time} antes do anoitecer.", "timeTillStructureDecay": "{time} antes da decomposição da parede de {type}.", "timeUntilUnlocksAt": "{time} até desbloquear em {location}.", "timer": "Temporizador: {message}.", "timerIdDoesNotExist": "ID do Temporizador: {id} não existe.", "timerIdInvalid": "O ID doTemporizador é inválido.", "timerRemoved": "ID do Temporizador: {id} foi removido.", "timerSet": "Temporizador definido para {time}.", "tokensDidNotReplenish": "Tokens não reabasteceram a tempo.", "toolCupboard": "Armário de Ferramentas", "total": "Total", "tracker": "<PERSON><PERSON><PERSON><PERSON>", "trackerAddPlayerDesc": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> {tracker}", "trackerRemovePlayerDesc": "Remover <PERSON><PERSON><PERSON> {tracker}", "trademarkShownBeforeMessage": "{trademark} será exibido antes das mensagens.", "trainYard": "Estação de Comboios", "travelingVendor": "<PERSON><PERSON><PERSON>", "travelingVendorDetectedSetting": "<PERSON>uan<PERSON> o Vendedor <PERSON>nte for detetado, envia uma notificação.", "travelingVendorHaltedAt": "<PERSON> Vendedor Ambulante parou em {location}.", "travelingVendorHaltedSetting": "<PERSON>uan<PERSON> o Vendedor <PERSON> parar, envia uma notificação.", "travelingVendorLeftSetting": "<PERSON>uando o Vendedor Ambulante sair do mapa, envia uma notificação.", "travelingVendorLocatedAt": "O Vendedor Ambulante está em {location}.", "travelingVendorLeftMap": "O Vendedor Ambulante saiu do mapa em {location}.", "travelingVendorNotCurrentlyOnMap": "O Vendedor Ambulante não está no mapa.", "travelingVendorResumedAt": "O Vendedor Ambulante voltou a andar em {location}.", "travelingVendorSpawnedAt": "O Vendedor Ambulante apareceu em {location}.", "turnOffCap": "DESLIGAR", "turnOnCap": "LIGAR", "turningGroupOnOff": "Tornar o grupo {group} {status}.", "two": "<PERSON><PERSON>", "type": "Tipo", "unavailable": "Indisponível", "underscore": "<PERSON><PERSON><PERSON><PERSON>", "underwater": "Subaquático", "underwaterLab": "Laboratório <PERSON>", "unhandledRejection": "Unhandled Rejection: {error}", "unknown": "Desconhecido", "unknownInteraction": "Interação Desconhecida...", "unmutedCap": "DESSILENCIADO", "updateCap": "ATUALIZAÇÃO", "upkeep": "Custo de manutenção", "upkeepForItem": "O custo de manutenção de {item} é {cost}.", "userAddedToBlacklist": "{user} foi adicionado à lista negra.", "userAlreadyInBlacklist": "{user} já está na lista negra.", "userButtonInteraction": "Interação de botão - Guilda: {guild}, Canal: {channel}, Utilizador: {user}, CustomId: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Interação de Botão - VerifyId: {id} SUCESSO", "userJustConnected": "{name} acabou de conectar.", "userModalInteraction": "Interação Modal - Guilda: {guild}, Canal: {channel}, Utilizador: {user}, CustomId: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Interação Modal - VerifyId: {id} SUCESSO", "userNotInBlacklist": "{user} não está na lista negra.", "userNotRegistered": "{user} não está registado.", "userPartOfBlacklist": "VerifyId: {id}, {user} faz parte da lista negra.", "userPartOfBlacklistDiscord": "Utilizador Bloqueado! Guilda: {guild}, Canal: {channel}, Utilizador: {user}, Mensagem: {message}.", "userPartOfBlacklistInGame": "Utilizador Bloqueado! Utilizador: {user}, Mensagem: {message}.", "userRemovedFromBlacklist": "{user} foi removido da lista negra.", "userSaid": "{user} disse: {text}", "userSelectMenuInteraction": "Interação de Menu de Seleção - Guilda: {guild}, Canal: {channel}, Utilizador: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Interação de Menu de Seleção - VerifyId: {id} SUCESSO", "userTurnedOnOffSmartSwitchFromDiscord": "{user} mudou o Interruptor Inteligente {name} para {status} através do discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} mudou o Grupo de Interruptores Inteligentes {name} para {status} através do discord.", "value": "Valor", "vendingMachine": "Máquina de Venda", "vendingMachineDetectedSetting": "Quando uma nova Máquina de Venda Automática for detetada, envia uma notificação.", "voiceCap": "VOZ", "warningCap": "AVISO", "waterTreatmentPlant": "Estação de Tratamento de Água", "websiteCap": "WEBSITE", "websocketClosedBeforeConnection": "WebSocket fechou antes de a conexão ser estabelecida.", "westOfGrid": "<PERSON>este da grelha", "wipe": "Wipe", "wipeDetected": "Wipe detetado!", "yield": "Rendimento", "youAreAlreadyLeader": "Você j<PERSON> o líder.", "youAreNotPairedWithServer": "O comando Líder não funciona porque você não está emparelhado com o servidor."}