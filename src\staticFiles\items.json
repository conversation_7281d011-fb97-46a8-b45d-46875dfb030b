{"3222790": {"shortname": "attire.hide.helterneck", "name": "<PERSON><PERSON>", "description": "A Halterneck made from the hide of an animal."}, "3380160": {"shortname": "movembermoustachecard", "name": "<PERSON> <PERSON><PERSON>", "description": "A fake moustache for Movember"}, "4384538": {"shortname": "pie.apple", "name": "Apple Pie", "description": "A delicious apple pie, always too hot. Provides a boost to hunger, health and hydration. Gives better night vision, including tree markings and ore hotspots, for a short time."}, "14241751": {"shortname": "arrow.fire", "name": "Fire Arrow", "description": "A fuel soaked arrow, ignite it by aiming."}, "15388698": {"shortname": "barricade.stone", "name": "Stone Barricade", "description": "A protective barricade made out of Stones."}, "20489901": {"shortname": "twitchsunglasses", "name": "Purple Sunglasses", "description": "Sunglasses - Gained from Rust's first Twitch drop event."}, "21402876": {"shortname": "burlap.gloves.new", "name": "<PERSON><PERSON><PERSON>", "description": "Basic burlap gloves, offering protection from the elements"}, "22947882": {"shortname": "white<PERSON><PERSON>", "name": "White ID Tag", "description": "White ID Tag"}, "23352662": {"shortname": "sign.hanging.banner.large", "name": "Large Banner Hanging", "description": "A large banner hanging on a wall."}, "23391694": {"shortname": "hat.bunnyhat", "name": "<PERSON>", "description": "A silly Bunny Hat with wobbly ears to celebrate Easter 2021"}, "28201841": {"shortname": "rifle.m39", "name": "M39 Rifle", "description": "Military grade semi auto rifle"}, "37122747": {"shortname": "keycard_green", "name": "Green Keycard", "description": "A low level clearance card granting access to basic areas."}, "39600618": {"shortname": "microphonestand", "name": "Microphone Stand", "description": "A powered microphone that lets you broadcast your voice. Press [+attack2] to change voice mode between high and low pitch."}, "42535890": {"shortname": "sign.neon.125x215.animated", "name": "Medium Animated Neon Sign", "description": "An animated neon sign!"}, "51984655": {"shortname": "ammo.pistol.fire", "name": "Incendiary Pistol Bullet", "description": "Slower moving ammunition that deals fire damage. There's a small chance it will start a fire."}, "54265286": {"shortname": "pie.crocodile", "name": "Crocodile Pie", "description": "A pie made from crocodile meat, may cause a reptile dysfunction. Boosts hunger, health and hydration. Gives better night vision, including tree markings and ore hotspots, for a short time."}, "60528587": {"shortname": "horse.armor.roadsign", "name": "Roadsign Horse Armor", "description": "A full body armor for a horse. Provides strong protection for you and your steed, at the cost of greatly reduced movement speed."}, "62577426": {"shortname": "photo", "name": "Photograph", "description": "A photograph taken with an instant camera."}, "63265850": {"shortname": "wall.external.high.frontier", "name": "High External Frontier Wall", "description": "A high stone wall used to keep people off your property."}, "69511070": {"shortname": "metal.fragments", "name": "Metal Fragments", "description": "Metal Fragments. Smelted from Metal Ore, used in lots of different crafting recipes."}, "70102328": {"shortname": "redidtag", "name": "Red ID Tag", "description": "Red ID Tag"}, "73681876": {"shortname": "techparts", "name": "Tech Trash", "description": "A collection of random tech parts."}, "81423963": {"shortname": "yellow<PERSON><PERSON>", "name": "Yellow ID Tag", "description": "Yellow ID Tag"}, "86840834": {"shortname": "hazmatsuit_scientist_nvgm", "name": "NVGM Scientist Suit", "description": "A hazmat suit made from radiation resistant rubber."}, "95950017": {"shortname": "metalpipe", "name": "Metal Pipe", "description": "Metal Pipe."}, "97903330": {"shortname": "purecraftingtea_quality", "name": "Pure Crafting Quality Tea", "description": "A pure crafting tea. provides hydration and increases the chances of a higher quality crafting outcome for a short time."}, "98508942": {"shortname": "sign.pictureframe.xxl", "name": "XXL Picture Frame", "description": "A double extra large canvas for artists paintings."}, "99588025": {"shortname": "wall.external.high", "name": "High External Wooden Wall", "description": "A high wooden wall used to keep people off your property."}, "106959911": {"shortname": "frankensteins.monster.01.legs", "name": "Light Frankenstein Legs", "description": "Slightly faster, slightly weaker.\n\nCombined at a Frankenstein Table to create your very own monster."}, "110116923": {"shortname": "metal.facemask.icemask", "name": "Ice Metal Facemask", "description": "A protective facemask which provides the user with excellent head protection from all forms of attacks."}, "120820987": {"shortname": "pie.chicken", "name": "Chicken Pie", "description": "Just like mom used to make. Provides a boost to hunger, health and hydration. Increases the chance of better genes from crops for short time."}, "121049755": {"shortname": "sign.pictureframe.tall", "name": "Tall Picture Frame", "description": "A tall canvas for artists paintings."}, "122783240": {"shortname": "clone.black.berry", "name": "<PERSON>", "description": "A clipping of a Black Berry Plant."}, "140006625": {"shortname": "ptz.cctv.camera", "name": "PTZ CCTV Camera", "description": "A CCTV Camera system can be used for realtime surveillance and security when paired with the Computer Station. This camera has pan, tilt, and zoom capability."}, "143803535": {"shortname": "grenade.f1", "name": "F1 Grenade", "description": "A reliable military grenade that can be thrown a short distance. Deadly at close range on detonation."}, "158303804": {"shortname": "knife.bone.obsidian", "name": "Obsidian Bone Knife", "description": "Melee weapon crafted from obsidian. Good for harvesting carcases."}, "170758448": {"shortname": "vehicle.1mod.cockpit.with.engine", "name": "Cockpit With Engine Vehicle Module", "description": "Single module cockpit for a driver and one passenger, with a small engine."}, "171931394": {"shortname": "stone.pickaxe", "name": "<PERSON>", "description": "Primitive tool used for harvesting Stone, Metal ore and Sulfur ore."}, "174866732": {"shortname": "weapon.mod.8x.scope", "name": "Variable Zoom <PERSON>", "description": "A large military-grade scope that can be configured from 4x zoom to 16x zoom."}, "176787552": {"shortname": "riflebody", "name": "Rifle Body", "description": "The firing mechanism of a rifle. Used in construction of a weapon that can fire 5.56 ammo fully automatic."}, "177226991": {"shortname": "scarecrow", "name": "Scarecrow", "description": "Get into the halloween spirit with this decorative deployable item."}, "180752235": {"shortname": "pink<PERSON><PERSON>", "name": "Pink ID Tag", "description": "Pink ID Tag"}, "184516676": {"shortname": "beehive", "name": "Beehive", "description": "Hand made beehive, place a nucleus inside to raise your own bees and produce honeycomb. Happy bees will make honeycomb more quickly."}, "185586769": {"shortname": "innertube.horse", "name": "Inner Tube", "description": "An inflated tube for aquatic activities."}, "190184021": {"shortname": "kayak", "name": "<PERSON><PERSON>", "description": "An improvised wooden kayak. Seats two and requires a Paddle to operate."}, "192249897": {"shortname": "rockingchair.rockingchair3", "name": "Green", "description": ""}, "196700171": {"shortname": "attire.hide.vest", "name": "Hide Vest", "description": "Simple hide vest, made with common materials. Basic all-round protection."}, "196784377": {"shortname": "improvised.shield", "name": "Improvised Shield", "description": "A makeshift metal shield, sturdy but only protects a small area. Usable with single handed weapons and tools. "}, "198438816": {"shortname": "vending.machine", "name": "Vending Machine", "description": "Trade your goods with other players safely by creating sell and buy orders.  \r\n\r\nIf a raider gains access to the rear panel, they will have free reign over all of your goodies. Keep it safe."}, "200773292": {"shortname": "hammer", "name": "Hammer", "description": "A Hammer, used to upgrade building materials. Right-click for the options. You can also pick up deployed objects while the hammer is equipped."}, "204391461": {"shortname": "coal", "name": "Coal :(", "description": "Crappy Holidays!"}, "204970153": {"shortname": "wrappedgift", "name": "Wrapped Gift", "description": "A lazily wrapped gift."}, "209218760": {"shortname": "head.bag", "name": "Head Bag", "description": "A sack containing the head of a defeated foe. Can be added to trophies."}, "210787554": {"shortname": "iotable", "name": "Engineering Workbench", "description": "This allows you to craft and unlock electrical, water and industrial items"}, "215754713": {"shortname": "arrow.bone", "name": "<PERSON>", "description": "An Arrow equipped with a large bone arrowhead making it very easy to hit targets at the expense of damage"}, "223891266": {"shortname": "tshirt", "name": "T-Shirt", "description": "A tshirt, simple undergarment. Good protection from damage and the elements."}, "236677901": {"shortname": "lumberjack.pickaxe", "name": "Prototype Pickaxe", "description": "A prototype field pickaxe. Useful for gathering ore from rocks."}, "237239288": {"shortname": "pants", "name": "<PERSON>ts", "description": "A pair of pants, highly capable undergarment for your legs. Great all round protection from damage and the elements."}, "240752557": {"shortname": "gunrack_tall.horizontal", "name": "Tall Weapon Rack", "description": "Artfully display your arsenal with a handcrafted wall-mounted weapon rack."}, "242421166": {"shortname": "lightup.large", "name": "Light-Up Frame Large", "description": "A large light up frame"}, "242933621": {"shortname": "frontiermirror.large", "name": "Frontier Mirror Large", "description": "A large wooden frontier themed mirror"}, "248643189": {"shortname": "bigcatmeat.spoiled", "name": "Spoiled Big Cat Meat", "description": "Spoiled Big Cat Meat. Consuming will damage your health."}, "254522515": {"shortname": "largemedkit", "name": "Large Medkit", "description": "A large medkit that heals you to max health over time and stops any bleeding instantly.\n\nGuarantees 100% recovery from the wounded state if placed in your toolbar."}, "261913429": {"shortname": "firework.volcano", "name": "White Volcano Firework", "description": "Emits a beautiful shower of white sparks"}, "263834859": {"shortname": "scraptea", "name": "Basic Scrap Tea", "description": "A basic scrap tea, temporarily increases the amount of scrap you receive from barrels a small amount."}, "268565518": {"shortname": "vehicle.1mod.storage", "name": "Storage Vehicle Module", "description": "Single module storage."}, "271048478": {"shortname": "hat.ratmask", "name": "<PERSON>", "description": "A Beautifully crafted bronze and gold Rat mask to celebrate the 2020 Lunar New Year"}, "273172220": {"shortname": "fun.trumpet", "name": "Plumber's Trumpet", "description": "Toot your own horn with this brassy improvised Trumpet.\r"}, "273951840": {"shortname": "scarecrow.suit", "name": "Scarecrow Suit", "description": "A spooky scarecrow suit"}, "277730763": {"shortname": "halloween.mummysuit", "name": "Mummy Suit", "description": "A mummy suit"}, "281099360": {"shortname": "bread.loaf", "name": "Bread Loaf", "description": "A loaf of bread, eating it provides a boost to health, hunger and hydration. Feeding to a horse will provide a boost to its digestion and dung production for a short time."}, "282103175": {"shortname": "giantlollipops", "name": "Giant Lollipop Decor", "description": "Get into the holiday spirit with these decorative giant lollipops"}, "286193827": {"shortname": "jar.pickle", "name": "Pickles", "description": "Cucumbers in a vinegar bath, jarred by an amateur. Eat at your own risk."}, "286648290": {"shortname": "discofloor", "name": "Disco Floor", "description": "A vibrant flashing floor that pulses in time to music."}, "296519935": {"shortname": "diving.fins", "name": "Diving Fins", "description": "Diving Fins. Significantly boosts your speed underwater but greatly reduces mobility on land."}, "301063058": {"shortname": "wantedposter.wantedposter2", "name": "Wanted Poster 2", "description": "A poster that can display a given player's face as wanted."}, "304481038": {"shortname": "flare", "name": "Flare", "description": "Light up the night sky with this brand new red flare!"}, "309017792": {"shortname": "pie.bigcat", "name": "Big Cat Pie", "description": "A pie made from jungle cat meat, purrfect temperature. Boosts hunger, health and hydration. Shows nearby animal tracks for a short time."}, "317398316": {"shortname": "metal.refined", "name": "High Quality Metal", "description": "High quality metal suitable for armor and weapons construction."}, "320438357": {"shortname": "pie.hunters", "name": "Hunters Pie", "description": "Tasty hunters pie, made with real deer. Provides a boost to hunger, health and hydration. Reduces bleeding for a short time."}, "340210699": {"shortname": "frontiermirror.small", "name": "Frontier Mirror Small", "description": "A small wooden frontier themed mirror"}, "342438846": {"shortname": "fish.anchovy", "name": "<PERSON><PERSON><PERSON>", "description": "A small bony fish commonly found in large schools."}, "343045591": {"shortname": "aiming.module.mlrs", "name": "MLRS Aiming Module", "description": "An aiming system computer module for an MLRS vehicle."}, "349762871": {"shortname": "ammo.grenadelauncher.he", "name": "40mm HE Grenade", "description": "Ammunition for a 40mm Grenade Launcher."}, "352130972": {"shortname": "apple.spoiled", "name": "Rotten Apple", "description": "A rotten apple. Eating it currently provides a tiny boost to health, hunger, and thirst."}, "352321488": {"shortname": "sunglasses", "name": "Sunglasses", "description": "A pair of sunglasses. Functional and stylish!"}, "352499047": {"shortname": "guntrap", "name": "Shotgun Trap", "description": "A shotgun trap triggered by movement, place near doorways and load with handmade shells."}, "355877490": {"shortname": "minigunammopack", "name": "Minigun Ammo Pack", "description": "A backpack designed to store ammunition for a minigun."}, "359723196": {"shortname": "arcade.machine.chippy", "name": "Chippy Arcade Game", "description": "An Arcade Machine"}, "363163265": {"shortname": "hosetool", "name": "Ho<PERSON> Tool", "description": "A tool used to make fluid connections between objects. Aim at an object and click on an input/output handle, then click on another object's input/output handle to form a connection."}, "368008432": {"shortname": "craftingtea_quality", "name": "Basic Crafting Quality Tea", "description": "A basic crafting tea. provides hydration and increases the chances of a higher quality crafting outcome for a short time."}, "377750553": {"shortname": "pureharvestingtea", "name": "Pure Harvesting Tea", "description": "A pure harvesting tea, provides hydration and temporarily increases the amount of resources you receive from harvesting corpses a large amount."}, "390728933": {"shortname": "clone.yellow.berry", "name": "Yellow Berry Clone", "description": "A clipping of a Yellow Berry Plant."}, "392828520": {"shortname": "crocodilemeat.cooked", "name": "Cooked Crocodile Meat", "description": "Delicious Crocodile Meat, Eating it will restore some health, hunger, and thirst."}, "418081930": {"shortname": "wood.armor.jacket", "name": "Wood Chestplate", "description": "A shoddy piece of body armor made from simple materials, provides some basic protection from melee and ranged attacks."}, "442289265": {"shortname": "weapon.mod.holosight", "name": "Holosight", "description": "Uses laser projection to create a holographic sight which is always pointing at the target regardless of view angle."}, "442886268": {"shortname": "rocket.launcher", "name": "Rocket Launcher", "description": "Shoots rockets slightly farther than North Korea."}, "443432036": {"shortname": "fluid.switch", "name": "Fluid Switch & Pump", "description": "A simple switch that enables fluid to pass through. Can be switched on/off manually or via electricity. Can also pump water upwards to higher entities when powered."}, "446206234": {"shortname": "torchholder", "name": "<PERSON><PERSON>", "description": "A wall mounted holder for torches."}, "450531685": {"shortname": "lightupmirror.large", "name": "Light-Up Mirror Large", "description": "A large light-up mirror"}, "468313189": {"shortname": "hazmatsuittwitch", "name": "Twitch Rivals Hazmat Suit", "description": "A purple hazmat suit made from radiation resistant rubber."}, "472505338": {"shortname": "rifle.ak.med", "name": "Medieval Assault Rifle", "description": "Medieval themed high damage machine rifle."}, "476066818": {"shortname": "cassette", "name": "Cassette - Long", "description": "A tape that you can record audio on to using a Cassette Recorder. Stores up to 30s of audio."}, "479143914": {"shortname": "gears", "name": "Gears", "description": "A selection of gears. Some functional, some not."}, "479292118": {"shortname": "halloween.lootbag.large", "name": "Large Loot Bag", "description": "A massive haul. Contains the best halloween loot"}, "486661382": {"shortname": "clantable", "name": "Clan Table", "description": "A table for managing clans."}, "491263800": {"shortname": "hazmatsuit.nomadsuit", "name": "Nomad Suit", "description": "A nomad suit."}, "492357192": {"shortname": "electric.random.switch", "name": "RAND Switch", "description": "This switch will allow passthrough based on a random number. Each time the SET input receives power it will roll true or false to allow passthrough"}, "504109620": {"shortname": "sculpture.ice", "name": "Ice Sculpture", "description": "An block of ice you can sculpt."}, "507284030": {"shortname": "cocoknight.armor.pants", "name": "Coconut Armor Pants", "description": "A shoddy piece of leg armor made from coconut and plants, provides some basic protection from melee and ranged attacks."}, "524678627": {"shortname": "scraptea.advanced", "name": "Advanced Scrap Tea", "description": "An advanced scrap tea, temporarily increases the amount of scrap you receive from barrels a moderate amount."}, "528668503": {"shortname": "flame<PERSON>ret", "name": "<PERSON> Turret", "description": "Automated Flame turret. Requires Low Grade Fuel. Sprays flames when triggered."}, "547862680": {"shortname": "knighttorso.armour", "name": "Knights armour cuirass", "description": "A well made collection of scrap metal formed into a knights armour."}, "550753330": {"shortname": "ammo.snowballgun", "name": "", "description": ""}, "553270375": {"shortname": "electric.battery.rechargable.large", "name": "Large Rechargeable Battery", "description": "A Large Rechargeable Battery. Must have a minimum charge of 5 seconds to discharge. Can be wired in series. Charging rate is dependant on power in, with a maximum of 80% efficiency."}, "553887414": {"shortname": "skull_fire_pit", "name": "Skull Fire Pit", "description": "Enjoy burning your enemies remnants to ashes with this Halloween exclusive! Provides warmth and light, and you can cook with it."}, "553967074": {"shortname": "wallpaper.wall", "name": "Wallpaper Wall", "description": ""}, "559147458": {"shortname": "fishtrap.small", "name": "Survival Fish Trap", "description": "Traps fish, place on the shore and load with bait. The more bait loaded the bigger the chance of catching Trout."}, "567235583": {"shortname": "weapon.mod.small.scope", "name": "8x <PERSON><PERSON>", "description": "A small 8x zoom scope."}, "567871954": {"shortname": "secretlabchair", "name": "Secretlab Chair", "description": "A luxurious, comfortable chair for long sessions of CCTV watching."}, "573676040": {"shortname": "coffin.storage", "name": "<PERSON><PERSON>", "description": "An old wooden coffin, can store up to 42 items"}, "573926264": {"shortname": "semibody", "name": "Semi Automatic Body", "description": "The firing mechanism of a semi automatic weapon."}, "576509618": {"shortname": "fun.boomboxportable", "name": "Portable Boom Box", "description": "A portable Boom Box that can play tapes and internet audio streams. [attack] to start/stop and [attack2] to modify settings."}, "588596902": {"shortname": "ammo.handmade.shell", "name": "Handmade Shell", "description": "Shoddy ammo with multiple projectiles."}, "593465182": {"shortname": "table", "name": "Table", "description": "Every home needs a table. A decorative item which provides comfort when in close proximity."}, "596469572": {"shortname": "rf.detonator", "name": "RF Transmitter", "description": "A hand held RF signal broadcaster. Left click to broadcast. Configurable with right mouse."}, "602628465": {"shortname": "parachute", "name": "Parachute", "description": "Once equipped, press [+jump] while in mid-air to pull the cord. Must be repacked after use. Must be at least 15m above the ground in order to deploy."}, "602741290": {"shortname": "burlap.shirt", "name": "<PERSON><PERSON><PERSON>", "description": "A Shirt made out of burlap."}, "603811464": {"shortname": "maxhealthtea.advanced", "name": "Advanced Max Health Tea", "description": "An advanced health tea, provides hydration and temporarily boosts maximum health a moderate amount."}, "605467368": {"shortname": "ammo.rifle.incendiary", "name": "Incendiary 5.56 Rifle Ammo", "description": "Slower moving ammunition that deals fire damage. There's a small chance it will start a fire."}, "607400343": {"shortname": "legacy.shelter.wood", "name": "Legacy <PERSON> Shelter", "description": "The classic s*** shack. A great starter base for any fresh spawn. You can only have one shelter deployed at anytime."}, "609049394": {"shortname": "battery.small", "name": "Battery - Small", "description": "A Battery."}, "610102428": {"shortname": "industrial.conveyor", "name": "Industrial Conveyor", "description": "Moves an item from one container to another."}, "613961768": {"shortname": "botabag", "name": "Bota <PERSON>", "description": "A canteen used to carry Water. Left click to drink, right click to fill from water sources, or to pour out."}, "615112838": {"shortname": "rail.road.planter", "name": "Rail Road Planter", "description": "A large planter with enough room to plant 9 seeds."}, "621915341": {"shortname": "meat.boar", "name": "Raw Pork", "description": "Raw pork. Eating it will damage your health, try cooking it first."}, "625599716": {"shortname": "metal.shield", "name": "Metal Shield", "description": "A large sturdy metal shield, keeps arrows and bullets at bay. Usable\r with single handed weapons and tools."}, "634478325": {"shortname": "cctv.camera", "name": "CCTV Camera", "description": "A CCTV Camera system can be used for realtime surveillance and security around your base when powered and paired with the Computer Station."}, "642482233": {"shortname": "sticks", "name": "Sticks", "description": "Some long, some short."}, "647240052": {"shortname": "triangle.rail.road.planter", "name": "Triangle Rail Road Planter", "description": "A triangle planter with enough room to plant 4 seeds."}, "649912614": {"shortname": "pistol.revolver", "name": "Revolver", "description": "A standard eight shot revolver."}, "656371026": {"shortname": "carburetor3", "name": "High Quality Carburetor", "description": "A high quality carburetor for a combustion engine. Mixes air and fuel to the proper ratio."}, "656371027": {"shortname": "carburetor2", "name": "Medium Quality Carburetor", "description": "A medium quality carburetor for a combustion engine. Mixes air and fuel to the proper ratio."}, "656371028": {"shortname": "carburetor1", "name": "Low Quality Carburetor", "description": "A low quality carburetor for a combustion engine. Mixes air and fuel to the proper ratio."}, "657352755": {"shortname": "beachtable", "name": "Beach Table", "description": "A small table to hold your drinks while relaxing."}, "665332906": {"shortname": "electric.timer", "name": "Timer", "description": "A Timer switch, will pass power through for duration."}, "671063303": {"shortname": "riot.helmet", "name": "Riot Helmet", "description": "A makeshift riot Helmet. Great at deflecting melee attacks."}, "671706427": {"shortname": "wall.window.bars.toptier", "name": "Reinforced Glass Window", "description": "Window bars to fit a standard window. These bars are made out of reinforced metal and contain a thick surround to protect from incoming projectiles."}, "674734128": {"shortname": "xmas.door.garland", "name": "Festive Doorway Garland", "description": "A Festive decoration to spruce up your  doorway during the holdiays!"}, "678698219": {"shortname": "shotgun.m4", "name": "M4 Shotgun", "description": "A semi automatic military issue shotgun"}, "680234026": {"shortname": "fish.yellowperch", "name": "Yellow Perch", "description": "A Yellow Perch, commonly found in freshwater rivers."}, "695450239": {"shortname": "spear.cny", "name": "Lunar New Year Spear", "description": "A Chinese themed New Year themed metal Spear"}, "696029452": {"shortname": "map", "name": "Paper Map", "description": "Helps you figure out where you are. You can annotate the map by right-clicking and drawing on it.\n\n\nDrag the map into your belt bar to make it active. You will then be able to view the map by holding down the map button (G by default)."}, "699075597": {"shortname": "woodcross", "name": "<PERSON>en Cross", "description": "A wooden cross marking the remains of an unknown soul"}, "703057617": {"shortname": "military flamethrower", "name": "Military Flame Thrower", "description": "A Military grade flamethrower. Uses low grade fuel as ammunition."}, "709206314": {"shortname": "hat.tigermask", "name": "Tiger Mask", "description": "A special tiger mask to celebrate Chinese New Year"}, "721798950": {"shortname": "vehicle.car_radio", "name": "Car Radio", "description": "A small radio to use in cars. Will only work while the car is turned on."}, "722955039": {"shortname": "gun.water", "name": "Water Gun", "description": "A large water gun with pumpable pressure. Hold reload to pump!"}, "723407026": {"shortname": "woodmirror.standing", "name": "<PERSON> Standing", "description": "A standing wooden mirror"}, "734320711": {"shortname": "orchid", "name": "Orchid", "description": "Smells nice."}, "742745918": {"shortname": "industrial.splitter", "name": "Industrial Splitter", "description": "Splits an industrial connection into three separate connections."}, "755224797": {"shortname": "bottle.vodka", "name": "<PERSON><PERSON><PERSON>", "description": "A bottle of Russian Vodka."}, "756125481": {"shortname": "woodmirror.medium", "name": "Wood Mirror Medium", "description": "A medium wood mirror"}, "756517185": {"shortname": "xmas.present.medium", "name": "Medium Present", "description": "A medium present, might be good! Collect 5 to upgrade to a larger present."}, "756890702": {"shortname": "wall.external.high.adobe", "name": "High External Adobe Wall", "description": "A high stone wall used to keep people off your property."}, "762289806": {"shortname": "electric.sirenlight", "name": "Siren Light", "description": "A spinning siren light"}, "782422285": {"shortname": "sofa.pattern", "name": "Sofa - Pattern", "description": "A comfortable old sofa with a floral pattern. A decorative item which provides comfort and seats two."}, "785728077": {"shortname": "ammo.pistol", "name": "Pistol Bullet", "description": "Ammunition for a Pistol. Loses velocity when fired over long distances resulting in slightly decreased damage."}, "789333045": {"shortname": "sunken.knife", "name": "Sunken Combat Knife", "description": "A corroded sunken knife designed for close combat engagements, can attack while sprinting. Best in class at harvesting flesh."}, "794356786": {"shortname": "attire.hide.boots", "name": "<PERSON><PERSON> Boots", "description": "Simple hide boots, made with common materials. Basic all-round protection."}, "794443127": {"shortname": "xmas.tree", "name": "Christmas Tree", "description": "Get into the holiday spirit with this decorative Christmas tree"}, "795236088": {"shortname": "torch", "name": "<PERSON>ch", "description": "A Torch. Lights your way at night and can be used as a weapon."}, "795371088": {"shortname": "shotgun.pump", "name": "Pump Shotgun", "description": "A Shotgun. Fires six rounds."}, "803222026": {"shortname": "box.repair.bench", "name": "<PERSON><PERSON>", "description": "You can repair your items here for a fraction of their construction cost. You may also use this to apply new skins to existing items."}, "803954639": {"shortname": "seed.blue.berry", "name": "Blue Berry Seed", "description": "These blue berry seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more berries and faster growth."}, "809199956": {"shortname": "gravestone", "name": "Gravestone", "description": "A Gravestone marking the remains of an unknown soul"}, "809689733": {"shortname": "mummymask", "name": "Mum<PERSON>", "description": "A classic halloween costume mask which provides decent protection."}, "809942731": {"shortname": "scarecrowhead", "name": "Scarecrow Wrap", "description": "A creepy burlap scarecrow hat"}, "813023040": {"shortname": "wolfmeat.cooked", "name": "Cooked Wolf Meat", "description": "Cooked Wolf Meat. Eating it will restore some health, hunger, and thirst."}, "814297925": {"shortname": "medieval.box.wooden.large", "name": "Medieval Large Wood Box", "description": "Keep your things in this medieval themed wooden storage box. Stores up to 48 items."}, "818733919": {"shortname": "door.hinged.industrial.a", "name": "Industrial Door", "description": "Medium strength door, vulnerable to explosives."}, "818877484": {"shortname": "pistol.semiauto", "name": "Semi-Automatic Pistol", "description": "A semi-automatic pistol, fires rapidly and with good accuracy."}, "821588319": {"shortname": "bicycle", "name": "Bicycle", "description": "A pedal bike."}, "826309791": {"shortname": "sign.post.town.roof", "name": "Two Sided Town Sign Post", "description": "A double sided town sign post, with roof, that you can plant into terrain."}, "830839496": {"shortname": "seed.red.berry", "name": "<PERSON> Berry Seed", "description": "These red berry seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more berries and faster growth."}, "831955134": {"shortname": "skylantern.skylantern.purple", "name": "Sky Lantern - <PERSON>", "description": ""}, "832133926": {"shortname": "wood.armor.pants", "name": "Wood Armor Pants", "description": "A shoddy piece of leg armor made from simple materials, provides some basic protection from melee and ranged attacks."}, "833533164": {"shortname": "box.wooden.large", "name": "Large Wood Box", "description": "Keep your things in this wooden storage box. Stores up to 48 items."}, "835042040": {"shortname": "frankensteins.monster.02.legs", "name": "Medium <PERSON> Legs", "description": "A balanced set of legs.\n\nCombined at a Frankenstein Table to create your very own monster."}, "838308300": {"shortname": "weapon.mod.burstmodule", "name": "<PERSON><PERSON><PERSON>", "description": "A weapon attachment that modifies recoil and rate of fire. Allows weapon to fire in 3 round bursts when turned on. Use [+firemode] to toggle on or off."}, "838831151": {"shortname": "clone.blue.berry", "name": "<PERSON>", "description": "A clipping of a Blue Berry Plant."}, "839738457": {"shortname": "scrapmirror.medium", "name": "Scrap Mirror Medium", "description": "A medium scrap mirror"}, "844440409": {"shortname": "easter.bronzeegg", "name": "Bronze Egg", "description": "A Bronze Egg. Open for a surprise or collect 10 to upgrade to a silver egg."}, "850280505": {"shortname": "bucket.helmet", "name": "Bucket Helmet", "description": "A Bucket Helmet. It provides a level of regional protection from inflicted damage."}, "853471967": {"shortname": "laserlight", "name": "Laser Light", "description": "A small device that shoots out visible lasers in time to music."}, "854447607": {"shortname": "white.berry", "name": "<PERSON>", "description": "A white berry. Can be eaten or used in mixing table recipes to create teas."}, "858486327": {"shortname": "green.berry", "name": "<PERSON> Berry", "description": "A green berry. Can be eaten or used in mixing table recipes to create teas."}, "861513346": {"shortname": "hazmatsuit.lumberjack", "name": "Lumberjack Suit", "description": "A Lumberjack Suit"}, "866332017": {"shortname": "sign.neon.xl", "name": "Large Neon Sign", "description": "A large neon sign!"}, "866889860": {"shortname": "barricade.wood", "name": "Wooden Barricade", "description": "A wooden barricade. Will hurt players and animals that run into it."}, "878301596": {"shortname": "vehicle.module", "name": "Generic vehicle module", "description": "Generic vehicle module"}, "882559853": {"shortname": "spiderweb", "name": "Spider Webs", "description": "Giant Cobwebs which can be hung against both a ceiling and a wall."}, "884424049": {"shortname": "bow.compound", "name": "Compound Bow", "description": "A very powerful bow. Can only be fully drawn while stationary. Keep still for bonus damage and speed."}, "888415708": {"shortname": "electric.rf.receiver", "name": "RF Receiver", "description": "An RF Receiver"}, "895374329": {"shortname": "vehicle.2mod.passengers", "name": "Passenger Vehicle Module", "description": "Dual module seating."}, "912235912": {"shortname": "clone.sunflower", "name": "Sun<PERSON>", "description": "A clipping of a sunflower plant."}, "915408809": {"shortname": "ammo.grenadelauncher.smoke", "name": "40mm Smoke Grenade", "description": "Ammunition for a 40mm Grenade Launcher."}, "920930831": {"shortname": "industrial.wall.light.blue", "name": "Blue Industrial Wall Light", "description": "A small blue light source."}, "924598634": {"shortname": "clone.wheat", "name": "<PERSON><PERSON>", "description": "A Clipping of a Wheat Plant."}, "926800282": {"shortname": "valve2", "name": "Medium Quality Valves", "description": "Medium quality poppet valves for a combustion engine. Valves control the intake and exhaust flow."}, "935606207": {"shortname": "minigun", "name": "Minigun", "description": "A military issue belt-fed machine gun with that must be spun up to fire. Fires rapidly once spun up at the expense of movement speed."}, "935692442": {"shortname": "tshirt.long", "name": "Longsleeve T-Shirt", "description": "Long sleeve tshirt, versatile clothing for protection against damage and the elements."}, "936496778": {"shortname": "floor.grill", "name": "Floor grill", "description": "A floor grill must be placed in floor frames. Perfect for placing above a large furnace."}, "952603248": {"shortname": "weapon.mod.flashlight", "name": "Weapon flashlight", "description": "Attaches to a weapon. Provides user with a beam of light. Pressing F (default) to toggle light."}, "960673498": {"shortname": "huntingtrophylarge", "name": "Large Hunting Trophy", "description": "A large trophy stand to mount the head of killed animals or enemies."}, "962186730": {"shortname": "tincan.alarm", "name": "Tin Can Alarm", "description": "A primitive perimeter alarm made from hanging tin cans. Clatters loudly when disturbed, alerting you to any trespassers. Simple but effective."}, "963906841": {"shortname": "rock", "name": "Rock", "description": "A Rock. The most basic melee weapon and gathering tool."}, "968019378": {"shortname": "clatter.helmet", "name": "<PERSON><PERSON>", "description": "Special helmet for players who own <PERSON><PERSON> and have played for at least 30 minutes, replaces the bucket helmet."}, "968421290": {"shortname": "connected.speaker", "name": "Connected Speaker", "description": "A small speaker that will play any audio from a connected Boom Box."}, "969768382": {"shortname": "reinforced.wooden.shield", "name": "Reinforced Wooden Shield", "description": "A basic wooden shield with added protection, keeps you safe but not for long. Usable with single handed weapons and tools."}, "971362526": {"shortname": "skull.trophy.jar", "name": "Skull Trophy", "description": "A decorative mount that can hold the skull of a friend or foe."}, "975983052": {"shortname": "trophy", "name": "Twitch Rivals Trophy", "description": "A trophy dedicated to the survivors of Rust Twitch Rivals"}, "980333378": {"shortname": "attire.hide.poncho", "name": "<PERSON><PERSON> Poncho", "description": "Primitive armour for the chest made from animal skin. One size fits all. Offers some basic protection from damage and the elements."}, "988652725": {"shortname": "smart.switch", "name": "Smart Switch", "description": "A smart electric switch."}, "989925924": {"shortname": "fish.raw", "name": "Raw Fish", "description": "Raw Fish."}, "996293980": {"shortname": "skull.human", "name": "Human Skull", "description": "A human skull."}, "996757362": {"shortname": "wagon", "name": "Wagon", "description": "A train car."}, "998894949": {"shortname": "seed.corn", "name": "<PERSON><PERSON>", "description": "Corn seeds can be found when picking wild Corn plants. These seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more corn and faster growth."}, "999690781": {"shortname": "geiger.counter", "name": "Geiger Counter", "description": "A geiger counter used by scientists"}, "1004843240": {"shortname": "seed.orchid", "name": "Orchid Seed", "description": "Orchid seeds can be found when picking wild Orchids."}, "1015352446": {"shortname": "submarineduo", "name": "Duo Submarine", "description": "A small two-person submarine."}, "1028889957": {"shortname": "lightupmirror.medium", "name": "Light-Up Mirror Medium", "description": "A medium light-up mirror"}, "1036321299": {"shortname": "bluedogtags", "name": "Blue Dog Tags", "description": "Blue Dog Tags"}, "1046904719": {"shortname": "<PERSON><PERSON><PERSON>", "name": "Abyss Metal Hatchet", "description": "Metal hatchet, reclaimed from the abyss, perfect for survival and crafting"}, "1052926200": {"shortname": "mining.quarry", "name": "Mining Quarry", "description": "Extracts minerals from the ground. Use a Survey Charge to determine the amount of minerals available for extraction in any given area."}, "1055319033": {"shortname": "ammo.grenadelauncher.buckshot", "name": "40mm Shotgun Round", "description": "Ammunition for a 40mm Grenade Launcher."}, "**********": {"shortname": "xmas.lightstring", "name": "Christmas Lights", "description": "A string of colored lights to decorate your home"}, "**********": {"shortname": "sparkplug3", "name": "High Quality Spark Plugs", "description": "High quality spark plugs for a combustion engine. Spark plugs ignite the fuel/air mixture to move the pistons."}, "**********": {"shortname": "syringe.medical", "name": "Medical Syringe", "description": "Heal yourself or others with this syringe. Left-click heals you, right-click heals a target."}, "**********": {"shortname": "attire.nesthat", "name": "Nest Hat", "description": "An extremely silly easter nest hat with googly eyes."}, "**********": {"shortname": "cardtable", "name": "Card Table", "description": "A table for playing card games"}, "**********": {"shortname": "pitchfork", "name": "Pitchfork", "description": "A 3 pronged pitchfork. Extremely easy to hit targets with."}, "**********": {"shortname": "wrappingpaper", "name": "Wrapping Paper", "description": "Use this to conceal any item in a lazily wrapped gift"}, "**********": {"shortname": "bbq", "name": "Barbeque", "description": "A makeshift Barbeque. You can cook significant amounts of food with this."}, "**********": {"shortname": "clothing.mod.armorinsert_metal", "name": "Metal Armor Insert", "description": "Metal insert for crafted clothing and armor, provides extra protection from projectiles."}, "1103488722": {"shortname": "snowballgun", "name": "Snowball Gun", "description": "Load snowballs and lay waste to your foes with Christmas cheer."}, "1104520648": {"shortname": "chainsaw", "name": "Chainsaw", "description": "A Chainsaw. Very effective at cutting trees in addition to soft fleshy objects. Requires Low Grade Fuel to run."}, "1107575710": {"shortname": "hazmatsuit_scientist_arctic", "name": "Arctic Scientist Suit", "description": "A hazmat suit made from radiation resistant rubber."}, "1110385766": {"shortname": "metal.plate.torso", "name": "Metal Chest Plate", "description": "A metal plate covering your torso, provides excellent protection from all forms of attacks."}, "1112162468": {"shortname": "blue.berry", "name": "Blue Berry", "description": "A blue berry. Can be eaten or used in mixing table recipes to create teas."}, "1113514903": {"shortname": "attackhelicopter", "name": "Attack Helicopter", "description": "A fairly solid helicopter with weapons capability."}, "1115193056": {"shortname": "wall.frame.lunar2025_a", "name": "Lunar <PERSON>", "description": "A Lunar New Year themed decorative wall divider with geometric inlay"}, "**********": {"shortname": "purecoolingtea", "name": "Pure Cooling Tea", "description": "A pure cooling tea that temporarily decreases your max and core temperature."}, "**********": {"shortname": "candycane", "name": "<PERSON>", "description": "A very old candy cane from 2014"}, "**********": {"shortname": "fish.spoiled", "name": "Spoiled Fish Meat", "description": "Spoiled Fish Meat. Consuming will damage your health."}, "**********": {"shortname": "gunrack_stand", "name": "Weapon Rack Stand", "description": "Holds weaponry haphazardly, a charmingly chaotic addition to your virtual armory."}, "**********": {"shortname": "ceilinglight", "name": "Ceiling Light", "description": "A small ceiling mounted light source."}, "**********": {"shortname": "catapult", "name": "Catapult", "description": "An improvised catapult siege weapon. Built for flinging destructive projectiles with questionable accuracy. Can be towed by horses."}, "**********": {"shortname": "storage.monitor", "name": "Storage Monitor", "description": "The Storage Monitor attaches to the Tool Cupboard and Large Storage Box to monitor the container contents. Output sends a pulse when the container is updated."}, "**********": {"shortname": "sign.wooden.large", "name": "Large Wooden Sign", "description": "A 3-meter by 1.5-meter wooden sign that you can write on."}, "**********": {"shortname": "crankshaft2", "name": "Medium Quality Crankshaft", "description": "A medium quality crankshaft for a combustion engine. Converts piston movement into rotational motion."}, "1158340332": {"shortname": "crankshaft3", "name": "High Quality Crankshaft", "description": "A high quality crankshaft for a combustion engine. Converts piston movement into rotational motion."}, "**********": {"shortname": "crankshaft1", "name": "Low Quality Crankshaft", "description": "A low quality crankshaft for a combustion engine. Converts piston movement into rotational motion."}, "**********": {"shortname": "lock.code", "name": "Code Lock", "description": "An electronic lock. Locked and unlocked with four-digit code."}, "**********": {"shortname": "hitchtroughcombo", "name": "Hitch & Trough", "description": "A Hitching post and Trough. Dismounting your horses here will keep them healthy and fed as long as it is kept stocked with food."}, "**********": {"shortname": "metal.detector", "name": "Metal Detector", "description": "Detects metal objects which may otherwise be hidden. When the light flashes, or you hear a beep, aim to search for items. Increase the bar until a flag appears. Dig at the flag with a melee item to reveal what it is. Different areas yield different rewards"}, "**********": {"shortname": "grenade.bee", "name": "Bee Grenade", "description": "A fragile glass jar full of bees. Breaking upon impact, the bees will find the nearest player to attack. Careful where you throw it!"}, "**********": {"shortname": "electric.andswitch", "name": "AND Switch", "description": "A logic gate that allows electrical passthrough if BOTH inputs receives power, passthrough amount is the greater of either power source"}, "**********": {"shortname": "cupboard.tool.shockbyte", "name": "Shockbyte Tool Cupboard", "description": "Ensure 100% Tool Cupboard uptime with this Tool Cupboard skin."}, "1176355476": {"shortname": "concretehatchet", "name": "<PERSON><PERSON><PERSON>", "description": "A salvaged piece of concrete and rebar which can be used to harvest wood"}, "1177596584": {"shortname": "elevator", "name": "Elevator", "description": "A powered elevator. Place another elevator above or below this to connect it. Requires power."}, "1178325727": {"shortname": "wheat", "name": "Wheat", "description": "A bundle of wheat. Edible, but better off in a cooking recipe."}, "1181207482": {"shortname": "heavy.plate.helmet", "name": "Heavy Plate Helmet", "description": "Offers superior protection at the cost of reduced vision and movement speed."}, "1184215560": {"shortname": "spoiled.produce", "name": "Spoiled Produce", "description": "A spoiled fruit or vegetable. It's so far gone it's hard to tell what it originally was."}, "1186655046": {"shortname": "vehicle.2mod.fuel.tank", "name": "Fuel Tank Vehicle Module", "description": "Dual module large fuel tank."}, "1189981699": {"shortname": "cratecostume", "name": "Crate Costume", "description": "A sneaky crate costume. Use with a friend to expertly troll other players"}, "1199391518": {"shortname": "roadsigns", "name": "Road Signs", "description": "Some road signs made of metal."}, "1205084994": {"shortname": "photoframe.large", "name": "Large Photo Frame", "description": "A large frame for your photos."}, "1205607945": {"shortname": "sign.hanging", "name": "Two Sided Hanging Sign", "description": "A double sided hanging sign, to attach to buildings."}, "1221063409": {"shortname": "door.double.hinged.toptier", "name": "Armored Double Door", "description": "Extremely strong door with a hatch to see and shoot out of."}, "1223729384": {"shortname": "lavenderidtag", "name": "Lavender ID Tag", "description": "Lavender ID Tag"}, "1223900335": {"shortname": "dogtagneutral", "name": "Dog Tag", "description": "Neutral Dog Tag"}, "1230323789": {"shortname": "smgbody", "name": "SMG Body", "description": "The firing mechanism of a submachinegun. Used in construction of a weapon that can fire pistol ammo fully automatic."}, "1230691307": {"shortname": "captainslog", "name": "Captain's Log", "description": "A deteriorating piece of paper with information relating to the last moments of this vessel."}, "1234878710": {"shortname": "telephone", "name": "Telephone", "description": "Use the telephone to call other telephones on the island!"}, "1234880403": {"shortname": "sewingkit", "name": "Sewing <PERSON>", "description": "A Sewing kit. Used to make advanced clothing."}, "1242482355": {"shortname": "jack<PERSON><PERSON><PERSON>.angry", "name": "<PERSON>", "description": "A Lantern. Place it where you need light."}, "1242522330": {"shortname": "cursedcauldron", "name": "Cursed <PERSON>", "description": "A spooky cast iron cauldron with a green goo."}, "1248356124": {"shortname": "explosive.timed", "name": "Timed Explosive Charge", "description": "C4, useful for breaking into bases."}, "1258768145": {"shortname": "sunglasses02black", "name": "Sunglasses", "description": "A pair of sunglasses. Functional and stylish!"}, "1259919256": {"shortname": "mixingtable", "name": "Mixing Table", "description": "Used for mixing recipes."}, "1263920163": {"shortname": "grenade.smoke", "name": "Smoke Grenade", "description": "Produces a large cloud of opaque greyish white smoke."}, "1266491000": {"shortname": "hazmatsuit", "name": "Hazmat Suit", "description": "A hazmat suit made from radiation resistant rubber."}, "1268178466": {"shortname": "industrial.wall.light.green", "name": "Green Industrial Wall Light", "description": "A small green light source."}, "**********": {"shortname": "red.berry", "name": "<PERSON> Berry", "description": "A red berry. Can be eaten or used in mixing table recipes to create teas."}, "**********": {"shortname": "piano", "name": "Wheelbarrow Piano", "description": "The Piano is a repurposed wheelbarrow that weighs a ton, but makes up for it with a wide range of notes. "}, "**********": {"shortname": "humanmeat.spoiled", "name": "Spoiled Human Meat", "description": "Spoiled Human Meat. Consuming will damage your health."}, "**********": {"shortname": "weaponrack.doublelight", "name": "Weapon Rack Double Light", "description": "A weapon rack light."}, "**********": {"shortname": "electric.xorswitch", "name": "XOR Switch", "description": "An exclusive-or logic gate that allows electrical passthrough if ONLY ONE input receives power, passthrough amount is whichever single input is active. If BOTH inputs recieve power, passthrough will be zero."}, "**********": {"shortname": "ammo.rocket.seeker", "name": "Homing Missile", "description": "Fast moving rocket ammunition. Uses a type of radar for active target guidance from a Homing Launcher"}, "**********": {"shortname": "sign.neon.125x125", "name": "Small Neon Sign", "description": "A small neon sign!"}, "**********": {"shortname": "storage_barrel_b", "name": "Storage Barrel Vertical", "description": "Keep your things in this wooden barrel. Stores up to 48 items. Cannot be locked."}, "**********": {"shortname": "woodmirror.large", "name": "Wood Mirror Large", "description": "A large wooden mirror"}, "1312843609": {"shortname": "skull", "name": "Skull", "description": "A spooky skull"}, "1315082560": {"shortname": "hat.oxmask", "name": "Ox Mask", "description": "An Ox mask to celebrate the 2021 Lunar New Year."}, "1318558775": {"shortname": "smg.mp5", "name": "MP5A4", "description": "A medium damage machine gun."}, "1319617282": {"shortname": "halloween.lootbag.small", "name": "Small Loot Bag", "description": "Open for a surprise, or collect 10 to upgrade to a medium loot bag containing better loot!"}, "1324203999": {"shortname": "firework.boomer.champagne", "name": "Champagne Boomer", "description": "A very large mortar type firework with an massive champagne colored explosion followed by smaller orange starbursts"}, "1326180354": {"shortname": "salvaged.sword", "name": "Salvaged Sword", "description": "A powerful melee weapon made from a filed-down wrench. Great durability, good damage."}, "1327005675": {"shortname": "wall.ice.wall", "name": "Short Ice Wall", "description": "A short wall made of solid ice."}, "1330084809": {"shortname": "valve1", "name": "Low Quality Valves", "description": "Low quality poppet valves for a combustion engine. Valves control the intake and exhaust flow."}, "**********": {"shortname": "pumpkinbasket", "name": "<PERSON><PERSON><PERSON> Basket", "description": "A pumpkin basket to help you collect candy much faster during Trick or Treat events with left click. Use right click to throw candy"}, "**********": {"shortname": "bearmeat.spoiled", "name": "Spoiled Bear Meat", "description": "Spoiled Bear Meat. Consuming will damage your health."}, "**********": {"shortname": "jungle.rock", "name": "Jungle Rock", "description": "A Jungle Rock. The most basic melee weapon and gathering tool."}, "**********": {"shortname": "door.hinged.toptier", "name": "Armored Door", "description": "Extremely strong door with a hatch to see and shoot out of."}, "**********": {"shortname": "snowmachine", "name": "Snow Machine", "description": "A machine which will blanket the surrounding terrain in snow."}, "**********": {"shortname": "minecart.planter", "name": "Minecart Planter", "description": "A small planter with enough room to plant 2 seeds."}, "**********": {"shortname": "blueid<PERSON>", "name": "Blue ID Tag", "description": "Blue ID Tag"}, "**********": {"shortname": "goldmirror.large", "name": "Gold Mirror large", "description": "A large gold mirror"}, "**********": {"shortname": "burlap.gloves", "name": "<PERSON><PERSON> Gloves", "description": "Gloves made out of leather, offers a small amount of protection to the upper body."}, "**********": {"shortname": "corn", "name": "Corn", "description": "An ear of corn. Eating it provides a small boost to health, hunger, and thirst."}, "**********": {"shortname": "electric.teslacoil", "name": "Tesla Coil", "description": "An electrical trap that zaps nearby players when powered. More power supplied will result in higher damage. Loses durability with use."}, "**********": {"shortname": "barricade.wood.cover", "name": "Wooden Barricade Cover", "description": "Perfect for cover when engaging in gun fights. Decays rapidly when placed outside of building privilege."}, "**********": {"shortname": "pistol.python", "name": "Python Revolver", "description": "A High Powered six-shooter."}, "**********": {"shortname": "vehicle.1mod.rear.seats", "name": "Rear Seats Vehicle Module", "description": "Single module seating for two passengers."}, "**********": {"shortname": "leather", "name": "Leather", "description": "Leather from an animal. Used in many clothing items and more."}, "**********": {"shortname": "barricade.woodwire", "name": "Barbed Wooden Barricade", "description": "A wooden barricade with barbed wire, made of Wood and Metal Fragments. The barbed wire causes damage and will also slow player movement."}, "1390353317": {"shortname": "door.double.hinged.metal", "name": "Sheet Metal Double Door", "description": "Medium strength door, vulnerable to explosives."}, "1391703481": {"shortname": "meat.pork.burned", "name": "Burnt Pork", "description": "This little piggy has been in the oven for too long."}, "1394042569": {"shortname": "rhib", "name": "RHIB", "description": ""}, "1397052267": {"shortname": "supply.signal", "name": "Supply Signal", "description": "Purple signal smoke. Use to mark a location for an airdrop."}, "1400460850": {"shortname": "horse.saddlebag", "name": "Saddle bag", "description": "Equipping this saddle bag will add extra storage space on your horse"}, "1401987718": {"shortname": "ducttape", "name": "Duct <PERSON>", "description": "A versatile adhesive tape reinforced with cloth."}, "1409529282": {"shortname": "door.closer", "name": "Door Closer", "description": "Automatically closes your doors when left open."}, "1412103380": {"shortname": "seed.sunflower", "name": "Sunflower Seed", "description": "Sunflower seeds can be found when picking wild Sunflowers."}, "1413014235": {"shortname": "fridge", "name": "<PERSON><PERSON>", "description": "A fridge you can store food into!"}, "1414245162": {"shortname": "note", "name": "Note", "description": "A scrap of paper for leaving notes."}, "1414245519": {"shortname": "rose", "name": "<PERSON>", "description": "Romantic AF."}, "**********": {"shortname": "rope", "name": "<PERSON><PERSON>", "description": "A Length of Rope."}, "**********": {"shortname": "horse.costume", "name": "Horse Costume", "description": "An inflatable horse costume. Acting as a replacement for wooden pants, you can equip this item to look as silly as possible."}, "**********": {"shortname": "deermeat.raw", "name": "Raw Deer Meat", "description": "Raw Deer Meat. Eating it will damage your health, try cooking it first."}, "**********": {"shortname": "bucket.water", "name": "Water Bucket", "description": "A bucket for transporting water or other liquids."}, "**********": {"shortname": "cocoknight.armor.torso", "name": "Coconut Armor Chestplate", "description": "A shoddy piece of body armor made from coconut and plants, provides some basic protection from melee and ranged attacks."}, "**********": {"shortname": "minihelicopter.repair", "name": "Minicopter", "description": ""}, "**********": {"shortname": "hopper", "name": "<PERSON>", "description": "Will suck up any dropped items in it's radius while powered"}, "**********": {"shortname": "industrial.crafter", "name": "Industrial Crafter", "description": "Attaches to a workbench to allow automated crafting."}, "**********": {"shortname": "bow.hunting", "name": "Hunting Bow", "description": "Hunting Bow, useful for short to medium range combat and hunting."}, "**********": {"shortname": "wall.frame.fence.gate", "name": "Chainlink Fence Gate", "description": "A Chainlink Fence Gate."}, "**********": {"shortname": "cookingworkbench", "name": "Cooking Workbench", "description": "Used to cook advanced recipes for food, fuel, ammunition and explosives."}, "**********": {"shortname": "wantedposter.wantedposter4", "name": "Wanted Poster 4", "description": "A poster that can display a given player's face as wanted."}, "**********": {"shortname": "pie.pork", "name": "Pork Pie", "description": "Tempting pork pie, provides a boost to hunger, health and hydration. Improves healing rate from other sources for a short time."}, "**********": {"shortname": "weapon.mod.muzzlebrake", "name": "Muzzle Brake", "description": "Lowers recoil felt by the operator but can make bullet path more unpredictable and dealing slightly less damage to your foes."}, "**********": {"shortname": "oretea", "name": "Basic Ore Tea", "description": "A basic ore tea, provides hydration and temporarily increases your yield from harvesting ores a small amount."}, "**********": {"shortname": "cupboard.tool.retro", "name": "Retro Tool Cupboard", "description": "Transform your tool cupboard experience with this modified Hi-Fi cabinet. Complete with retro displays indicating vital base data as well as frosted glass doors allowing you to view its contents of tools and resources!"}, "**********": {"shortname": "jackhammer", "name": "<PERSON><PERSON>", "description": "A Pneumatic Jackhammer to blast through ore nodes. The sheer power of this device requires no aiming as all weak spots will be instantly destroyed. Works well on human flesh. Can be refilled at a Workbench."}, "1491189398": {"shortname": "paddle", "name": "Paddle", "description": "A versatile melee weapon that can also be used to paddle a kayak."}, "1491753484": {"shortname": "frankensteins.monster.02.torso", "name": "Medium <PERSON>", "description": "A balanced torso.\n\nCombined at a Frankenstein Table to create your very own monster."}, "1494014226": {"shortname": "discord.trophy", "name": "Discord Trophy", "description": "A companion that reminds you of your youth."}, "1512054436": {"shortname": "clone.potato", "name": "Potato Clone", "description": "A Clipping of a Potato Plant."}, "1516531815": {"shortname": "harvestingtea", "name": "Basic Harvesting Tea", "description": "A basic harvesting tea, provides hydration and temporarily increases the amount of resources you receive from harvesting corpses a small amount."}, "1516985844": {"shortname": "wall.frame.netting", "name": "Netting", "description": "Netting you can climb."}, "1521286012": {"shortname": "sign.post.double", "name": "Double Sign Post", "description": "A double sign post that you can plant into terrain."}, "1523195708": {"shortname": "targeting.computer", "name": "Targeting Computer", "description": "A computer loaded with software that can analyze a video and produce rotational deltas to individual objects contained in the feed."}, "1523403414": {"shortname": "cassette.short", "name": "Cassette - Short", "description": "A tape that you can record audio on to using a Cassette Recorder. Stores up to 10s of audio."}, "1524187186": {"shortname": "workbench1", "name": "Workbench Level 1", "description": "This allows you to craft and unlock items requiring workbench level 1"}, "1524980732": {"shortname": "carvable.pumpkin", "name": "Carvable <PERSON>", "description": "A pumpkin that you can carve. \n\nRequires wood as a fuel source."}, "1525520776": {"shortname": "building.planner", "name": "Building Plan", "description": "A building plan. You can use this to craft buildings - right click when equipped for more options."}, "**********": {"shortname": "clone.white.berry", "name": "<PERSON>", "description": "A clipping of a White Berry Plant."}, "**********": {"shortname": "chair", "name": "Chair", "description": "Every home needs a chair. A decorative item which provides comfort while seated."}, "**********": {"shortname": "humanmeat.cooked", "name": "Cooked Human Meat", "description": "Salty meat from a human being, eating this will restore some hunger and health but reduces hydration. Maybe you should look for a better food source."}, "**********": {"shortname": "industrial.combiner", "name": "Industrial Combiner", "description": "Combines three separate industrial connections into one connection."}, "**********": {"shortname": "spear.wooden", "name": "<PERSON><PERSON>", "description": "Primitive weapon, perfect for hunting your foes. Can be upgraded to Stone Spear."}, "**********": {"shortname": "sign.post.single", "name": "Single Sign Post", "description": "A single sign post that you can plant into terrain."}, "**********": {"shortname": "rifle.ak", "name": "Assault Rifle", "description": "High damage machine rifle."}, "**********": {"shortname": "apple", "name": "Apple", "description": "An Apple. Eating it provides a small boost to health, hunger, and thirst."}, "**********": {"shortname": "bleach", "name": "Bleach", "description": "A powerful oxidizing agent. Can be used as a disinfectant."}, "**********": {"shortname": "grenade.molotov", "name": "<PERSON><PERSON><PERSON> Cocktail", "description": "A glass bottle filled with low grade fuel. Smashes and spreads fire when it lands, incredibly high damage to wood and players but innefective against stronger materials."}, "**********": {"shortname": "sunglasses02red", "name": "Sunglasses", "description": "A pair of sunglasses. Functional and stylish!"}, "**********": {"shortname": "vehicle.1mod.engine", "name": "Engine Vehicle Module", "description": "Single module large engine."}, "**********": {"shortname": "horse.saddle.single", "name": "Single Horse Saddle", "description": "A horse saddle with one seat. Having this item allows you to claim a horse in a stable by holding E and selecting the \"Claim\" option"}, "**********": {"shortname": "diverpicka<PERSON>", "name": "Abyss Metal Pickaxe", "description": "Plucked from the abyss, this Metal Pickaxe is a prized possession for any survivor"}, "1568388703": {"shortname": "diesel_barrel", "name": "Diesel Fuel", "description": "Suitable fuel to power quarries and the giant excavator."}, "1569882109": {"shortname": "fishingrod.handmade", "name": "Handmade Fishing Rod", "description": "Use [attack2] to cast out the line and wait for a bite. Requires bait."}, "1572152877": {"shortname": "mintid<PERSON>", "name": "Mint ID Tag", "description": "Mint ID Tag"}, "1575635062": {"shortname": "frankensteintable", "name": "Frankenstein Table", "description": "Place a monsters head, torso and legs into the table to summon Frankensteins monster."}, "1578317134": {"shortname": "hazmat.plushy", "name": "Haz<PERSON>", "description": "A plushy of your favourite outfit made by the fabulous people at YouTooz.com"}, "1581210395": {"shortname": "planter.large", "name": "Large Planter Box", "description": "A large planter with enough room to plant 9 seeds."}, "1588298435": {"shortname": "rifle.bolt", "name": "Bolt Action Rifle", "description": "A High powered, highly accurate, long range rifle."}, "1588492232": {"shortname": "drone", "name": "Drone", "description": "A remote controlled drone operated from the Computer station with a limited range. Easily damaged from impacts. "}, "1601468620": {"shortname": "jumpsuit.suit.blue", "name": "Blue Jumpsuit", "description": "A Blue Jumpsuit"}, "1601800933": {"shortname": "honey", "name": "<PERSON><PERSON> of Honey", "description": "A jar of honey. Good for reducing hunger and radiation, or could be saved for recipes. The source of the glass jar is a mystery."}, "1602646136": {"shortname": "spear.stone", "name": "<PERSON>pear", "description": "A stone-tipped wooden Spear. Slightly longer range."}, "1603174987": {"shortname": "confetticannon", "name": "Confetti Cannon", "description": "Blast a cloud of birthday themed confetti cheer."}, "1604837581": {"shortname": "wooden.shield", "name": "Wooden Shield", "description": "A basic wooden shield, won't last very long. Usable with single handed weapons and tools."}, "1608640313": {"shortname": "shirt.tanktop", "name": "Tank Top", "description": "A tank top, comes with free stains. Basic protection, but better than nothing."}, "1614528785": {"shortname": "frankensteins.monster.03.torso", "name": "Heavy <PERSON>", "description": "Slightly slower, slightly stronger.\n\nCombined at a Frankenstein Table to create your very own monster."}, "1619039771": {"shortname": "electric.digitalclock", "name": "Digital Clock", "description": "A digital clock that displays the server time and lets you set alarms to pass power through when ringing. Ideal for synchronizing traps or timed events."}, "1621942085": {"shortname": "outbreak.sprayer", "name": "Outbreak Sprayer", "description": "An outbreak sprayer used by scientists"}, "1623701499": {"shortname": "industrial.wall.light", "name": "Industrial Wall Light", "description": "A small light source."}, "1629293099": {"shortname": "snowman", "name": "Snowman", "description": "Get into the holiday spirit with this decorative snowman"}, "1629564540": {"shortname": "wallpaper.tool", "name": "Wallpaper Tool", "description": "Transform your base from a rusty shack to a cozy hideout! This tool lets you apply wallpaper on walls, floors and ceilings. A nice touch of style - until the next raid, at least.\n\nPress [+attack2] to select your skin."}, "1638322904": {"shortname": "ammo.rocket.fire", "name": "Incendiary Rocket", "description": "Creates flames upon impact."}, "1643667218": {"shortname": "sign.neon.xl.animated", "name": "Large Animated Neon Sign", "description": "A large, animated neon sign!"}, "1655650836": {"shortname": "barricade.metal", "name": "Metal Barricade", "description": "A tough metal barricade with barbed wire, made from Metal Fragments. The barbed wire causes damage and will also slow player movement."}, "1655979682": {"shortname": "can.beans.empty", "name": "Empty Can Of Beans", "description": "An empty Can of Beans. Can be smelted into Metal Fragments."}, "1658229558": {"shortname": "lantern", "name": "Lantern", "description": "A Lantern. Place it where you need light, requires low grade fuel to work."}, "1659114910": {"shortname": "hat.gas.mask", "name": "Gas Mask", "description": "A Gas Mask"}, "1659447559": {"shortname": "horse.armor.wood", "name": "Wooden Horse Armor", "description": "A full body armor for a horse made of wood. Provides moderate protection for you and your steed, but with a reduction in movement speed."}, "**********": {"shortname": "yellow.berry", "name": "Yellow Berry", "description": "A yellow berry. Can be eaten or used in mixing table recipes to create teas."}, "**********": {"shortname": "fish.cooked", "name": "Cooked Fish", "description": "Cooked Fish. Eating it provides a small boost to health, hunger, and thirst."}, "**********": {"shortname": "stocking.small", "name": "Small Stocking", "description": "A small stocking. Hang it by a fire and you might just get a gift! Collect 6 to upgrade to a SUPER Stocking."}, "**********": {"shortname": "hat.beenie", "name": "<PERSON><PERSON>", "description": "A wooly hat, offering some basic protection from damage and cold for your head."}, "**********": {"shortname": "boomerang", "name": "Boomerang", "description": "Can be used a melee weapon or as a throwable. Once thrown (with right click) it travels in an arc before coming back to you."}, "**********": {"shortname": "xmas.decoration.gingerbreadmen", "name": "Decorative Gingerbread Men", "description": "Decorative Gingerbread Men you can hang on your Christmas tree"}, "**********": {"shortname": "lightupframe.small", "name": "Light-Up Frame Small", "description": "A small light-up frame"}, "**********": {"shortname": "modularcarlift", "name": "Modular Car Lift", "description": "This allows you to modify modular vehicles"}, "1697996440": {"shortname": "photoframe.landscape", "name": "Landscape Photo Frame", "description": "A landscape oriented canvas for artists paintings."}, "**********": {"shortname": "bone.club", "name": "Bone Club", "description": "A blunt weapon made from a femur. All around basic gathering tool."}, "**********": {"shortname": "ammo.rifle.hv", "name": "HV 5.56 Rifle Ammo", "description": "This ammunition travels faster, resulting in less drop and slightly higher damage when fired over long distances."}, "**********": {"shortname": "maxhealthtea.pure", "name": "Pure Max Health Tea", "description": "A pure health tea, provides hydration and temporarily boosts maximum health a large amount."}, "**********": {"shortname": "hat.candle", "name": "Candle Hat", "description": "A hat with a candle on top. You can turn the candle on in your inventory."}, "**********": {"shortname": "ballista.static", "name": "Ballista", "description": "A stationary ballista, perfect for defence. Can fire deadly bolts at distant targets."}, "**********": {"shortname": "weapon.mod.targetingattachment", "name": "Targeting Attachment", "description": "An internal weapon mod that allows the user to send pings to their team while the weapon is equipped."}, "**********": {"shortname": "bone.fragments", "name": "Bone Fragments", "description": "Extracted from animal carcass.  Used as a raw material to make bone weapons and armour."}, "**********": {"shortname": "attire.hide.pants", "name": "<PERSON><PERSON>", "description": "Trousers made from the hide of an animal. Moderate protection against animal attacks."}, "**********": {"shortname": "xmas.decoration.lights", "name": "Tree Lights", "description": "A tree isn't complete without colorful lights!"}, "1729120840": {"shortname": "door.hinged.wood", "name": "Wooden Door", "description": "A Cheap door to secure your base. Its vulnerability to fire and weak explosive resistance makes the door a temporary solution to securing your base. Due to its flaws you should look at upgrading to a higher tier door such as Sheet Metal.\n\nThe Wooden Door can take two kinds of locks the basic Key Lock and the Code Lock. To pick up the door, remove any locks and open, hold down the E (USE) key and select 'Pickup'."}, "1729374708": {"shortname": "oretea.pure", "name": "Pure Ore Tea", "description": "A pure ore tea, provides hydration and temporarily increases your yield when harvesting ores a large amount."}, "1729712564": {"shortname": "photoframe.portrait", "name": "Portrait Photo Frame", "description": "A portrait oriented canvas for artists paintings."}, "1730664641": {"shortname": "wallpaper.ceiling", "name": "Wallpaper Ceiling", "description": ""}, "1735402444": {"shortname": "discofloor.largetiles", "name": "Disco Floor", "description": "A vibrant flashing floor that pulses in time to music."}, "1744298439": {"shortname": "firework.boomer.blue", "name": "<PERSON>", "description": "A very large mortar type firework with a blue starburst"}, "1746956556": {"shortname": "bone.armor.suit", "name": "Bone Armor", "description": "A primitive suit of armor made of bones, weak protection from damage and the elements but better than nothing."}, "1751045826": {"shortname": "hoodie", "name": "<PERSON><PERSON>", "description": "A hoodie."}, "1757265204": {"shortname": "easter.silveregg", "name": "Silver Egg", "description": "A Silver Egg. Contains medium level loot, Collect 10 to upgrade to a gold Egg"}, "1758333838": {"shortname": "rockingchair.rockingchair2", "name": "<PERSON><PERSON>", "description": ""}, "1762167092": {"shortname": "<PERSON><PERSON><PERSON>", "name": "Green ID Tag", "description": "Green ID Tag"}, "1768112091": {"shortname": "snowmobiletomaha", "name": "Tomaha Snowmobile", "description": "An old snowmobile from around 1980."}, "1769475390": {"shortname": "woodframe.standing", "name": "<PERSON>", "description": "A standing wooden frame"}, "1770475779": {"shortname": "worm", "name": "<PERSON><PERSON>", "description": "A small, writhing worm that can be used for bait. Could also be eaten in desperate situations."}, "1770744540": {"shortname": "vehicle.chassis", "name": "Generic vehicle chassis", "description": "Generic vehicle chassis"}, "1771755747": {"shortname": "black.berry", "name": "<PERSON> Berry", "description": "A black berry."}, "1776460938": {"shortname": "blood", "name": "Blood", "description": "Used in creation of medkits."}, "1783512007": {"shortname": "cactusflesh", "name": "<PERSON>act<PERSON>", "description": "Flesh of a Cactus, contains water."}, "1784005657": {"shortname": "parachute.deployed", "name": "Parachute (Deployed)", "description": "Deployed version of parachute. Hidden."}, "1784406797": {"shortname": "fun.tuba", "name": "Sousaphone", "description": "A collection of used piping that’s been converted into a Tuba. Probably not very hygienic.\r"}, "1787198294": {"shortname": "frontiermirror.standing", "name": "Frontier Mirror Standing", "description": "A standing wooden frontier themed mirror"}, "1789825282": {"shortname": "candycaneclub", "name": "Candy Cane Club", "description": "Smash some faces with this large piece of candy"}, "1796682209": {"shortname": "smg.2", "name": "Custom SMG", "description": "A rapid-firing Machine Gun. Sacrifices accuracy and distance for speed."}, "**********": {"shortname": "toolgun", "name": "<PERSON>'s <PERSON><PERSON>", "description": "Celebrating 15 Years of Garry's Mod! Use this tool gun to upgrade building parts. Right-click for the options. You can also pick up deployed objects while the hammer is equipped."}, "**********": {"shortname": "water.radioactive", "name": "Radioactive Water", "description": "Glowing water. Consuming will damage your health and irradiate you."}, "**********": {"shortname": "knife.bone", "name": "Bone Knife", "description": "Melee weapon crafted from bone fragments. Good for harvesting carcases."}, "**********": {"shortname": "skylantern", "name": "Sky Lantern", "description": "A beautiful sky lantern. Can be launched in any direction. Has one inventory slot. Can be Ignited with a lit torch."}, "**********": {"shortname": "wolfmeat.burned", "name": "Burnt Wolf Meat", "description": "Spoiled Wolf Meat. Consuming will damage your health."}, "**********": {"shortname": "catapult.ammo.boulder", "name": "Scattershot", "description": "A cluster of heavy rock projectiles for a catapult. Wide spread, pure kinetic damage on impact."}, "**********": {"shortname": "electric.cabletunnel", "name": "Cable Tunnel", "description": "Allows you to pass a cable through a wall. Each input will forward the amount it receives to the corresponding output on the other side."}, "**********": {"shortname": "abovegroundpool", "name": "Above Ground Pool", "description": "A large wooden pool that can be filled with water."}, "**********": {"shortname": "grenade.beancan", "name": "Beancan Grenade", "description": "An unreliable explosive grenade. Each one has a different length fuse, so you might lose some fingers. Can be used to make satchel charges."}, "**********": {"shortname": "divertorch", "name": "<PERSON><PERSON>", "description": "Illuminate the underwater depths with this durable and waterproof diving torch"}, "**********": {"shortname": "electric.fuelgenerator.small", "name": "Small Generator", "description": "A small electric generator powered by Low Grade Fuel that outputs 40 power."}, "1850456855": {"shortname": "roadsign.kilt", "name": "Road Sign Kilt", "description": "A shoddy piece of leg armor made from roadsigns. Moderate protection from melee and projectiles."}, "1856217390": {"shortname": "easterbasket", "name": "Egg Basket", "description": "An easter egg basket. Equip to pick up eggs during an easter egg hunt with left click. Use right click to throw"}, "1858828593": {"shortname": "egg", "name": "Egg", "description": "An egg laid by a chicken, will hatch in a coop. Can be eaten raw but best saved for a recipe."}, "1865253052": {"shortname": "draculamask", "name": "Dracula Mask", "description": "A classic halloween costume mask which provides decent protection."}, "**********": {"shortname": "motorbike_sidecar", "name": "Motorbike With Sidecar", "description": "A motorbike with sidecar."}, "**********": {"shortname": "cocoknight.armor.gloves", "name": "Coconut Armor Gloves", "description": "Hand protection made of bark and plants, offers some protection to the upper body."}, "**********": {"shortname": "bearmeat.cooked", "name": "Cooked Bear Meat", "description": "Delicious Bear Meat, Eating it will restore some health, hunger, and thirst."}, "**********": {"shortname": "vehicle.1mod.cockpit.armored", "name": "Armored Cockpit Vehicle Module", "description": "Single module armored cockpit for a driver and one passenger."}, "**********": {"shortname": "burlap.headwrap", "name": "<PERSON><PERSON><PERSON>", "description": "A Headwrap made out of burlap."}, "**********": {"shortname": "rowboat", "name": "Rowboat", "description": ""}, "**********": {"shortname": "metalblade", "name": "Metal Blade", "description": "A Metal Blade for making melee weapons."}, "**********": {"shortname": "piston1", "name": "Low Quality Pistons", "description": "Low quality pistons for a combustion engine. Pistons convert expanding combustion gases into the motion that turns a crankshaft."}, "**********": {"shortname": "piston3", "name": "High Quality Pistons", "description": "High quality pistons for a combustion engine. Pistons convert expanding combustion gases into the motion that turns a crankshaft."}, "**********": {"shortname": "piston2", "name": "Medium Quality Pistons", "description": "Medium quality pistons for a combustion engine. Pistons convert expanding combustion gases into the motion that turns a crankshaft."}, "1885488976": {"shortname": "spookyspeaker", "name": "Spooky Speaker", "description": "Frighten your guests with creepy and spooky halloween sounds!"}, "1895235349": {"shortname": "discoball", "name": "Disco Ball", "description": "Get groovy with this stunning disco ball."}, "1898094925": {"shortname": "clone.pumpkin", "name": "Pumpkin Plant Clone", "description": "A Clipping of a pumpkin plant."}, "1899610628": {"shortname": "halloween.lootbag.medium", "name": "Medium Loot Bag", "description": "Contains medium level loot, Collect 10 to upgrade to a Large loot bag, containing the best loot"}, "1903654061": {"shortname": "planter.small", "name": "Small Planter Box", "description": "A small planter with enough room to plant 3 seeds.\r"}, "1905387657": {"shortname": "radiationremovetea.pure", "name": "Pure Rad. Removal Tea", "description": "A tea that removes some radiation."}, "1911552868": {"shortname": "seed.black.berry", "name": "<PERSON> Berry Seed", "description": "These black berry seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more berries and faster growth."}, "**********": {"shortname": "pistol.prototype17", "name": "Prototype 17", "description": "A protype pistol with a built-in 3 round burst fire mode. Strong in close quarters. Swap between single shot and burst with [+firemode]"}, "**********": {"shortname": "lightupmirror.standing", "name": "Light-Up Mirror Standing", "description": "A standing light-up mirror"}, "**********": {"shortname": "horsemeat.burned", "name": "Burnt Horse Meat", "description": "Burned horse meat. Eating it will damage your health."}, "**********": {"shortname": "porkmeat.spoiled", "name": "Spoiled Pork Meat", "description": "Spoiled Pork Meat. Consuming will damage your health."}, "**********": {"shortname": "black.raspberries", "name": "Black Raspberries", "description": "Black Raspberries picked from berry bushes. Eating it provides a small boost to health, hunger, and thirst."}, "**********": {"shortname": "frontier_hatchet", "name": "Frontier Hatchet", "description": "A Frontier Hatchet, useful for chopping trees and dismembering corpses."}, "**********": {"shortname": "campfire", "name": "Camp Fire", "description": "A small camp fire. Provides warmth and light, and you can cook with it."}, "**********": {"shortname": "floor.ladder.hatch", "name": "Ladder Hatch", "description": "A hatch that opens and changes into a ladder."}, "**********": {"shortname": "lightupframe.standing", "name": "Light-Up Frame Standing", "description": "A standing light-up frame"}, "**********": {"shortname": "shelves", "name": "Salvaged Shelves", "description": "Shelves for item stacking"}, "**********": {"shortname": "electric.switch", "name": "Switch", "description": "A simple electric switch."}, "**********": {"shortname": "pistol.nailgun", "name": "<PERSON><PERSON><PERSON>", "description": "A construction tool turned deadly"}, "**********": {"shortname": "catapult.ammo.bee", "name": "Bee Catapult Bomb", "description": "A bunch of Bee Grenades in a single catapult projectile. Creates swarms of Bees on impact."}, "**********": {"shortname": "crossbow", "name": "Crossbow", "description": "A very accurate ranged weapon."}, "**********": {"shortname": "cakefiveyear", "name": "Birthday Cake", "description": "A very special and happy birthday to <PERSON><PERSON>!"}, "**********": {"shortname": "chicken.burned", "name": "Burnt Chicken", "description": "Burned chicken. Eating it will damage your health."}, "**********": {"shortname": "gunrack.single.1.horizontal", "name": "Frontier Bolts Single It<PERSON>", "description": "Artfully display your weapon with a handcrafted wall-mounted single weapon rack."}, "**********": {"shortname": "surveycharge", "name": "Survey Charge", "description": "Use this on the terrain to see which resources are available for mining."}, "**********": {"shortname": "floor.triangle.grill", "name": "Floor triangle grill", "description": "A floor grill must be placed in floor frames. Perfect for placing above a large furnace."}, "**********": {"shortname": "horse.shoes.advanced", "name": "High Quality Horse Shoes", "description": "Equipping these horse shoes will grant your horse additional movement speed"}, "**********": {"shortname": "trike", "name": "Trike", "description": "A three-wheeled bicycle."}, "**********": {"shortname": "burlap.trousers", "name": "Burlap Trousers", "description": "Trousers made out of burlap."}, "**********": {"shortname": "weapon.mod.extendedmags", "name": "Extended Magazine", "description": "Larger magazine size"}, "2009734114": {"shortname": "xmasdoorwreath", "name": "Christmas Door Wreath", "description": "Get into the christmas spirit with this holiday door wreath."}, "2019042823": {"shortname": "tarp", "name": "Tarp", "description": "A waterproof tarp."}, "2021351233": {"shortname": "radiationremovetea.advanced", "name": "Advanced Rad. Removal Tea", "description": "A tea that removes some radiation."}, "2023888403": {"shortname": "electric.battery.rechargable.medium", "name": "Medium Rechargeable Battery", "description": "A Medium Rechargable Battery. Must have a minimum charge of 5 seconds to discharge. Can be wired in series. Charging rate is dependant on power in, with a maximum of 80% efficiency."}, "**********": {"shortname": "scraptea.pure", "name": "Pure Scrap Tea", "description": "A pure scrap tea, temporarily increases the amount of scrap you receive from barrels a large amount."}, "**********": {"shortname": "dart.scatter", "name": "<PERSON><PERSON><PERSON>", "description": "A spread shot dart that fires multiple smaller darts."}, "**********": {"shortname": "pie.bear", "name": "Bear Pie", "description": "A pie made of bear meat, provides a boost to hunger, health and hydration. Increases effect of harvesting teas but reduces their duration."}, "**********": {"shortname": "knife.combat", "name": "Combat Knife", "description": "Combat knife designed for close combat engagements, can attack while sprinting. Best in class at harvesting flesh."}, "**********": {"shortname": "floor.triangle.ladder.hatch", "name": "Triangle Ladder Hatch", "description": "A hatch that opens and changes into a ladder."}, "**********": {"shortname": "clothing.mod.armorinsert_lead", "name": "Lead <PERSON><PERSON>", "description": "Lead insert that adds radiation protection for crafted clothes and armor."}, "**********": {"shortname": "skull.wolf", "name": "Wolf Skull", "description": "A wolf skull."}, "**********": {"shortname": "innertube.unicorn", "name": "Inner Tube", "description": "An inflated tube for aquatic activities."}, "**********": {"shortname": "factorydoor", "name": "Factory Door", "description": "Medium strength door, vulnerable to explosives. Contains a bulletproof viewing portal"}, "2054929933": {"shortname": "rifle.ak.jungle", "name": "Jungle Relic Assault Rifle", "description": "Jungle Relic themed high damage machine rifle."}, "2055695285": {"shortname": "frontiermirror.medium", "name": "Frontier Mirror Medium", "description": "A medium wood frontier themed mirror"}, "2063916636": {"shortname": "oretea.advanced", "name": "Advanced Ore Tea", "description": "An advanced ore tea, provides hydration and temporarily increases your yield from ores a moderate amount."}, "2068884361": {"shortname": "smallbackpack", "name": "Small Backpack", "description": "A small, makeshift backpack"}, "2070189026": {"shortname": "sign.pole.banner.large", "name": "<PERSON> on pole", "description": "A large banner hanging on a pole."}, "2083256995": {"shortname": "t1_smg", "name": "Handmade SMG", "description": "An extremely basic automatic firearm. Low damage and accuracy."}, "2087678962": {"shortname": "searchlight", "name": "Search Light", "description": "A Large, wide beam, aimable light source."}, "2090395347": {"shortname": "electric.solarpanel.large", "name": "Large Solar Panel", "description": "A solar panel which converts sunlight into energy. The amount of energy generated is dependent on the sun's intensity and angle to the panel."}, "2100007442": {"shortname": "electric.audioalarm", "name": "Audio Alarm", "description": "A speaker which will emit a loud warning alarm when powered"}, "2104517339": {"shortname": "strobelight", "name": "Strobe Light", "description": "A flashing light, 3 speeds. Causes seizures."}, "2106561762": {"shortname": "xmas.decoration.tinsel", "name": "Decorative Tinsel", "description": "Decorative Tinsel you can hang on your Christmas tree"}, "2114754781": {"shortname": "water.purifier", "name": "Water Purifier", "description": "A Water Purifier. Place overtop of a campfire. Will provide clean, drinkable water from salty, or stagnant water."}, "2120241887": {"shortname": "goldmirror.standing", "name": "Gold Mirror Standing", "description": "A standing gold mirror"}, "2126889441": {"shortname": "santabeard", "name": "<PERSON>", "description": "A santa beard. Ho Ho ho."}, "2133269020": {"shortname": "clone.red.berry", "name": "<PERSON>", "description": "A clipping of a Red Berry Plant."}, "2137338174": {"shortname": "gates.external.high.frontier", "name": "High External Frontier Gate", "description": "A high wooden gate, allowing access in and out of your compound."}, "-1498613415": {"shortname": "advancedcoolingtea", "name": "Advanced Cooling Tea", "description": "An advanced cooling tea, provides hydration and temporarily decreases your max and core temperature."}, "-652889722": {"shortname": "advancedcraftingtea_quality", "name": "Advanced Crafting Quality Tea", "description": "An advanced crafting tea, provides hydration and temporarily increases the chances of a higher quality crafting outcome."}, "-334418777": {"shortname": "advancedwarmingtea", "name": "Advanced Warming Tea", "description": "An advanced warming tea that temporarily increases both your core and minimum temperature."}, "-1385721419": {"shortname": "advanceharvestingtea", "name": "Advanced Harvesting Tea", "description": "An advanced harvesting tea, provides hydration and temporarily boosts the amount of resources you receive from harvesting corpses a moderate amount."}, "-2097376851": {"shortname": "ammo.nailgun.nails", "name": "<PERSON><PERSON><PERSON>", "description": "Standard nailgun ammunition"}, "-1691396643": {"shortname": "ammo.pistol.hv", "name": "HV Pistol Ammo", "description": "This ammunition travels faster, resulting in less drop and slightly higher damage when fired over long distances."}, "-1321651331": {"shortname": "ammo.rifle.explosive", "name": "Explosive 5.56 Rifle Ammo", "description": "This ammo explodes on contact dealing a small amount of fragmentation damage to nearby objects."}, "-1211166256": {"shortname": "ammo.rifle", "name": "5.56 Rifle Ammo", "description": "Ammunition for a Rifle. Loses velocity when fired over long distances resulting in slightly decreased damage."}, "-*********": {"shortname": "ammo.rocket.basic", "name": "Rocket", "description": "Ammunition for a Rocket Launcher. A short to medium range self-propelled, unguided rocket which deals large splash damage on impact."}, "-1841918730": {"shortname": "ammo.rocket.hv", "name": "High Velocity Rocket", "description": "Fast moving rocket ammunition. Short to medium range self-propelled, unguided rocket which deals large splash damage on impact but less damage than a normal rocket."}, "-1843426638": {"shortname": "ammo.rocket.mlrs", "name": "MLRS Rocket", "description": "A rocket intended for use in an MLRS (Multiple Launch Rocket System)."}, "-384243979": {"shortname": "ammo.rocket.sam", "name": "SAM Ammo", "description": "Ammunition for a SAM Site. This missile contains a rudimentary guidance system to help it navigate through strong winds."}, "-17123659": {"shortname": "ammo.rocket.smoke", "name": "Smoke Rocket WIP!!!!", "description": "Emits thick, visibility blocking smoke upon impact."}, "-1036635990": {"shortname": "ammo.shotgun.fire", "name": "12 Gauge Incendiary Shell", "description": "Incendiary Shotgun Ammunition"}, "-1685290200": {"shortname": "ammo.shotgun", "name": "12 Gauge Buckshot", "description": "Ammunition for a Shotgun."}, "-727717969": {"shortname": "ammo.shotgun.slug", "name": "12 Gauge Slug", "description": "Single large projectile for a Shotgun."}, "-1432674913": {"shortname": "antiradpills", "name": "Anti-Radiation Pills", "description": "Taking these pills will lower your radiation level. WARNING: May cause extreme dehydration."}, "-1023065463": {"shortname": "arrow.hv", "name": "High Velocity Arrow", "description": "Lighter, faster arrow that deals less damage."}, "-1234735557": {"shortname": "arrow.wooden", "name": "<PERSON><PERSON>", "description": "An arrow for a Hunting Bow and Crossbow."}, "-1622110948": {"shortname": "attire.banditguard", "name": "Bandit Guard Gear", "description": "A bandit guard suit"}, "-1266045928": {"shortname": "attire.bunny.onesie", "name": "<PERSON>", "description": "A <PERSON>. Grants additional Egg Vision allowing you to detect more eggs during an Easter egg hunt."}, "-1004426654": {"shortname": "attire.bunnyears", "name": "<PERSON>", "description": "<PERSON> Ears. Grants additional Egg Vision allowing you to detect more eggs during an Easter egg hunt."}, "-747743875": {"shortname": "attire.egg.suit", "name": "Egg Suit", "description": "An eggsquisite egg costume, can be painted when unequipped."}, "-1773144852": {"shortname": "attire.hide.skirt", "name": "Hide Skirt", "description": "Simple hide skirt, made with common materials. Basic all-round protection."}, "-1506417026": {"shortname": "attire.ninja.suit", "name": "Ninja Suit", "description": "BaboAbe Ninja Suit"}, "-324675402": {"shortname": "attire.reindeer.headband", "name": "Reindeer Antlers", "description": "A festive reindeer antlers headband and nose."}, "-842267147": {"shortname": "attire.snowman.helmet", "name": "Snowman <PERSON><PERSON><PERSON>", "description": "A snowman helmet which provides ample protection."}, "-2139580305": {"shortname": "autoturret", "name": "Auto Turret", "description": "This automated electric sentry turret will engage and neutralize any moving targets it has line of sight to. You must equip it with a projectile weapon and the corresponding ammunition. Note : The turret will search for targets in a 180 degree arc facing *you* when you place it. Requires 10 power."}, "-262590403": {"shortname": "axe.salvaged", "name": "Salvaged Axe", "description": "A high-yield resource gathering axe. Slower than the Hatchet."}, "-19318653": {"shortname": "ballista.bolt.hammerhead", "name": "Hammerhead Bolt", "description": "Heavy and solid bolt for a ballista, designed to hammer through doors."}, "-1987565603": {"shortname": "ballista.bolt.incendiary", "name": "Incendiary Bolt", "description": "A fire-tipped bolt for a ballista, wrapped in burning cloth, ignites upon impact."}, "-1127003365": {"shortname": "ballista.bolt.piercer", "name": "<PERSON><PERSON>", "description": "A piercing bolt for a ballista, capable of penetrating armored vehicles."}, "-357442017": {"shortname": "ballista.bolt.pitchfork", "name": "Pitchfork Bolt", "description": "A wide-impact bolt for a ballista, covering a large area while dealing moderate damage."}, "-759279626": {"shortname": "ballista.mounted", "name": "Mounted Ballista", "description": "A ballista mounted on wheels, designed for mobility and capable of firing bolts at distant targets. Can be towed by horses."}, "-1652561344": {"shortname": "bamboo.barrel", "name": "Bamboo Barrel", "description": "Keep your things in this bamboo barrel. Stores up to 48 items. Cannot be locked."}, "-2072273936": {"shortname": "bandage", "name": "Bandage", "description": "Heal yourself or others with this bandage. Left-click heals you, right-click heals a target."}, "-1215166612": {"shortname": "barrelcostume", "name": "A Barrel Costume", "description": "A stupid barrel costume, so you can hide like a pro..p"}, "-1950721390": {"shortname": "barricade.concrete", "name": "Concrete Barr<PERSON>de", "description": "A barricade made out of Stones."}, "-424687710": {"shortname": "barricade.medieval", "name": "Medieval Barricade", "description": "A medieval barricade. Designed to block paths and provide defensive cover as well as being an effective tool to fortify your base."}, "-559599960": {"shortname": "barricade.sandbags", "name": "Sandbag Barricade", "description": "A protective barricade made out of Sand Bags."}, "-1274093662": {"shortname": "bathtub.planter", "name": "Bath Tub Planter", "description": "A small planter with enough room to plant 3 seeds."}, "-479314201": {"shortname": "batteringram.head.repair", "name": "Battering Ram Head", "description": ""}, "-187304968": {"shortname": "batteringram", "name": "Battering Ram", "description": "A reinforced siege weapon built for smashing through wooden and stone constructions with brute force. Must be repaired with high quality metal to keep functioning."}, "-321431890": {"shortname": "beachchair", "name": "Beach Chair", "description": "The Beach Chair is the best way to relax, whether it's by the pool or on the beach."}, "-1621539785": {"shortname": "beachparasol", "name": "Beach Parasol", "description": "A large umbrella that will shield you from the sun."}, "-8312704": {"shortname": "beachtowel", "name": "Beach Towel", "description": "A colourful beach towel. Placing this will give you a location to respawn."}, "-989755543": {"shortname": "bearmeat.burned", "name": "Burnt Bear Meat", "description": "Bear Meat that has been overcooked."}, "-**********": {"shortname": "bearmeat", "name": "Raw Bear Meat", "description": "Raw Bear Meat. Eating it will damage your health, try cooking it first."}, "-**********": {"shortname": "bed", "name": "Bed", "description": "A Bed. Placing this will give you a place to respawn and can be used much more frequently than a sleeping bag. Note: Must be placed inside a building."}, "-**********": {"shortname": "bigcatmeat.cooked", "name": "Cooked Big Cat Meat", "description": "Delicious Big Cat Meat, Eating it will restore some health, hunger, and thirst."}, "-**********": {"shortname": "bigcatmeat", "name": "Raw Big Cat Meat", "description": "Raw Big Cat Meat. Eating it will damage your health, try cooking it first."}, "-851288382": {"shortname": "blowpipe", "name": "Blow Pipe", "description": "A blown weapon that fires darts with different effects."}, "-586342290": {"shortname": "blueberries", "name": "Blueberries", "description": "Blueberries picked from berry bushes. Eating it provides a small boost to health, hunger, and thirst."}, "-996920608": {"shortname": "blueprintbase", "name": "Blueprint", "description": "Blueprint"}, "-880412831": {"shortname": "blunderbuss", "name": "Blunderbuss", "description": "A Shotgun with two barrels allowing two shots to be fired in quick succession before needing to reload."}, "-**********": {"shortname": "boogieboard", "name": "Boogie Board", "description": "Use the boogie board to catch some sick waves."}, "-**********": {"shortname": "boombox", "name": "Boom Box", "description": "A large speaker to play recorded cassette tapes which can also stream audio from the internet. Open the settings to change audio options."}, "-**********": {"shortname": "boots.frog", "name": "<PERSON>", "description": "Thank you for playing the game through its early access run. Enjoy these boots made from radiation resistant frog rubber."}, "-180129657": {"shortname": "box.wooden", "name": "Wood Storage Box", "description": "Keep your things in this wooden storage box. Stores up to 18 items."}, "-761829530": {"shortname": "burlap.shoes", "name": "Burlap Shoes", "description": "Shoes made out of burlap."}, "-700591459": {"shortname": "can.beans", "name": "Can of Beans", "description": "Beans found as loot. Eating it provides a small boost to health, hunger, and thirst."}, "-**********": {"shortname": "can.tuna.empty", "name": "Empty Tuna Can", "description": "An empty Can of Tuna. Can be smelted into Metal Fragments."}, "-**********": {"shortname": "can.tuna", "name": "Can of Tuna", "description": "Chunked tuna found as loot. Eating it provides a small boost to health, hunger, and thirst."}, "-912398867": {"shortname": "cassette.medium", "name": "Cassette - Medium", "description": "A tape that you can record audio on to using a Cassette Recorder. Stores up to 20s of audio."}, "-**********": {"shortname": "catapult.ammo.explosive", "name": "Propane Explosive Bomb", "description": "An explosive propane-filled keg, devastating to structures. Can be placed or used as a projectile in a catapult."}, "-484006286": {"shortname": "catapult.ammo.incendiary", "name": "Firebomb", "description": "A crude firebomb made of fuel-filled jugs wrapped in a net. Can be placed or used as a projectile in a catapult."}, "-**********": {"shortname": "chair.icethrone", "name": "Ice Throne", "description": "Every home needs a chair. A decorative item which provides comfort while seated."}, "-**********": {"shortname": "charcoal", "name": "Charc<PERSON>l", "description": "Byproduct from campfires and furnaces used for crafting gunpowder."}, "-**********": {"shortname": "chicken.cooked", "name": "Cooked Chicken", "description": "Cooked chicken. Eating it will restore some health, hunger, and thirst."}, "-152332823": {"shortname": "chicken.costume", "name": "Chicken Costume", "description": "An inflatable chicken costume. Acting as a replacement for wooden pants, you can equip this item to look as silly as possible."}, "-**********": {"shortname": "chicken.raw", "name": "Raw Chicken Breast", "description": "Raw chicken breast. Eating it will damage your health, try cooking it first."}, "-751151717": {"shortname": "chicken.spoiled", "name": "Spoiled Chicken", "description": "Spoiled Chicken. Eating it will damage your health."}, "-**********": {"shortname": "chickencoop", "name": "Chicken Coop", "description": "An enclosure to keep some chickens around. Chickens with plenty of food, water and sunlight produce more eggs."}, "-**********": {"shortname": "chineselantern", "name": "Chinese Lantern", "description": "A decorative Paper lantern"}, "-770304148": {"shortname": "chineselanternwhite", "name": "Chinese Lantern White", "description": "A decorative White Paper lantern"}, "-965336208": {"shortname": "chocolate", "name": "Chocolate Bar", "description": "Chocolate Bar found as loot. Eating it provides a small boost to health, hunger, and thirst."}, "-778875547": {"shortname": "clone.corn", "name": "<PERSON><PERSON>", "description": "A Clipping of a Corn Plant."}, "-**********": {"shortname": "clone.green.berry", "name": "<PERSON>", "description": "A clipping of a Green Berry Plant."}, "-886280491": {"shortname": "clone.hemp", "name": "<PERSON><PERSON>", "description": "A clipping of a hemp plant."}, "-798662404": {"shortname": "clone.orchid", "name": "<PERSON><PERSON><PERSON>", "description": "A clipping of an orchid plant."}, "-19360132": {"shortname": "clone.rose", "name": "<PERSON>", "description": "A clipping of a rose plant."}, "-858312878": {"shortname": "cloth", "name": "<PERSON><PERSON><PERSON>", "description": "Cloth from an animal or a Hemp Bush. Used in many clothing items, weapons, and more."}, "-903796529": {"shortname": "clothing.mod.armorinsert_asbestos", "name": "Asbestos Armor Insert", "description": "Asbestos insert providing extra protection from heat for crafted clothes and armor."}, "-593892112": {"shortname": "clothing.mod.armorinsert_wood", "name": "<PERSON>en Armor Insert", "description": "Wood insert for crafted clothes and armor, provides some extra protection from bullets and melee. "}, "-582467439": {"shortname": "cocoknight.armor.helmet", "name": "Coconut Armor Helmet", "description": "A shoddy helmet made from coconut, provides some basic protection from melee and ranged attacks."}, "-803263829": {"shortname": "coffeecan.helmet", "name": "Coffee Can Helmet", "description": "A Coffee Can Helmet. It provides a level of regional protection from inflicted damage."}, "-1247485104": {"shortname": "command.block", "name": "Command Block", "description": "A block that runs commands"}, "-1488398114": {"shortname": "composter", "name": "Composter", "description": "Produces fertilizer from compostable items."}, "-1588628467": {"shortname": "computerstation", "name": "Computer Station", "description": "A Computer station for remote control access"}, "-1360171080": {"shortname": "concretepickaxe", "name": "<PERSON><PERSON><PERSON>", "description": "A salvaged piece of concrete and rebar with a sharp edge. Can be used to harvest Stone, Metal ore and Sulfur ore."}, "-**********": {"shortname": "coolingtea", "name": "Basic Cooling Tea", "description": "A basic cooling tea that temporarily decreases your max and core temperature."}, "-**********": {"shortname": "crocodilemeat", "name": "Raw Crocodile Meat", "description": "Raw Crocodile Meat. Eating it will damage your health, try cooking it first."}, "-**********": {"shortname": "crocodilemeat.spoiled", "name": "Spoiled Crocodile Meat", "description": "Spoiled Crocodile Meat. Consuming will damage your health."}, "-321733511": {"shortname": "crude.oil", "name": "Crude Oil", "description": "Raw Oil out of the ground, must be refined in a refinery to be used."}, "-97956382": {"shortname": "cupboard.tool", "name": "Tool Cupboard", "description": "Placing and authorising with the cupboard will make it so only you can build in a 50 meter radius of it. If you want your friends to build, they'll need to auth too. You should protect this."}, "-361911645": {"shortname": "dart.bone", "name": "<PERSON>", "description": "A basic damage dart."}, "-963819285": {"shortname": "dart.incapacitate", "name": "Incapacitate Dart", "description": "A dart that slows your target and blocks their vision briefly."}, "-594596146": {"shortname": "dart.radiation", "name": "Radiation Dar<PERSON>", "description": "A dart that applies stackable radiation."}, "-274709858": {"shortname": "dart.wood", "name": "<PERSON>", "description": "A basic damage dart."}, "-**********": {"shortname": "deer.skull.mask", "name": "<PERSON> Helmet", "description": "A primitive helmet made from bone. Moderate protection from projectiles and melee damage."}, "-78533081": {"shortname": "deermeat.burned", "name": "Burnt Deer Meat", "description": "Burned Deer Meat. Eating it will damage your health."}, "-**********": {"shortname": "deermeat.cooked", "name": "Cooked Deer Meat", "description": "Cooked Deer Meat. Eating it will restore some health, hunger, and thirst."}, "-**********": {"shortname": "deermeat.spoiled", "name": "Spoiled Deer Meat", "description": "Spoiled Deer Meat. Consuming will damage your health."}, "-113413047": {"shortname": "diving.mask", "name": "Diving Mask", "description": "A diving mask that provides clear vision under water at the cost of reduced peripheral vision."}, "-**********": {"shortname": "diving.tank", "name": "Diving Tank", "description": "An oxygen tank that enables breathing under water for a period of time. Can be refilled at a workbench."}, "-**********": {"shortname": "diving.wetsuit", "name": "Wetsuit", "description": "A Wetsuit providing protection from frigid waters."}, "-**********": {"shortname": "door.double.hinged.bardoors", "name": "Wooden Frontier Bar Doors", "description": "Hand-carved, rugged bar doors embody the spirit of the frontier. A must-have for any survivor."}, "-**********": {"shortname": "door.double.hinged.wood", "name": "Wood Double Door", "description": "A Cheap door to secure your base. Its vulnerability to fire and weak explosive resistance makes the door a temporary solution to securing your base. Due to its flaws you should look at upgrading to a higher tier door such as Sheet Metal.\n\nThe Wooden Door can take two kinds of locks the basic Key Lock and the Code Lock. To pick up the door, remove any locks and open, hold down the E (USE) key and select 'Pickup'."}, "-2067472972": {"shortname": "door.hinged.metal", "name": "Sheet Metal Door", "description": "Medium strength door, vulnerable to explosives."}, "-1112793865": {"shortname": "door.key", "name": "Door Key", "description": "A key to a door, created from a Lock. Keep this item safe; if lost you'll be unable to open the door again!"}, "-258574361": {"shortname": "draculacape", "name": "Dracula Cape", "description": "A robustly thick outer garment providing ample protection to the wearer."}, "-854270928": {"shortname": "dragondoorknocker", "name": "Dragon Door Knocker", "description": "A heavy brass door knocker"}, "-1519126340": {"shortname": "dropbox", "name": "Drop Box", "description": "This item can be deployed on walls so that players can input items from the exterior of the building for safe keeping. You can only access the submitted contents of the Drop Box from the rear."}, "-1330640246": {"shortname": "drumkit", "name": "Junkyard Drum Kit", "description": "An array of pots, pans and buckets that passes as a serviceable Drum Kit. Three cymbals, some Tom Drums and a Kick Drum means you can easily lay down a beat."}, "-1002156085": {"shortname": "easter.goldegg", "name": "Gold Egg", "description": "A Gold Egg. Containing the best easter loot."}, "-126305173": {"shortname": "easter.<PERSON>eggs", "name": "Painted Egg", "description": "Some simple painted eggs. Collect 10 to upgrade to a bronze egg"}, "-979302481": {"shortname": "easterdoorwreath", "name": "Easter Door Wreath", "description": "A beautiful, hand crafted Easter themed door wreath."}, "-692338819": {"shortname": "electric.battery.rechargable.small", "name": "Small Rechargeable Battery", "description": "A small rechargable battery. Must have a minimum charge of 5 seconds to discharge. Can be wired in series. Charging rate is dependant on power in, with a maximum of 80% efficiency."}, "-690968985": {"shortname": "electric.blocker", "name": "Blocker", "description": "This object prevents passthrough while power is received through its second input."}, "-1778897469": {"shortname": "electric.button", "name": "<PERSON><PERSON>", "description": "A simple electric button."}, "-216999575": {"shortname": "electric.counter", "name": "Counter", "description": "A basic cathode ray tube screen combined with an incremental counter. Can display power received, or can count upwards and allow passthrough when a target is reached."}, "-502177121": {"shortname": "electric.doorcontroller", "name": "Door Controller", "description": "A Door Controller. Will manipulate the state of the closest door when it receives power"}, "-939424778": {"shortname": "electric.flasherlight", "name": "Flasher Light", "description": "A flashing blue light"}, "-1196547867": {"shortname": "electric.furnace", "name": "Electric Furnace", "description": "An electrical version of a furnace that uses electricity instead of a fuel source."}, "-295829489": {"shortname": "electric.generator.small", "name": "Test Generator", "description": "A Mailbox that accepts notes. Only you can access the mail due to a one way slit."}, "-1507239837": {"shortname": "electric.hbhfsensor", "name": "HBHF Sensor", "description": "A Heartbeat, Breathing, Humi<PERSON>y and Footstep sensor. Passthrough is equal to the number of humans detected in a 10m radius"}, "-784870360": {"shortname": "electric.heater", "name": "Electric Heater", "description": "Provides warmth in a radius when on"}, "-44876289": {"shortname": "electric.igniter", "name": "Igniter", "description": "Ignites nearby objects such as furnaces and campfires when power is received"}, "-798293154": {"shortname": "electric.laserdetector", "name": "Laser Detector", "description": "A gate which allows power to flow while a player is in the beam"}, "-1286302544": {"shortname": "electric.orswitch", "name": "OR Switch", "description": "A logic gate that allows electrical passthrough if EITHER input receives power, passthrough amount is the greater of either power source"}, "-2049214035": {"shortname": "electric.pressurepad", "name": "Pressure Pad", "description": "A gate which allows power to flow while a player is standing on it"}, "-1044468317": {"shortname": "electric.rf.broadcaster", "name": "RF Broadcaster", "description": "An RF Broadcaster"}, "-948291630": {"shortname": "electric.seismicsensor", "name": "Seismic Sensor", "description": "A small device that detects vibrations within a set range. Will output a different power value depending on the explosive type detected, ideal to know when your time has come."}, "-282113991": {"shortname": "electric.simplelight", "name": "Simple Light", "description": "A simple debugging light"}, "-563624462": {"shortname": "electric.splitter", "name": "Splitter", "description": "Splits an electrical signal into 3 multiple signals. the amount passed through is equal to the input amount divided by the number of used output slots."}, "-781014061": {"shortname": "electric.sprinkler", "name": "Sprinkler", "description": "A small sprinkler that sprays water around it. Requires a hose connection to supply it with water."}, "-1448252298": {"shortname": "electrical.branch", "name": "Electrical Branch", "description": "This object allows you to branch power off from a main line by a set amount."}, "-458565393": {"shortname": "electrical.combiner", "name": "Root Combiner", "description": "This object combines two root electrical sources into a single signal. Helpful for stringing together low energy batteries or solar panels to produce higher power output. Can be wired in series, can not be used with any non energy producing electrical components."}, "-746647361": {"shortname": "electrical.memorycell", "name": "Memory Cell", "description": "A 1 bit storage component. SET input will set the value to 1 CLEAR input sets the value to 0. Output will provide connected power if value is 1, Inverted output will provide connected power if value is 0. This is also known as a D-Type Flip Flop"}, "-1878475007": {"shortname": "explosive.satchel", "name": "Satchel Charge", "description": "Small explosive package constructed of beancan grenades. Useful for destroying wood and sheet metal doors."}, "-592016202": {"shortname": "explosives", "name": "Explosives", "description": "A component used for crafting C4 and Rockets."}, "-1018587433": {"shortname": "fat.animal", "name": "Animal Fat", "description": "Harvested from animals, combine with cloth to create a low quality fuel source."}, "-930193596": {"shortname": "fertilizer", "name": "Fertilizer", "description": "Helps plants grow by increasing ground condition. Produced in a composter."}, "-1535621066": {"shortname": "fireplace.stone", "name": "Stone Fireplace", "description": "A stone fireplace to heat your home. Must be placed indoors and against a wall."}, "-656349006": {"shortname": "firework.boomer.green", "name": "<PERSON>", "description": "A very large mortar type firework with a green starburst"}, "-7270019": {"shortname": "firework.boomer.orange", "name": "Orange Boomer", "description": "A very large mortar type firework with an orange starburst"}, "-379734527": {"shortname": "firework.boomer.pattern", "name": "<PERSON><PERSON> Boomer", "description": "A special boomer which allows you light up the night sky with a custom pattern that you draw. Multiple colors and altitude settings are available."}, "-1553999294": {"shortname": "firework.boomer.red", "name": "<PERSON>", "description": "A very large mortar type firework with a red starburst"}, "-280223496": {"shortname": "firework.boomer.violet", "name": "<PERSON>", "description": "A very large mortar type firework with a violet starburst"}, "-515830359": {"shortname": "firework.romancandle.blue", "name": "Blue Roman Candle", "description": "A small repeating firework, shoots a series of Blue balls into the air"}, "-1306288356": {"shortname": "firework.romancandle.green", "name": "Green Roman Candle", "description": "A small repeating firework, shoots a series of Green balls into the air"}, "-1486461488": {"shortname": "firework.romancandle.red", "name": "Red Roman Candle", "description": "A small repeating firework, shoots a series of red balls into the air"}, "-99886070": {"shortname": "firework.romancandle.violet", "name": "Violet <PERSON>dle", "description": "A small repeating firework, shoots a series of Violet balls into the air"}, "-454370658": {"shortname": "firework.volcano.red", "name": "Red Volcano Firework", "description": "Emits a beautiful shower of red sparks"}, "-1538109120": {"shortname": "firework.volcano.violet", "name": "Violet Volcano Firework", "description": "Emits a beautiful shower of Violet sparks"}, "-587989372": {"shortname": "fish.catfish", "name": "Catfish", "description": "A large, whiskered fresh water based predator. Eats everything, so you never know what you'll find in it's stomach."}, "-1698937385": {"shortname": "fish.herring", "name": "Herring", "description": "A small fish commonly found in a variety of water sources."}, "-542577259": {"shortname": "fish.minnows", "name": "Minnows", "description": "A Small group of edible fish."}, "-1904821376": {"shortname": "fish.orangeroughy", "name": "Orange Roughy", "description": "A rare fish found in the depths of the ocean. Eats everything, so you never know what you'll find in it's stomach."}, "-851988960": {"shortname": "fish.salmon", "name": "Salmon", "description": "A large Salmon, found in fresh and salt water. Eats everything, so you never know what you'll find in it's stomach."}, "-1654233406": {"shortname": "fish.sardine", "name": "Sardine", "description": "A small oily fish, commonly found in large schools."}, "-1768880890": {"shortname": "fish.smallshark", "name": "Small Shark", "description": "A small, surprisingly aggressive deep sea predator. Eats everything, so you never know what you'll find in it's stomach."}, "-1878764039": {"shortname": "fish.troutsmall", "name": "Small Trout", "description": "A Small Trout. Good for a few meals."}, "-1707425764": {"shortname": "fishing.tackle", "name": "Fishing Tackle", "description": "Fishing Tackle"}, "-1913996738": {"shortname": "fishtrophy", "name": "Fish Trophy", "description": "A wall mountable item to show off your greatest catches. Fish not included."}, "-1215753368": {"shortname": "flamethrower", "name": "Flame Thrower", "description": "A Flamethrower. Uses low grade fuel as ammunition."}, "-196667575": {"shortname": "flashlight.held", "name": "Flashlight", "description": "A powerful flashlight which illuminates your surroundings and can be used as a weapon."}, "-265292885": {"shortname": "fluid.combiner", "name": "Fluid Combiner", "description": "Combines three separate fluid connections into one connection."}, "-1166712463": {"shortname": "fluid.splitter", "name": "Fluid Splitter", "description": "Splits a fluid connection into three separate connections."}, "-1973785141": {"shortname": "fogmachine", "name": "Fogger-3000", "description": "A Fog machine which runs on low grade fuel. Can be set to fill an area with a dense fog or triggered by motion."}, "-1647389398": {"shortname": "frankensteinmask", "name": "Frankenstein Mask", "description": "Frankenstein halloween costume mask which provides decent protection."}, "-134959124": {"shortname": "frankensteins.monster.01.head", "name": "Light Frankenstein Head", "description": "Slightly faster, slightly weaker.\n\nCombined at a Frankenstein Table to create your very own monster."}, "-1624770297": {"shortname": "frankensteins.monster.01.torso", "name": "<PERSON> <PERSON>", "description": "Slightly faster, slightly weaker.\n\nCombined at a Frankenstein Table to create your very own monster."}, "-1732475823": {"shortname": "frankensteins.monster.02.head", "name": "Medium Frankenstein Head", "description": "A balanced head.\n\nCombined at a Frankenstein Table to create your very own monster."}, "-297099594": {"shortname": "frankensteins.monster.03.head", "name": "Heavy Frankenstein Head", "description": "Slightly slower, slightly stronger.\n\nCombined at a Frankenstein Table to create your very own monster."}, "-2024549027": {"shortname": "frankensteins.monster.03.legs", "name": "Heavy Frankenstein Legs", "description": "Slightly slower, slightly stronger.\n\nCombined at a Frankenstein Table to create your very own monster."}, "-2107018088": {"shortname": "fun.bass", "name": "<PERSON><PERSON><PERSON>", "description": "Use this stringed together shovel to play some deep, deep tunes.\r"}, "-1530414568": {"shortname": "fun.casetterecorder", "name": "Cassette Recorder", "description": "Insert a cassette and play the recorded audio with [attack], [attack2] to record new audio and [+reload] to throw."}, "-1049881973": {"shortname": "fun.cowbell", "name": "<PERSON><PERSON><PERSON>", "description": "A road sign that's been repurposed to produce some beautiful, calming, percussion."}, "-2040817543": {"shortname": "fun.flute", "name": "Pan Flute", "description": "A set of repurposed PVC pipes that can be used to play a variety of melodies.\r"}, "-2124352573": {"shortname": "fun.guitar", "name": "Acoustic Guitar", "description": "A classic acoustic guitar, built for jam sessions around campfires at night."}, "-979951147": {"shortname": "fun.jerrycanguitar", "name": "<PERSON>", "description": "Take advantage of the unique acoustics of the Jerry Can Guitar to play some rustic tunes."}, "-1379036069": {"shortname": "fun.tambourine", "name": "Canbourine", "description": "A selection of crumpled cans assembled into a ring to produce some great background percussion."}, "-1999722522": {"shortname": "furnace", "name": "Furnace", "description": "A Furnace. Use it to smelt mined ore."}, "-1992717673": {"shortname": "furnace.large", "name": "Large Furnace", "description": "Used to smelt large quantities of ore. Difficult to secure, must be placed on terrain."}, "-629028935": {"shortname": "fuse", "name": "Electric Fuse", "description": "An unreliable electric fuse. Will allow electrical passthrough for a period of time."}, "-401905610": {"shortname": "gates.external.high.adobe", "name": "High External Adobe Gate", "description": "A high stone gate, allowing access in and out of your compound."}, "-691113464": {"shortname": "gates.external.high.stone", "name": "High External Stone Gate", "description": "A high stone gate, allowing access in and out of your compound."}, "-335089230": {"shortname": "gates.external.high.wood", "name": "High External Wooden Gate", "description": "A high wooden gate, allowing access in and out of your compound."}, "-1819763926": {"shortname": "generator.wind.scrap", "name": "Wind Turbine", "description": "Converts kinetic energy harvested from the wind into electricity. Amount generated will vary depending on wind speed. Higher altitudes will yield stronger winds."}, "-1043618880": {"shortname": "ghostsheet", "name": "<PERSON> Costume", "description": "Definitely not a last minute costume idea."}, "-695124222": {"shortname": "giantcandycanedecor", "name": "Giant Candy Decor", "description": "Get into the holiday spirit with this decorative inflated giant candy cane"}, "-558880549": {"shortname": "gingerbreadsuit", "name": "Gingerbread Suit", "description": "A suit made of gingerbread, slightly crunchy."}, "-690276911": {"shortname": "gloweyes", "name": "Glowing Eyes", "description": "glowing eyes"}, "-1899491405": {"shortname": "glue", "name": "Glue", "description": "A strong adhesive."}, "-996235148": {"shortname": "goldframe.large", "name": "Gold Frame large", "description": "A large gold frame"}, "-1901993050": {"shortname": "goldframe.medium", "name": "Gold Frame Medium", "description": "A medium gold frame"}, "-1836526520": {"shortname": "goldframe.small", "name": "Gold Frame Small", "description": "A small gold frame"}, "-**********": {"shortname": "goldframe.standing", "name": "Gold Frame Standing", "description": "A standing gold frame"}, "-**********": {"shortname": "goldmirror.medium", "name": "Gold Mirror Medium", "description": "A medium gold mirror"}, "-**********": {"shortname": "goldmirror.small", "name": "Gold Mirror Small", "description": "A small gold mirror"}, "-746030907": {"shortname": "granolabar", "name": "Granola Bar", "description": "Granola Bar found as loot. Eating it provides a small boost to health, hunger."}, "-455286320": {"shortname": "gray<PERSON><PERSON>", "name": "Gray ID Tag", "description": "Gray ID Tag"}, "-936921910": {"shortname": "grenade.flashbang", "name": "Flashbang", "description": "A bright explosive device which blinds and disorients anyone near it upon detonation"}, "-568419968": {"shortname": "grub", "name": "<PERSON><PERSON><PERSON>", "description": "A pale, fleshy insect that can be used for bait. Could also be eaten in desperate situations."}, "-265876753": {"shortname": "gunpowder", "name": "<PERSON>der", "description": "Made from Sulfur and Charcoal, a key ingredient of anything that goes bang!"}, "-246672609": {"shortname": "gunrack.horizontal", "name": "Horizontal Weapon <PERSON>", "description": "Artfully display your arsenal with a handcrafted wall-mounted weapon rack."}, "-849373693": {"shortname": "gunrack.single.2.horizontal", "name": "Frontier Horseshoe Single <PERSON><PERSON>", "description": "Artfully display your weapon with a handcrafted wall-mounted single weapon rack."}, "-52398594": {"shortname": "gunrack.single.3.horizontal", "name": "Frontier Horns Single <PERSON><PERSON>", "description": "Artfully display your weapon with a handcrafted wall-mounted single weapon rack."}, "-96256997": {"shortname": "gunrack_wide.horizontal", "name": "Wide Weapon Rack", "description": "Artfully display your arsenal with a handcrafted wall-mounted weapon rack."}, "-1989600732": {"shortname": "hab.armor", "name": "Hot Air Balloon Armor", "description": "Armor for the Hot Air Balloon"}, "-1759188988": {"shortname": "habrepair", "name": "Hot Air Balloon", "description": "A Hot Air Balloon."}, "-1923843855": {"shortname": "half.bamboo.shelves", "name": "Half Height Bamboo Shelves", "description": "Half height salvaged bamboo shelves for stacking"}, "-888153050": {"shortname": "halloween.candy", "name": "Halloween Candy", "description": "Some loose halloween candy Collect 10 to upgrade to a Small Loot Bag!"}, "-1785231475": {"shortname": "halloween.surgeonsuit", "name": "Sur<PERSON>", "description": "A Surgeon Suit"}, "-1506397857": {"shortname": "hammer.salvaged", "name": "Salvaged Hammer", "description": "A Hammer made from a bunch of other crap."}, "-839576748": {"shortname": "handcuffs", "name": "Handcuffs", "description": "You're under arrest! Use on a surrendering or wounded player to keep them restrained. The wearer is blocked from using or holding items and can be pushed around."}, "-23994173": {"shortname": "hat.boonie", "name": "<PERSON><PERSON><PERSON>", "description": "A boonie hat, offering minor protection from damage and cold for your head."}, "-1022661119": {"shortname": "hat.cap", "name": "Baseball Cap", "description": "Simple hat for your head. Offers basic protection from damage and the cold."}, "-22883916": {"shortname": "hat.dragonmask", "name": "Dragon Mask", "description": "A special dragon mask to celebrate Chinese New Year"}, "-1539025626": {"shortname": "hat.miner", "name": "Miners Hat", "description": "A leather cap with a flashlight attached. It uses Low Grade Fuel and can be activated from the inventory."}, "-986782031": {"shortname": "hat.rabbitmask", "name": "Rabbit Mask", "description": "A Beautifully crafted Rabbit mask to celebrate the 2023 Lunar New Year"}, "-1314079879": {"shortname": "hat.snakemask", "name": "Snake mask", "description": "A Beautifully crafted Snake mask to celebrate the 2025 Lunar New Year"}, "-507248640": {"shortname": "hat.wellipets", "name": "Wellipets Hat", "description": "A hat made of wellipets boots"}, "-1478212975": {"shortname": "hat.wolf", "name": "<PERSON> Headdress", "description": "A headdress made out of the scraped out head of a wolf."}, "-1252059217": {"shortname": "hatchet", "name": "Hatchet", "description": "A Hatchet, useful for chopping trees and dismembering corpses."}, "-470439097": {"shortname": "hazmatsuit.arcticsuit", "name": "Arctic Suit", "description": "An arctic variant of the hazmat suit which trades radiation for cold protection."}, "-797592358": {"shortname": "hazmatsuit.diver", "name": "Abyss Divers Suit", "description": "A divers suit found in the abyss, doubles as a reliable hazmat suit"}, "-105415879": {"shortname": "hazmatsuit.frontier", "name": "Frontier Suit", "description": "A Frontier suit"}, "-560304835": {"shortname": "hazmatsuit.spacesuit", "name": "Space Suit", "description": "A spacesuit built for harsh conditions."}, "-253079493": {"shortname": "hazmatsuit_scientist", "name": "Scientist Suit", "description": "A hazmat suit made from radiation resistant rubber."}, "-1958316066": {"shortname": "hazmatsuit_scientist_peacekeeper", "name": "Scientist Suit", "description": "A hazmat suit made from radiation resistant rubber."}, "-1696379844": {"shortname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>"}, "-2123125470": {"shortname": "healingtea.advanced", "name": "Advanced Healing Tea", "description": "An advanced healing tea, provides hydration and heals you a moderate amount over time."}, "-929092070": {"shortname": "<PERSON><PERSON>a", "name": "Basic Healing Tea", "description": "A basic healing tea, provides hydration and heals you a small amount over time."}, "-1677315902": {"shortname": "healingtea.pure", "name": "Pure Healing Tea", "description": "A pure healing tea, provides hydration and heals you a large amount over time."}, "-1102429027": {"shortname": "heavy.plate.jacket", "name": "Heavy Plate Jacket", "description": "Offers superior protection at the cost of aiming down sights and reduced movement speed."}, "-1778159885": {"shortname": "heavy.plate.pants", "name": "Heavy Plate Pants", "description": "Offers superior protection at the cost of reduced movement speed."}, "-722629980": {"shortname": "heavyscientistyoutooz", "name": "Heavy Scientist <PERSON><PERSON><PERSON>", "description": "Heavy Scientist <PERSON><PERSON><PERSON>"}, "-1214542497": {"shortname": "hmlmg", "name": "HMLMG", "description": "A hand made belt-fed light machine gun with high damage, high rate of fire, low accuracy during short bursts."}, "-1442559428": {"shortname": "hobobarrel", "name": "Hobo Barrel", "description": "A vagabond staple. Use this repurposed oil barrel to stay warm."}, "-218009552": {"shortname": "homingmissile.launcher", "name": "Homing Missile Launcher", "description": "An active-guidance homing missile launcher. Once launched, the target must be tracked by the launcher until impact. Can be distracted by flares."}, "-1513203236": {"shortname": "honeycomb", "name": "Honeycomb", "description": "Building blocks of a hive, taken from a natural beehive. Can be cooked in a furnace to guarantee honey, or extracted for honey and a small chance of a nucleus."}, "-1323101799": {"shortname": "horse.saddle.double", "name": "Double Horse Saddle", "description": "A horse saddle with two seats. Having this item allows you to claim a horse in a stable by holding E and selecting the \"Claim\" option"}, "-1997543660": {"shortname": "horse.saddle", "name": "Horse Saddle", "description": "Having this item allows you to claim a horse in a stable by holding E and selecting the \"Claim\" option"}, "-**********": {"shortname": "horse.shoes.basic", "name": "Basic Horse Shoes", "description": "Equipping these horse shoes will grant your horse additional movement speed"}, "-**********": {"shortname": "horsedung", "name": "Horse Dung", "description": "Fresh and fertile."}, "-**********": {"shortname": "horsemeat.cooked", "name": "Cooked Horse Meat", "description": "Cooked Horse Meat. Eating it will restore some health, hunger, and thirst."}, "-**********": {"shortname": "horsemeat.raw", "name": "Raw Horse Meat", "description": "Raw Horse Meat. Eating it will damage your health, try cooking it first."}, "-724146494": {"shortname": "horsemeat.spoiled", "name": "Spoiled Horse Meat", "description": "Spoiled Horse Meat. Consuming will damage your health."}, "-**********": {"shortname": "hq.metal.ore", "name": "High Quality Metal Ore", "description": "A rock containing High Quality Metal. Can be smelted in a furnace."}, "-682687162": {"shortname": "humanmeat.burned", "name": "Burnt Human Meat", "description": "Burned Meat from a human being. Consuming will damage your health."}, "-**********": {"shortname": "humanmeat.raw", "name": "Raw Human Meat", "description": "Raw human meat. Consuming will damage your health."}, "-869598982": {"shortname": "hunting<PERSON>physmall", "name": "Small Hunting Trophy", "description": "A trophy stand to mount the head of killed animals or enemies."}, "-**********": {"shortname": "icepick.salvaged", "name": "Salvaged Icepick", "description": "A high-yield resource gathering Icepick."}, "-**********": {"shortname": "industrial.wall.light.red", "name": "Red Industrial Wall Light", "description": "A small red light source."}, "-697981032": {"shortname": "innertube", "name": "Inner Tube", "description": "An inflated tube for aquatic activities."}, "-**********": {"shortname": "jacket", "name": "Jacket", "description": "A rugged jacket, will help keep you warm. High cold protection but incompatible with other chest attire."}, "-48090175": {"shortname": "jacket.snow", "name": "Snow Jacket", "description": "A thick, high visibility Jacket. It will help keep you warm in any climate."}, "-**********": {"shortname": "jackola<PERSON>n.happy", "name": "<PERSON>", "description": "A Lantern. Place it where you need light."}, "-97459906": {"shortname": "jumpsuit.suit", "name": "Jumpsuit", "description": "A Jumpsuit"}, "-874908751": {"shortname": "jumpsuit.waterwellnpc", "name": "Waterwell NPC Jumpsuit", "description": "A green jumpsuit and gasmask worn by the waterwell NPC"}, "-484206264": {"shortname": "keycard_blue", "name": "Blue Keycard", "description": "A keycard with medium level clearance."}, "-1880870149": {"shortname": "keycard_red", "name": "Red Keycard", "description": "A keycard with advanced clearance, grants access to top level areas"}, "-194509282": {"shortname": "knife.butcher", "name": "<PERSON>", "description": "A sharp butcher knife. Extremely good at harvesting flesh from both dead or alive bodies."}, "-2073432256": {"shortname": "knife.skinning", "name": "Skinning Knife", "description": "A sharp skinning knife. Extremely good at harvesting flesh and can produce trophies that can be mounted on Hunting Trophies."}, "-427072335": {"shortname": "knightsarmour.helmet", "name": "Knights armour helmet", "description": "A medieval knights helmet. Made from scrap metal. Excellent craftmanship."}, "-945708533": {"shortname": "knightsarmour.skirt", "name": "Knights armour skirt plates", "description": "A well made set of knights plate that covers the hips and thighs. Made of scrap metal."}, "-316250604": {"shortname": "ladder.wooden.wall", "name": "<PERSON><PERSON>", "description": "A ladder will help you climb walls in any base."}, "-907422733": {"shortname": "largebackpack", "name": "Large Backpack", "description": "A larger military backpack that can hold items inside it"}, "-489848205": {"shortname": "largecandles", "name": "Large Candle Set", "description": "Large Candles"}, "-73195037": {"shortname": "legacy bow", "name": "Legacy bow", "description": "Legacy Bow, useful for short to medium range combat and hunting."}, "-1310391395": {"shortname": "legacyfurnace", "name": "Legacy Furnace", "description": "Legacy Furnace"}, "-1294739579": {"shortname": "lightupframe.medium", "name": "Light-Up Frame Medium", "description": "A medium Light-up frame"}, "-389796733": {"shortname": "lightupmirror.small", "name": "Light-Up Mirror Small", "description": "A small light-up mirror"}, "-2069578888": {"shortname": "lmg.m249", "name": "M249", "description": "High damage, high rate of fire, belt fed light machine gun."}, "-850982208": {"shortname": "lock.key", "name": "Key Lock", "description": "Place on a door to avoid any unwelcome guests. To craft your key first place the lock on a door and hold E while looking at the Key lock and select 'Create Key', a key will now be added to your crafting queue.\n\nIf the key is lost, you cannot make another, so don't forget to make extras and put them in a safe place!"}, "-110921842": {"shortname": "locker", "name": "Locker", "description": "A locker for keeping your clothes and armor safe!"}, "-2027988285": {"shortname": "locomotive", "name": "Locomotive", "description": "A powerful locomotive."}, "-1469578201": {"shortname": "longsword", "name": "Longsword", "description": "A powerful 2-handed melee weapon with good range and high damage."}, "-946369541": {"shortname": "lowgradefuel", "name": "Low Grade Fuel", "description": "Low Grade Fuel, used to power light sources."}, "-763071910": {"shortname": "lumberjack hoodie", "name": "Lumber<PERSON> <PERSON>", "description": "A lumberjack themed hoodie, earned by helping the Lumberjack. Offers the same protection as the standard hoodie."}, "-399173933": {"shortname": "lumberjack.hatchet", "name": "Prototype Hatchet", "description": "A prototype field hatchet, useful for gathering wood"}, "-1961560162": {"shortname": "lunar.firecrackers", "name": "Firecracker String", "description": "Happy Lunar New Year!"}, "-2026042603": {"shortname": "mace.baseballbat", "name": "Baseball Bat", "description": "A baseball bat wrapped in barbed wire"}, "-1966748496": {"shortname": "mace", "name": "Mace", "description": "A powerful 2-handed melee weapon, It's unique design makes it easier to hit your target when compared to other more traditional weapons."}, "-1137865085": {"shortname": "machete", "name": "Machete", "description": "A brutal weapon with good range."}, "-586784898": {"shortname": "mailbox", "name": "Mail Box", "description": "A Mailbox that accepts notes. Only you can access the mail due to a one way slit."}, "-**********": {"shortname": "mask.balaclava", "name": "Improvised Balaclava", "description": "A makeshift balaclava. Cost effective protection from the cold, but unremarkable at preventing melee and projectile damage."}, "-702051347": {"shortname": "mask.bandana", "name": "Bandana Mask", "description": "A square of cloth which is tied around the face over the nose and mouth."}, "-**********": {"shortname": "maxhealthtea", "name": "Basic Max Health Tea", "description": "A basic health tea, provides hydration and temporarily boosts maximum health a small amount."}, "-242084766": {"shortname": "meat.pork.cooked", "name": "Cooked Pork", "description": "Cooked pork, Eating it will restore some health, hunger, and thirst."}, "-380502678": {"shortname": "medieval.door.double.hinged.metal", "name": "Medieval Sheet Metal Double Door", "description": "Medium strength door, vulnerable to explosives."}, "-**********": {"shortname": "medieval.door.hinged.metal", "name": "Medieval Sheet Metal Door", "description": "Medium strength door, vulnerable to explosives."}, "-583379016": {"shortname": "megaphone", "name": "Megaphone", "description": "A megaphone that lets you broadcast your voice. [attack] to activate your voice."}, "-**********": {"shortname": "metal.facemask.hockey", "name": "Hockey Mask", "description": "A protective facemask which provides the user with excellent head protection from all forms of attacks."}, "-194953424": {"shortname": "metal.facemask", "name": "Metal Facemask", "description": "A protective facemask which provides the user with excellent head protection from all forms of attacks."}, "-4031221": {"shortname": "metal.ore", "name": "Metal Ore", "description": "A naturally occurring rock containing metal and metal compounds. You can extract this metal using a furnace."}, "-1478855279": {"shortname": "metal.plate.torso.icevest", "name": "Ice Metal Chest Plate", "description": "A metal plate covering your torso, provides excellent protection from all forms of attacks."}, "-1021495308": {"shortname": "metalspring", "name": "Metal Spring", "description": "A metal spring. Used to provide motion or resistance in objects."}, "-482348853": {"shortname": "minicrossbow", "name": "Mini Crossbow", "description": "A very accurate ranged weapon with a rotating barrel."}, "-1130709577": {"shortname": "mining.pumpjack", "name": "Pump <PERSON>", "description": "Extracts oil from the ground. Use a Survey Charge to determine the amount of oil available for extraction in any given area."}, "-1449152644": {"shortname": "mlrs", "name": "MLRS", "description": "M270 Multiple Launch Rocket System"}, "-20045316": {"shortname": "mobilephone", "name": "Mobile Phone", "description": "A mobile phone that lets you place calls from anywhere. Truly cutting edge Cobalt technology. Use [attack] to access the dialer."}, "-1417478274": {"shortname": "motorbike", "name": "Motorbike", "description": "A motorbike."}, "-**********": {"shortname": "movembermoustache", "name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "description": "A magnificent moustache for Movember"}, "-**********": {"shortname": "multiplegrenadelauncher", "name": "Multiple Grenade Launcher", "description": "A 40MM six barrel, semi automatic grenade launcher"}, "-**********": {"shortname": "mushroom", "name": "Mushroom", "description": "A Mushroom found on the ground. Eating it provides a small boost to health, hunger, and thirst."}, "-961457160": {"shortname": "newyeargong", "name": "New Year Gong", "description": "Ring in the Lunar new year with a massive gong!"}, "-**********": {"shortname": "nightvisiongoggles", "name": "Night Vision Goggles", "description": "Enables vision at nighttime by projecting and amplifying infrared light.The batteries can be recharged at a Lvl 2 workbench. Cannot be worn with a helmet."}, "-**********": {"shortname": "nucleus", "name": "Beehive Nucleus", "description": "The heart of any hive, needed for raising bees. Produce honeycomb from happy bees to grow the nucleus and increase yields."}, "-282193997": {"shortname": "orangeidtag", "name": "Orange ID Tag", "description": "Orange ID Tag"}, "-**********": {"shortname": "oubreak_scientist", "name": "Outbreak Scientist Suit", "description": "A outbreak scientist suit"}, "-733625651": {"shortname": "paddlingpool", "name": "Paddling Pool", "description": "A small inflatable pool that you can fill up with water."}, "-1695367501": {"shortname": "pants.shorts", "name": "Shorts", "description": "Trusty pair of shorts, may provide a breeze in undesired places. Worse than their full length counterparts with only basic protection from damage and the elements."}, "-**********": {"shortname": "paper", "name": "Paper", "description": "Paper, a ingredient for Building plan and Note."}, "-575744869": {"shortname": "partyhat", "name": "Party Hat", "description": "A very stupid looking party hat"}, "-**********": {"shortname": "pickaxe", "name": "Pickaxe", "description": "A Pickaxe, useful for gathering ore from rocks."}, "-**********": {"shortname": "pie.fish", "name": "Fish Pie", "description": "Delightful fish pie. Provides a boost to hunger, health and hydration. Increases base comfort level for a short time."}, "-**********": {"shortname": "pie.pumpkin", "name": "Pumpkin Pie", "description": "Spooky pumpkin pie, provides a boost to health, hunger and hydration. Increases maximum health for a short time."}, "-963820355": {"shortname": "pie.survivors", "name": "Survivor's Pie", "description": "It's people. Survivor's pie is made out of people. Provides a boost to health, hunger and hydration. Increases yield when harvesting corpses for a short time."}, "-**********": {"shortname": "pinata", "name": "<PERSON><PERSON><PERSON>", "description": "Batter up!"}, "-144513264": {"shortname": "pipetool", "name": "<PERSON><PERSON>", "description": "A tool used to make connections between industrial objects. Aim at an object and click on an input/output handle, then click on another object's input/output handle to form a connection. Holding right mouse will clear or cancel a connection."}, "-75944661": {"shortname": "pistol.eoka", "name": "Eoka Pistol", "description": "A very cheap, very ineffective, and very unreliable pistol that fires shells."}, "-852563019": {"shortname": "pistol.m92", "name": "M92 Pistol", "description": "A semi-automatic Pistol. Fires rapidly and with good accuracy. Military Grade"}, "-**********": {"shortname": "pistol.water", "name": "Water Pistol", "description": "A small water gun that doesn't require pumping, but has a lower range and output."}, "-280812482": {"shortname": "planter.triangle", "name": "Triangle Planter Box", "description": "A triangle planter with enough room to plant 4 seeds."}, "-804769727": {"shortname": "plantfiber", "name": "Plant Fiber", "description": "Fiber from dead plants. Can be composted."}, "-430416124": {"shortname": "plantpot.single", "name": "Single Plant Pot", "description": "A single plant pot."}, "-1651220691": {"shortname": "pookie.bear", "name": "Pookie Bear", "description": "A companion that reminds you of your youth."}, "-2086926071": {"shortname": "potato", "name": "Potato", "description": "Potato."}, "-365097295": {"shortname": "powered.water.purifier", "name": "Powered Water Purifier", "description": "A device that converts salt water to fresh water while powered."}, "-892718768": {"shortname": "prisonerhood", "name": "Prisoner <PERSON>", "description": "A hood for prisoners, restricts vision."}, "-**********": {"shortname": "propanetank", "name": "Empty Propane Tank", "description": "Strong metal object."}, "-567909622": {"shortname": "pumpkin", "name": "<PERSON><PERSON><PERSON>", "description": "An edible vegetable that can also be worn as a hat. Eating it provides a small boost to health, hunger, and thirst."}, "-**********": {"shortname": "purewarmingtea", "name": "Pure Warming Tea", "description": "A pure warming tea that temporarily increases both your core and minimum temperature."}, "-**********": {"shortname": "<PERSON><PERSON><PERSON>", "name": "Purple ID Tag", "description": "Purple ID Tag"}, "-496584751": {"shortname": "radiationremovetea", "name": "<PERSON><PERSON>. <PERSON>", "description": "A tea that removes some radiation."}, "-**********": {"shortname": "radiationresisttea.advanced", "name": "Advanced Anti-Rad Tea", "description": "An advanced tea that provides some hydration and temporarily increases your resistance to radiation a moderate amount."}, "-487356515": {"shortname": "radiationresisttea", "name": "Basic Anti-Rad Tea", "description": "A basic tea that provides hydration and temporarily increases your resistance to radiation a small amount."}, "-33009419": {"shortname": "radiationresisttea.pure", "name": "Pure Anti-Rad Tea", "description": "A pure tea that provides hydration and temporarily increases your resistance to radiation a large amount."}, "-602717596": {"shortname": "reddogtags", "name": "Red Dog Tags", "description": "Red Dog Tags"}, "-1861522751": {"shortname": "research.table", "name": "Research Table", "description": "You can use this table to learn how to permanently craft items you have found in exchange for scrap."}, "-544317637": {"shortname": "researchpaper", "name": "Research Paper", "description": "Use this item in a Research Bench to create blueprints of items."}, "-92315244": {"shortname": "revolver.hc", "name": "High Caliber Revolver", "description": "A powerful six-shot revolver using 5.56 rounds. High damage and accuracy, but slow fire rate and even slower to reload."}, "-566907190": {"shortname": "rf_pager", "name": "RF Pager", "description": "An RF Pager. An audible tone will be produced when a signal is picked up on the listening frequency. Can be set to silent mode."}, "-139037392": {"shortname": "rifle.ak.diver", "name": "Abyss Assault Rifle", "description": "A waterlogged assault rifle from the abyss"}, "-1335497659": {"shortname": "rifle.ak.ice", "name": "Ice Assault Rifle", "description": "Ice-covered high damage machine rifle."}, "-778367295": {"shortname": "rifle.l96", "name": "L96 Rifle", "description": "A military grade high powered, long range rifle with great accuracy."}, "-1812555177": {"shortname": "rifle.lr300", "name": "LR-300 Assault Rifle", "description": "Stalk your enemies with this high powered light assault rifle."}, "-904863145": {"shortname": "rifle.semiauto", "name": "Semi-Automatic Rifle", "description": "A Semi Automatic Rifle."}, "-348232115": {"shortname": "rifle.sks", "name": "SKS", "description": "A Military-Grade Semi Automatic Rifle."}, "-699558439": {"shortname": "roadsign.gloves", "name": "Roadsign Gloves", "description": "Gloves made out of metal, offer a good amount of protection to the upper body."}, "-2002277461": {"shortname": "roadsign.jacket", "name": "Road Sign Jacket", "description": "A shoddy piece of body armor made from roadsigns."}, "-1315992997": {"shortname": "rocket.launcher.dragon", "name": "Dragon Rocket Launcher", "description": "Unmatched craftsmanship meets devastating firepower."}, "-1863063690": {"shortname": "rockingchair", "name": "Rocking Chair", "description": "A rocking chair that responds to your input. Don't fire whilst seated."}, "-1104881824": {"shortname": "rug.bear", "name": "Rug <PERSON>", "description": "A decorative rug that can be placed on walls, floors, and ceilings. Provides comfort when in close proximity."}, "-1985799200": {"shortname": "rug", "name": "Rug", "description": "A decorative rug that can be placed on walls, floors, and ceilings. Provides comfort when in close proximity."}, "-173268129": {"shortname": "rustige_egg_a", "name": "<PERSON><PERSON><PERSON><PERSON> - Red", "description": "An exquisite hand crafted gold and diamond egg. When opened, a very special melody can be heard as you observe an intricate representation of your favorite monument."}, "-173268132": {"shortname": "rustige_egg_b", "name": "<PERSON><PERSON><PERSON><PERSON> - Blue", "description": "A beautiful hand crafted white gold and sapphire encrusted egg, with a miniature monument contained inside."}, "-173268131": {"shortname": "rustige_egg_c", "name": "<PERSON><PERSON><PERSON><PERSON> - Purple", "description": "A beautiful hand crafted purple egg with gold lace, a miniature offshore monument is contained inside."}, "-173268126": {"shortname": "rustige_egg_d", "name": "<PERSON><PERSON><PERSON><PERSON> - <PERSON>", "description": "A beautiful hand crafted Ivory and gold egg, with a miniature monument contained inside."}, "-173268125": {"shortname": "rustige_egg_e", "name": "<PERSON><PERSON><PERSON><PERSON> - Green", "description": "A beautiful hand crafted gold and green egg, with a miniature traincart contained inside."}, "-173268128": {"shortname": "rustige_egg_f", "name": "<PERSON><PERSON><PERSON><PERSON> - White", "description": "A beautiful hand crafted gold and white egg, with a miniature underwater lab scene contained inside."}, "-173268127": {"shortname": "rustige_egg_g", "name": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "description": "A beautiful hand crafted cerulean and gold egg, with a miniature cargo ship scene contained inside."}, "-2110553371": {"shortname": "salvaged.bamboo.shelves", "name": "Bamboo Salvaged Shelves", "description": "Bamboo shelves for item stacking"}, "-1978999529": {"shortname": "salvaged.cleaver", "name": "Salvaged Cleaver", "description": "A powerful 2-handed melee weapon with high damage."}, "-1009359066": {"shortname": "samsite", "name": "SAM Site", "description": "A surface to air rocket site. Requires 25 electricity to function."}, "-575483084": {"shortname": "santahat", "name": "Santa Hat", "description": "A santa hat. Ho Ho ho."}, "-1772746857": {"shortname": "scientistsuit_heavy", "name": "Heavy Scientist Suit", "description": "A heavy hazmat suit made from radiation resistant rubber."}, "-932201673": {"shortname": "scrap", "name": "Scrap", "description": "Scrap can be used in a Research table and Workbench to unlock and research items. Often used as a currency throughout the world."}, "-1094453063": {"shortname": "scrapframe.large", "name": "Scrap Frame large", "description": "A large scrap frame"}, "-1060567807": {"shortname": "scrapframe.medium", "name": "Scrap Frame Medium", "description": "A medium scrap frame"}, "-498301781": {"shortname": "scrapframe.small", "name": "Scrap Frame Small", "description": "A small scrap frame"}, "-1774190142": {"shortname": "scrapframe.standing", "name": "Scrap Frame Standing", "description": "A standing scrap frame"}, "-82758111": {"shortname": "scrapmirror.large", "name": "Scrap Mirror Large", "description": "A large scrap mirror"}, "-1050697733": {"shortname": "scrapmirror.small", "name": "Scrap Mirror Small", "description": "A small scrap mirror"}, "-1380144986": {"shortname": "scrapmirror.standing", "name": "Scrap Mirror Standing", "description": "A standing scrap mirror"}, "-1884328185": {"shortname": "scraptransportheli.repair", "name": "Scrap Transport Helicopter", "description": ""}, "-1776128552": {"shortname": "seed.green.berry", "name": "Green Berry Seed", "description": "These green berry seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more Corn and faster groth."}, "-237809779": {"shortname": "seed.hemp", "name": "<PERSON><PERSON>", "description": "Hemp seeds can be found when picking wild Hemp. These seeds can be planted in the ground and grown to collect additional cloth.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more cloth and faster growth."}, "-2084071424": {"shortname": "seed.potato", "name": "Potato Seed", "description": "These potato seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more potatoes and faster growth."}, "-1511285251": {"shortname": "seed.pumpkin", "name": "<PERSON><PERSON><PERSON> Seed", "description": "Pumpkin Seeds can be found when picking wild Pumpkin plants. These seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more pumpkins and faster growth."}, "-1037472336": {"shortname": "seed.rose", "name": "<PERSON>", "description": "Rose seeds can be found when picking wild Roses."}, "-1790885730": {"shortname": "seed.wheat", "name": "Wheat Seed", "description": "Wheat seeds, found when eating wheat or harvesting wild crops. Plant in the ground or a planter to grow more.  Seeds grown in good conditions will have higher yields and grow faster."}, "-992286106": {"shortname": "seed.white.berry", "name": "White Berry Seed", "description": "These white berry seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more berries and faster growth."}, "-520133715": {"shortname": "seed.yellow.berry", "name": "Yellow Berry Seed", "description": "These yellow berry seeds can be planted in the ground and grown to collect additional food.\n\n\nPlanting these seeds in a planter, and then watering them with large quantities of water yields significantly more berries and faster growth."}, "-1994909036": {"shortname": "sheetmetal", "name": "Sheet Metal", "description": "Sheet metal scraps."}, "-2025184684": {"shortname": "shirt.collared", "name": "Shirt", "description": "A smart casual shirt. Might make you look more trustworthy. Moderate clothing for protection against damage and the elements."}, "-1549739227": {"shortname": "shoes.boots", "name": "Boots", "description": "Sturdy work boots to keep your toes safe, you'd feel nothing if you kicked a table leg. Strong protection against the cold and incoming damage."}, "-765183617": {"shortname": "shotgun.double", "name": "Double Barrel Shotgun", "description": "A Shotgun with two barrels allowing two shots to be fired in quick succession before needing to reload."}, "-41440462": {"shortname": "shotgun.spas12", "name": "Spas-12 Shotgun", "description": "A semi automatic military issue shotgun"}, "-1367281941": {"shortname": "shotgun.waterpipe", "name": "Waterpipe Shotgun", "description": "A Shotgun. Fires a single round before reloading."}, "-1536855921": {"shortname": "shovel", "name": "<PERSON><PERSON><PERSON>", "description": "A shovel for digging up stuff"}, "-1199897169": {"shortname": "shutter.metal.embrasure.a", "name": "Metal horizontal embrasure", "description": "Static shutter that forms a metal horizontal embrasure over your window frame."}, "-1199897172": {"shortname": "shutter.metal.embrasure.b", "name": "Metal Vertical embrasure", "description": "Static shutter that forms a metal vertical embrasure over your window frame."}, "-1023374709": {"shortname": "shutter.wood.a", "name": "Wood Shutters", "description": "Wood shutters that you can open and close."}, "-1368584029": {"shortname": "sickle", "name": "<PERSON><PERSON>", "description": "A very sharp curved cutting weapon. While its traditional role was to cut grass, this tool is also extremely versatile at cutting throats, perhaps trees, too."}, "-1290278434": {"shortname": "siegetower", "name": "Siege Tower", "description": "A rolling siege tower, providing cover and access to enemy bases. Can be locked with your own door. Can be towed by horses."}, "-1647846966": {"shortname": "sign.hanging.ornate", "name": "Two Sided Ornate Hanging Sign", "description": "A double sided hanging sign, to attach to buildings."}, "-1423304443": {"shortname": "sign.neon.125x215", "name": "Medium Neon Sign", "description": "A medium neon sign!"}, "-845557339": {"shortname": "sign.pictureframe.landscape", "name": "Landscape Picture Frame", "description": "A landscape oriented canvas for artists paintings."}, "-1370759135": {"shortname": "sign.pictureframe.portrait", "name": "Portrait Picture Frame", "description": "A portrait oriented canvas for artists paintings."}, "-996185386": {"shortname": "sign.pictureframe.xl", "name": "XL Picture Frame", "description": "An extra large canvas for artists paintings."}, "-1832422579": {"shortname": "sign.post.town", "name": "One Sided Town Sign Post", "description": "A one sided town sign post, that you can plant into terrain."}, "-143132326": {"shortname": "sign.wooden.huge", "name": "<PERSON>ge <PERSON>en Sign", "description": "A 6-meter by 3-meter wooden sign that you can write on."}, "-1819233322": {"shortname": "sign.wooden.medium", "name": "Medium Wooden Sign", "description": "A 2-meter by 1-meter wooden sign that you can write on."}, "-1138208076": {"shortname": "sign.wooden.small", "name": "Small Wooden Sign", "description": "A 1-meter by 0.5-meter wooden sign that you can write on."}, "-193519904": {"shortname": "single.shallow.wall.shelves", "name": "Single Shallow Wall Shelves", "description": "Single Shallow Wall Shelves"}, "-1056824343": {"shortname": "skidoo", "name": "Diver propulsion vehicle", "description": "A personal underwater propulsion device"}, "-924959988": {"shortname": "skull.trophy.jar2", "name": "Skull Trophy", "description": "A decorative mount that can hold the skull of a friend or foe."}, "-769647921": {"shortname": "skull.trophy", "name": "Skull Trophy", "description": "A decorative mount that can hold the skull of a friend or foe."}, "-156748077": {"shortname": "skull.trophy.table", "name": "Skull Trophy", "description": "A decorative mount that can hold the skull of a friend or foe."}, "-216116642": {"shortname": "skull<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON> Knocker", "description": "A spooky skull door knocker"}, "-25740268": {"shortname": "skullspikes.candles", "name": "Skull Spikes", "description": "A spike of several skulls. Very spooky."}, "-1073015016": {"shortname": "skullspikes", "name": "Skull Spikes", "description": "A spike of several skulls. Very spooky."}, "-1078639462": {"shortname": "skullspikes.pumpkin", "name": "Skull Spikes", "description": "A spike of several skulls. Very spooky."}, "-1770889433": {"shortname": "skylantern.skylantern.green", "name": "Sky Lantern - <PERSON>", "description": ""}, "-1824770114": {"shortname": "skylantern.skylantern.orange", "name": "Sky Lantern - Orange", "description": ""}, "-1433390281": {"shortname": "skylantern.skylantern.red", "name": "Sky Lantern - Red", "description": ""}, "-*********": {"shortname": "sled", "name": "Sled", "description": "A wooden sled to quickly travel down snowy hills."}, "-*********": {"shortname": "sled.xmas", "name": "Sled", "description": "A wooden sled to quickly travel down snowy hills."}, "-1754948969": {"shortname": "sleepingbag", "name": "Sleeping Bag", "description": "A sleeping bag. Placing this will give you or an assigned player a place to respawn, with a long cooldown."}, "-1293296287": {"shortname": "small.oil.refinery", "name": "Small Oil Refinery", "description": "A small refinery which can produce Low Grade Fuel from Crude Oil."}, "-2058362263": {"shortname": "smallcandles", "name": "Small Candle Set", "description": "Small Candles"}, "-1039528932": {"shortname": "smallwaterbottle", "name": "Small Water Bottle", "description": "A Small Water Bottle. Left click to drink, right click to fill from water sources, or to pour out."}, "-*********": {"shortname": "smart.alarm", "name": "Smart Alarm", "description": "Sends a notifications to your phone when powered on."}, "-**********": {"shortname": "smg.thompson", "name": "<PERSON>", "description": "A medium damage machine gun."}, "-*********": {"shortname": "snakemeat.cooked", "name": "Cooked Snake Meat", "description": "Delicious Snake Meat, Eating it will restore some health, hunger, and thirst."}, "-**********": {"shortname": "snakemeat", "name": "Raw Snake Meat", "description": "Raw Snake Meat. Eating it will damage your health, try cooking it first."}, "-**********": {"shortname": "snakemeat.spoiled", "name": "Spoiled Snake Meat", "description": "Spoiled Snake Meat. Consuming will damage your health."}, "-363689972": {"shortname": "snowball", "name": "Snowball", "description": "A snowball, throw it!"}, "-**********": {"shortname": "snowmobile", "name": "Snowmobile", "description": "A custom snowmobile, made of various bits and pieces."}, "-555122905": {"shortname": "sofa", "name": "So<PERSON>", "description": "A comfortable old sofa. A decorative item which provides comfort and seats two."}, "-343857907": {"shortname": "soundlight", "name": "Sound Light", "description": "A light that will pulse in time to music when connected to a Boom Box."}, "-89874794": {"shortname": "sparkplug1", "name": "Low Quality Spark Plugs", "description": "Low quality spark plugs for a combustion engine. Spark plugs ignite the fuel/air mixture to move the pistons."}, "-493159321": {"shortname": "sparkplug2", "name": "Medium Quality Spark Plugs", "description": "Medium quality spark plugs for a combustion engine. Spark plugs ignite the fuel/air mixture to move the pistons."}, "-**********": {"shortname": "speargun", "name": "<PERSON><PERSON><PERSON>", "description": "A speargun which can only be fired underwater. Excellent for hunting Sharks."}, "-1800345240": {"shortname": "speargun.spear", "name": "<PERSON><PERSON><PERSON> Spear", "description": "Ammunition for a speargun."}, "-92759291": {"shortname": "spikes.floor", "name": "Wooden Floor Spikes", "description": "A set of spikes that will slow down and hurt anyone that walks over them."}, "-1100422738": {"shortname": "spinner.wheel", "name": "Spinning wheel", "description": "An interactive spinning wheel."}, "-596876839": {"shortname": "spraycan", "name": "Spray Can", "description": "Use the Spray Can to leave Tags or reskin your items. Press [attack] to spray and [attack2] to reskin items."}, "-1366326648": {"shortname": "spraycandecal", "name": "<PERSON>pray Can <PERSON>", "description": ""}, "-369760990": {"shortname": "stash.small", "name": "Small Stash", "description": "A hidden, burried stash to keep your items safe. After placing, return to the stash and aim at the ground where you hid it and it will reveal itself."}, "-465682601": {"shortname": "stocking.large", "name": "SUPER Stocking", "description": "A SUPER Stocking. Hang it by a fire and you might just get an awesome gift!"}, "-1583967946": {"shortname": "stonehatchet", "name": "<PERSON> Hatchet", "description": "Primitive tool used for harvesting wood from trees and logs."}, "-2099697608": {"shortname": "stones", "name": "Stones", "description": "Harvested from rocks using tools, basic building material."}, "-1049172752": {"shortname": "storageadaptor", "name": "Storage Adaptor", "description": "Attach to a storage container to allow industrial input/output connections."}, "-258457936": {"shortname": "storage_barrel_a", "name": "Unused Storage Barrel Vertical", "description": "Keep your things in this wooden storage box. Stores up to 48 items."}, "-1421257350": {"shortname": "storage_barrel_c", "name": "Storage Barrel Horizontal", "description": "Keep your things in this wooden barrel. Stores up to 48 items. Cannot be locked."}, "-1671551935": {"shortname": "submarine.torpedo.straight", "name": "Torped<PERSON>", "description": "A torpedo for use in a submarine. Fires ahead in a straight line."}, "-187031121": {"shortname": "submarinesolo", "name": "Solo Submarine", "description": "A small one-person submarine."}, "-1581843485": {"shortname": "sulfur", "name": "Sulfur", "description": "Sulfur is commonly used in gunpowder, medicine, and matches. Matches release sulfur dioxide when they burn, giving them their smell. Sulfur is an essential component to living cells. Many proteins contain sulfur. It is also used as a pesticide on organic farms."}, "-1157596551": {"shortname": "sulfur.ore", "name": "<PERSON><PERSON><PERSON>", "description": "A rock containing sulfur. The sulfur can be extracted using a furnace."}, "-611118083": {"shortname": "sunflower", "name": "Sunflower", "description": "Very tall."}, "-2103694546": {"shortname": "sunglasses02camo", "name": "Sunglasses", "description": "A pair of sunglasses. Functional and stylish!"}, "-176608084": {"shortname": "sunglasses03black", "name": "Sunglasses", "description": "A pair of sunglasses. Functional and stylish!"}, "-1997698639": {"shortname": "sunglasses03chrome", "name": "Sunglasses", "description": "A pair of sunglasses. Functional and stylish!"}, "-1408336705": {"shortname": "sunglasses03gold", "name": "Sunglasses", "description": "A pair of sunglasses. Functional and stylish!"}, "-1003665711": {"shortname": "supertea", "name": "Super Serum", "description": "Super Serum,  more ore, more wood, more scrap"}, "-1108136649": {"shortname": "tactical.gloves", "name": "Tactical Gloves", "description": "Military issue tactical gloves provide protection as well as weapon stabilization leading to higher accuracy when used."}, "-1736356576": {"shortname": "target.reactive", "name": "Reactive Target", "description": "A reactive target that knocks down when hit, can be reset."}, "-1262185308": {"shortname": "tool.binoculars", "name": "Binoculars", "description": "Binoculars allowing you to see objects at great length."}, "-1316706473": {"shortname": "tool.camera", "name": "Camera", "description": "A camera. Take screenshots with primary fire, zoom in and out with secondary fire, and lock focus with third fire."}, "-2001260025": {"shortname": "tool.instant_camera", "name": "Instant Camera", "description": "An instant camera. Take photos with primary fire, zoom in and out with secondary fire, and change focus mode with reload."}, "-1175656359": {"shortname": "torch.torch.skull", "name": "Cultist <PERSON>", "description": "Cultist <PERSON>"}, "-582782051": {"shortname": "trap.bear", "name": "Snap Trap", "description": "A metal trap that can injure or even kill people when stepped on. Can be picked up after been triggered."}, "-1663759755": {"shortname": "trap.landmine", "name": "Homemade Landmine", "description": "Homemade Landmine. If you stand on one, you can ask a friend to disarm it before it explodes."}, "-901370585": {"shortname": "trophy2023", "name": "Twitch Rivals Trophy 2023", "description": "A trophy dedicated to the survivors of Rust Twitch Rivals 2023"}, "-561148628": {"shortname": "tugboat", "name": "Tugboat", "description": ""}, "-1478445584": {"shortname": "tunalight", "name": "<PERSON><PERSON>", "description": "A makeshift wall light. Still smells faintly of fish."}, "-1569700847": {"shortname": "twitch.headset", "name": "Headset", "description": "Every gamer needs a headset! - Gained from Rust's first Twitch drop event."}, "-243540612": {"shortname": "twitchrivals2023desk", "name": "Twitch Rivals Desk", "description": "Twitch Rivals Desk"}, "-739993590": {"shortname": "twitchrivalsflag", "name": "Twitch Rivals Flag", "description": "A wearable flag for Twitch Rivals"}, "-1683726934": {"shortname": "twowaymirror.window", "name": "Two-Way Mirror", "description": "A Two-Way mirror"}, "-1802083073": {"shortname": "valve3", "name": "High Quality Valves", "description": "High quality poppet valves for a combustion engine. Valves control the intake and exhaust flow."}, "-885833256": {"shortname": "vampire.stake", "name": "Vampire Stake", "description": "A stake adorned with special properties making it able to release the soul of undead creatures with a single strike, as well as yield extra loot from their remains."}, "-1501451746": {"shortname": "vehicle.1mod.cockpit", "name": "Cockpit Vehicle Module", "description": "Single module cockpit for a driver and one passenger."}, "-1880231361": {"shortname": "vehicle.1mod.flatbed", "name": "Flatbed Vehicle Module", "description": "Single module flatbed."}, "-1615281216": {"shortname": "vehicle.1mod.passengers.armored", "name": "Armored Passenger Vehicle Module", "description": "Single module armored seating for two passengers."}, "-*********": {"shortname": "vehicle.1mod.taxi", "name": "Taxi Vehicle Module", "description": "Single module seating for two passengers, with protection at the front."}, "-1040518150": {"shortname": "vehicle.2mod.camper", "name": "Camper Vehicle Module", "description": "Dual module camper that allows up to 4 players to respawn. Also includes a barbecue, a locker and a small box for item storage."}, "-1693832478": {"shortname": "vehicle.2mod.flatbed", "name": "Large Flatbed Vehicle Module", "description": "Dual module large flatbed."}, "-44066600": {"shortname": "vehicle.chassis.2mod", "name": "Small Chassis", "description": "Two-socket vehicle chassis."}, "-44066823": {"shortname": "vehicle.chassis.3mod", "name": "Medium Chassis", "description": "Three-socket vehicle chassis."}, "-44066790": {"shortname": "vehicle.chassis.4mod", "name": "Large Chassis", "description": "Four-socket vehicle chassis."}, "-*********": {"shortname": "venom.snake", "name": "Snake Venom", "description": "The venom of a snake. Used to create Incapacitate darts."}, "-1416322465": {"shortname": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "description": "A walkie talkie set to an unknown channel"}, "-985781766": {"shortname": "wall.external.high.ice", "name": "High Ice Wall", "description": "A very high ice wall to protect your property."}, "-967648160": {"shortname": "wall.external.high.stone", "name": "High External Stone Wall", "description": "A high stone wall used to keep people off your property."}, "-956706906": {"shortname": "wall.frame.cell.gate", "name": "Prison Cell Gate", "description": "A prison cell gate to lock things behind bars"}, "-1429456799": {"shortname": "wall.frame.cell", "name": "Prison Cell Wall", "description": "A prison cell wall."}, "-1117626326": {"shortname": "wall.frame.fence", "name": "Chainlink Fence", "description": "A Chainlink Fence."}, "-148794216": {"shortname": "wall.frame.garagedoor", "name": "Garage Door", "description": "A garage door that rolls up and down."}, "-450890885": {"shortname": "wall.frame.lunar2025_b", "name": "Lunar <PERSON>ame Swirling", "description": "A Lunar New Year themed decorative wall divider with swirling inlay"}, "-2016974826": {"shortname": "wall.frame.lunar2025_c", "name": "Lunar <PERSON>", "description": "A Lunar New Year themed decorative wall divider with floral inlay"}, "-796583652": {"shortname": "wall.frame.shopfront", "name": "Shop Front", "description": "A shop front to do commerce."}, "-148229307": {"shortname": "wall.frame.shopfront.metal", "name": "Metal Shop Front", "description": "A bulletproof shop front for secure trades."}, "-1679267738": {"shortname": "wall.graveyard.fence", "name": "Graveyard Fence", "description": "A Spooky Fence"}, "-819720157": {"shortname": "wall.window.bars.metal", "name": "Metal Window Bars", "description": "Metal window bars made to fit a normal sized window."}, "-1183726687": {"shortname": "wall.window.bars.wood", "name": "Wooden Window Bars", "description": "Window bars made out of wood. They'll stop people climbing through your window, but probably not for very long."}, "-1614955425": {"shortname": "wall.window.glass.reinforced", "name": "Strengthened Glass Window", "description": "Bulletproof glass insert"}, "-551431036": {"shortname": "wallpaper.flooring", "name": "Wallpaper Flooring", "description": ""}, "-1501434104": {"shortname": "wallpaper", "name": "Wallpaper", "description": "Transform your base from a rusty shack to a cozy hideout with this sleek wallpaper. A nice touch of style - until the next raid, at least."}, "-1344017968": {"shortname": "wantedposter", "name": "Wanted Poster", "description": "A poster that can display a given player's face as wanted."}, "-1265020883": {"shortname": "wantedposter.wantedposter3", "name": "Wanted Poster 3", "description": "A poster that can display a given player's face as wanted."}, "-1142222427": {"shortname": "warmingtea", "name": "Basic Warming Tea", "description": "A basic warming tea that temporarily increases both your core and minimum temperature."}, "-463122489": {"shortname": "watchtower.wood", "name": "Watch Tower", "description": "A high wooden watchtower with a ladder"}, "-1863559151": {"shortname": "water.barrel", "name": "Water Barrel", "description": "A storage container for water. Can output 10ml of water a second via a hose."}, "-1100168350": {"shortname": "water.catcher.large", "name": "Large Water Catcher", "description": "Collects drinkable water from the air via rain and dew. Can be connected to other entities to provide water, 20ml a second."}, "-132247350": {"shortname": "water.catcher.small", "name": "Small Water Catcher", "description": "Collects drinkable water from the air via rain and dew. Can be connected to other entities to provide water, 20ml a second."}, "-**********": {"shortname": "water", "name": "Water", "description": "Water. Drinking it will alleviate your thirst."}, "-277057363": {"shortname": "water.salt", "name": "Salt Water", "description": "Water with a high salt content. Consuming will damage your health."}, "-119235651": {"shortname": "waterjug", "name": "Water Jug", "description": "A large jerry can for all kinds of water. Holds up to 5000ml. Careful not to throw it all away!"}, "-**********": {"shortname": "waterpump", "name": "Water Pump", "description": "Can be placed in a water source to collect that water while powered. Can be connected to other Water entities."}, "-**********": {"shortname": "weapon.mod.gascompressionovedrive", "name": "Gas Compression Overdrive", "description": "An internal weapon mod which compresses and directs gasses used for cycling the weapon to the barrel, effectively increasing projectile velocity and damage at the expense of cycle rate."}, "-132516482": {"shortname": "weapon.mod.lasersight", "name": "Weapon Lasersight", "description": "Attaches to a weapon. Provides user with a guiding laser and reduces weapon sway."}, "-**********": {"shortname": "weapon.mod.muzzleboost", "name": "Muzzle Boost", "description": "Increases weapon fire rate by channeling gasses back into the weapon cycling it faster, at the cost of bullet accuracy."}, "-781866273": {"shortname": "weapon.mod.oilfiltersilencer", "name": "Oil Filter Silencer", "description": "Significantly reduces the sound of gunfire, and completely removes any visible muzzle flash. Fashioned out of an old oil filter, this won't last long."}, "-1850571427": {"shortname": "weapon.mod.silencer", "name": "Military Silencer", "description": "Significantly reduces the sound of gunfire, and completely removes any visible muzzle flash."}, "-855748505": {"shortname": "weapon.mod.simplesight", "name": "Simple Handmade Sight", "description": "A poorly made sight, slightly better than iron sights."}, "-1659598760": {"shortname": "weapon.mod.sodacansilencer", "name": "Soda Can Silencer", "description": "Significantly reduces the sound of gunfire, and completely removes any visible muzzle flash. Crudely made out of a soda can, this will only survive a few shots."}, "-1163943815": {"shortname": "weaponrack.light", "name": "Weapon Rack Light", "description": "A weapon rack light."}, "-526026171": {"shortname": "wicker.barrel", "name": "Wicker Barrel", "description": "Keep your things in this wicker barrel. Stores up to 48 items. Cannot be locked."}, "-144417939": {"shortname": "wiretool", "name": "Wire Tool", "description": "A tool used to make connections between electrical objects. Aim at an object and click on an input/output handle, then click on another object's input/output handle to form a connection. Holding right mouse will clear or cancel a connection."}, "-395377963": {"shortname": "wolfmeat.raw", "name": "Raw Wolf Meat", "description": "Raw Wolf Meat. Eating it will damage your health, try cooking it first."}, "-**********": {"shortname": "wolfmeat.spoiled", "name": "Spoiled Wolf Meat", "description": "Spoiled Wolf Meat. Consuming will damage your health."}, "-**********": {"shortname": "wood.armor.helmet", "name": "Wood Armor Helmet", "description": "A shoddy helmet made from simple materials, provides some basic protection from melee and ranged attacks."}, "-151838493": {"shortname": "wood", "name": "<PERSON>", "description": "Wood. Collected from trees and used in many crafting recipes. It's also needed to cook in camp-fires."}, "-459159118": {"shortname": "woodarmor.gloves", "name": "<PERSON> Armor Gloves", "description": "Gloves made out of wood and leather, offers some protection to the upper body."}, "-635951327": {"shortname": "woodframe.large", "name": "<PERSON> Frame Large", "description": "A large wooden frame"}, "-**********": {"shortname": "woodframe.medium", "name": "Wood Frame Medium", "description": "A medium wooden frame"}, "-**********": {"shortname": "woodframe.small", "name": "Wood Frame Small", "description": "A small wooden frame"}, "-**********": {"shortname": "woodmirror.small", "name": "Wood Mirror Small", "description": "A small wooden mirror"}, "-541206665": {"shortname": "woodtea.advanced", "name": "Advanced Wood Tea", "description": "An advanced wood tea, temporarily increases the yield from cutting trees a moderate amount."}, "-649128577": {"shortname": "woodtea", "name": "Basic Wood Tea", "description": "A basic wood tea, temporarily increases the yield from cutting trees a small amount."}, "-557539629": {"shortname": "woodtea.pure", "name": "Pure Wood Tea", "description": "A pure wood tea, temporarily increases the yield from cutting trees a large amount."}, "-41896755": {"shortname": "workbench2", "name": "Workbench Level 2", "description": "This allows you to craft and unlock items requiring workbench level 2"}, "-1607980696": {"shortname": "workbench3", "name": "Workbench Level 3", "description": "This allows you to craft and unlock items requiring workbench level 3"}, "-810326667": {"shortname": "workcart", "name": "Work Cart", "description": "A service train designed to run on an underground rail system."}, "-2027793839": {"shortname": "xmas.advent", "name": "Advent Calendar", "description": "Receive a special gift each and every day!"}, "-1667224349": {"shortname": "xmas.decoration.baubels", "name": "Decorative Baubels", "description": "Decorative Baubels you can hang on your Christmas tree"}, "-209869746": {"shortname": "xmas.decoration.candycanes", "name": "Decorative Plastic Candy Canes", "description": "Decorative Plastic Candy Canes you can hang on your Christmas tree"}, "-129230242": {"shortname": "xmas.decoration.pinecone", "name": "Decorative Pinecones", "description": "Decorative Pinecones you can hang on your Christmas tree"}, "-1331212963": {"shortname": "xmas.decoration.star", "name": "Star Tree Topper", "description": "A golden star to top off your Christmas Tree"}, "-1230433643": {"shortname": "xmas.double.door.garland", "name": "Festive Double Doorway Garland", "description": "A Festive decoration to spruce up your double doorway during the holidays!"}, "-151387974": {"shortname": "xmas.lightstring.advanced", "name": "Deluxe Christmas Lights", "description": "Colored, Animated, Powered Lights. Requires 5 electricity. Click to start placement, right click to end. Each unit is 0.5m long."}, "-1622660759": {"shortname": "xmas.present.large", "name": "Large Present", "description": "A Large Present, the best there is. Unwrap it now!"}, "-722241321": {"shortname": "xmas.present.small", "name": "Small Present", "description": "A small stocking stuffer present, Collect 10 to upgrade to a larger present."}, "-1379835144": {"shortname": "xmas.window.garland", "name": "Festive Window Garland", "description": "A Festive decoration to spruce up your windows during the holdiays!"}, "-211235948": {"shortname": "xylophone", "name": "Xylobone", "description": "A selection of bones that were “acquired” for their acoustic attributes. Play a lullaby for your camp with these soothing tones."}}