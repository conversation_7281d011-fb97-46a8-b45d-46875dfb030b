{"24HoursInGameTimePassed": "24 aufeinanderfolgende Stunden sind In-Game verstrichen.", "abandonedCabins": "<PERSON>erlass<PERSON>", "abandonedMilitaryBase": "Verlassene Militärbasis", "abandonedSupermarket": "Verlassener Supermarkt", "addPlayerCap": "SPIELER HINZUFÜGEN", "addSwitchCap": "SCHALTER HINZUFÜGEN", "afkCap": "AFK", "airfield": "Flugplatz", "alarmHaveNotBeenTriggeredYet": "Der Alarm {alarm} wurde noch nicht ausgelöst.", "alias": "<PERSON><PERSON>", "aliasAlreadyExist": "Alias existiert bereits.", "aliasIndexCouldNotBeFound": "Alias-Index konnte nicht gefunden werden.", "aliasWasAdded": "<PERSON><PERSON> wurde hinzugefügt.", "aliasWasRemoved": "<PERSON><PERSON> wurde entfernt.", "aliases": "<PERSON><PERSON>", "all": "Alles", "allTeammatesAreDead": "Alle deine Teammitglieder sind tot.", "alreadySubscribedToItem": "<PERSON><PERSON><PERSON><PERSON> `{name}` ist bereits abonniert.", "ampersand": "Und-Zeichen", "andMorePlayers": "... und {number} weitere Spieler.", "any": "Alle", "apostrophe": "Apostroph", "arcticResearchBase": "Arktische Forschungsbasis", "asterisk": "Asterisk", "asteriskCctvDesc": "**** <PERSON><PERSON><PERSON><PERSON>, dass du einen numerischen Code brauchst, der auf jeder Karte unterschiedlich ist", "atLocation": "Position: {location}.", "atSign": "At-Zeichen", "autoDayCap": "AUTO-TAG", "autoNightCap": "AUTO-NACHT", "autoOffAnyOnlineCap": "AUTO-OFF-JMD-ONLINE", "autoOffCap": "AUTO-AUS", "autoOffProximityCap": "AUTO-AUS-PROXIMITÄT", "autoOnAnyOnlineCap": "AUTO-ON-JMD-ONLINE", "autoOnCap": "AUTO-AN", "autoOnProximityCap": "AUTO-AN-PROXIMITÄT", "autoSettingCap": "AUTO-EINSTELLUNG: ", "automaticallyTurnBackOnOff": " Wird automatisch in {time} {status}geschaltet.", "automaticallyTurningBackOnOff": "Schalte {device} automatisch {status}.", "autoturret": "Automatischer Geschützturm", "badGateway": "Bad Gateway: {error}", "banditCamp": "Banditenlager", "baseIsUnderAttack": "<PERSON><PERSON> wird angegriffen!", "battlemetricsApiRequestFailed": "Battlemetrics API Anfrage fehlgeschlagen: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics Server {server} konnte nicht aktualisiert werden.", "battlemetricsGlobalLoginCap": "GLOBAL: LOGIN", "battlemetricsGlobalLogoutCap": "GLOBAL: LOGOUT", "battlemetricsGlobalNameChangesCap": "GLOBAL: NAMENSÄNDERUNGEN", "battlemetricsId": "Battlemetrics-ID", "battlemetricsIdAndNameMissing": "Der Battlemetrics-Instanz fehlen ID und Name.", "battlemetricsInstanceCouldNotBeFound": "Battlemetrics-In<PERSON><PERSON> für {id} konnte nicht gefunden werden.", "battlemetricsOnlinePlayers": "Battlemetrics Online-<PERSON><PERSON><PERSON>", "battlemetricsPlayersLogin": "Battle<PERSON><PERSON>-<PERSON><PERSON>", "battlemetricsPlayersLogout": "Battlemetrics S<PERSON>ler-<PERSON><PERSON><PERSON>", "battlemetricsPlayersNameChanged": "Battlemetrics Spielername geändert", "battlemetricsServerNameChanged": "Battlemetrics Servername geändert", "battlemetricsServerNameChangesCap": "SERVER: <PERSON>AM<PERSON>SÄ<PERSON>ERUNGEN", "battlemetricsTrackerNameChangesCap": "TRACKER: <PERSON>AM<PERSON>SÄ<PERSON>ERUNGEN", "battlemetricsTrackerPlayerNameChanged": "Battlemetrics Tracker Spielername geändert", "blacklist": "Blacklist", "boomBox": "Boom Box", "bot": "Bot", "broadcaster": "Funksender", "buttonValueChange": "Schaltflächen-Interaktion - Kontroll-ID: {id}, Wert: {value}.", "buy": "<PERSON><PERSON><PERSON>", "calculated": "<PERSON><PERSON><PERSON><PERSON>", "cargoAt": "Position: {location}.", "cargoLeavingMapAt": "Frachtschiff verlässt die Karte [{location}].", "cargoLocatedAt": "Frachtschiff hält sich [{location}] auf.", "cargoNotCurrentlyOnMap": "Frachtschiff befindet sich derzeit nicht auf der Karte.", "cargoShipDetectedSetting": "<PERSON><PERSON> das Frachtschiff erkannt wird, sende eine Benachrichtigung.", "cargoShipDockingAtHarbor": "Frachtschiff ist gerade am Hafen bei {location} angelegt", "cargoShipDockingAtHarborSetting": "<PERSON><PERSON> das Frachtschiff an einem Hafen angelegt ist, sende eine Benachrichtigung.", "cargoShipEgressSetting": "<PERSON><PERSON> das Frachtschiff in die Austrittsphase eintritt, sende eine Benachrichtigung.", "cargoShipEntersEgressStage": "Das Frachtschiff sollte sich in der Austrittsphase befinden [{location}].", "cargoShipEntersMap": "Das Frachtschiff erscheint auf der Karte [{location}].", "cargoShipLeftHarbor": "Frachtschiff hat gerade den Hafen bei {location} verlassen", "cargoShipLeftMap": "Frachtschiff hat gerade die Karte verlassen [{location}].", "cargoShipLeftSetting": "<PERSON><PERSON> das Frachtschiff die Karte verlassen hat, sende eine Benachrichtigung.", "cargoShipLocated": "Frachtschiff hält sich [{location}] auf.", "cargoship": "Frach<PERSON><PERSON><PERSON>", "ceilingLight": "Deckenleuchte", "channelNameActivity": "aktivitäten", "channelNameAlarms": "alarme", "channelNameCommands": "be<PERSON><PERSON>e", "channelNameEvents": "ereignisse", "channelNameInformation": "informationen", "channelNameServers": "server", "channelNameSettings": "einstellungen", "channelNameStorageMonitors": "lagermonitore", "channelNameSwitchGroups": "schalter-gruppen", "channelNameSwitches": "schalter", "channelNameTeamchat": "teamchat", "channelNameTrackers": "tracker", "chinook47": "Transporthubschr.", "chinook47DetectedSetting": "<PERSON><PERSON> der Transporthubschrauber auf der Karte er<PERSON>int, sende eine Benachrichtigung.", "chinook47EntersMap": "Transporthubschrauber erscheint auf der Karte [{location}], um eine gesperrte Kiste abzuwerfen.", "chinook47LeftMap": "Transporthubschrauber hat die Karte verlassen [{location}].", "chinook47Located": "Transporthubschrauber hält sich [{location}] auf.", "chinook47NotOnMap": "Transporthubschrauber befindet sich derzeit nicht auf der Karte.", "christmasLights": "Weihnachtslichter", "circumflex": "Zirkumflex", "clanTag": "Clan-<PERSON><PERSON><PERSON><PERSON><PERSON>", "codes": "Codes", "colon": "Doppelpunkt", "comma": "<PERSON><PERSON>", "commandCap": "BEFEHL", "commandDelaySetting": "Soll es eine Befehlsverzögerung geben? Für wie lange?", "commandNotPossibleDiscord": "<PERSON><PERSON><PERSON> ist nicht möglich via Discord.", "commandSyntaxAdd": "hinzufügen", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "leben<PERSON><PERSON>", "commandSyntaxArmored": "gepanzert", "commandSyntaxCargo": "fracht", "commandSyntaxChinook": "transport", "commandSyntaxConnection": "verbindung", "commandSyntaxConnections": "verbindungen", "commandSyntaxCraft": "<PERSON><PERSON><PERSON>", "commandSyntaxDeath": "tod", "commandSyntaxDeaths": "tode", "commandSyntaxDecay": "zerfall", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "ereignisse", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "sprache", "commandSyntaxLarge": "groß", "commandSyntaxLeader": "anführer", "commandSyntaxList": "liste", "commandSyntaxMarker": "mark<PERSON><PERSON>", "commandSyntaxMarkers": "markier<PERSON>", "commandSyntaxMarket": "markt", "commandSyntaxMetal": "metall", "commandSyntaxMute": "stumm", "commandSyntaxNote": "notiz", "commandSyntaxNotes": "notizen", "commandSyntaxOff": "aus", "commandSyntaxOffline": "offline", "commandSyntaxOn": "ein", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "player", "commandSyntaxPlayers": "players", "commandSyntaxPop": "bev", "commandSyntaxProx": "nähe", "commandSyntaxRecycle": "recyceln", "commandSyntaxRemove": "löschen", "commandSyntaxResearch": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxSearch": "suche", "commandSyntaxSend": "sende", "commandSyntaxSmall": "klein", "commandSyntaxStack": "stapel", "commandSyntaxStatus": "status", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "stein", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "team", "commandSyntaxTime": "zeit", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxTwig": "zweig", "commandSyntaxUnmute": "entstumme", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "erhaltung", "commandSyntaxUptime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "holz", "commandsAlarmDesc": "Optionen für intelligente Alarme.", "commandsAlarmEditDesc": "Bearbeite die Eigenschaften von intelligenten Alarmen.", "commandsAlarmEditIdDesc": "Die ID des intelligenten Alarms.", "commandsAlarmEditImageDesc": "Lege das Bild fest, das am besten den intelligenten Alarm repräsentiert.", "commandsAliasAddAliasDesc": "Der zu verwendende Alias.", "commandsAliasAddDesc": "<PERSON><PERSON>.", "commandsAliasAddValueDesc": "Der Befehl/die Zeichenfolge.", "commandsAliasDesc": "<PERSON><PERSON><PERSON> einen Alias für einen Befehl/eine Zeichenfolge.", "commandsAliasRemoveDesc": "<PERSON><PERSON>.", "commandsAliasRemoveIndexDesc": "Der Index des zu entfernenden Aliases.", "commandsAliasShowDesc": "Zeige alle registrierten Aliase.", "commandsBlacklistAddDesc": "<PERSON>üge einen User zur Blacklist hinzu.", "commandsBlacklistDesc": "Blocke einen User vom Benutzen des Bots.", "commandsBlacklistDiscordUserDesc": "<PERSON>-User.", "commandsBlacklistRemoveDesc": "<PERSON><PERSON><PERSON><PERSON> einen User von <PERSON>list.", "commandsBlacklistShowDesc": "Zeige geblacklistete User an.", "commandsBlacklistSteamidDesc": "Die SteamId des Users.", "commandsCctvDesc": "Zeige Überwachungskamera-Codes für ein Monument an", "commandsCraftDesc": "Zeige die Kosten an einen Gegenstand herzustellen.", "commandsCraftQuantityDesc": "Die Menge der herzustellenden Gegenstände.", "commandsCredentialsAddDesc": "FCM-Anmeldedaten hinzufügen.", "commandsCredentialsDesc": "Festlegen/Entfernen der FCM-Anmeldedaten für das Benutzerkonto.", "commandsCredentialsRemoveDesc": "FCM-Anmeldedaten entfernen.", "commandsCredentialsRemoveSteamIdDesc": "SteamId der zu entfernenden FCM-Anmeldedaten.", "commandsCredentialsSetHosterDesc": "Lege den Hoster der FCM-Anmeldedaten fest.", "commandsCredentialsSetHosterSteamIdDesc": "SteamId des Hosters der FCM-Anmeldedaten.", "commandsCredentialsShowDesc": "Zeige die derzeit registrierten FCM-Anmeldedaten an.", "commandsDecayDesc": "Zeige Dauer bis zum Zerfall eines Gegenstandes an.", "commandsDespawnDesc": "Zeige Dauer bis zum Despawn eines Gegenstands an.", "commandsHelpCommandList": "Befehlsliste", "commandsHelpDesc": "Hilfe-Nachricht anzeigen.", "commandsHelpHowToCredentials": "Anleitung, um die FCM-Anmeldedaten zu registrieren", "commandsHelpHowToPairServer": "Anleitung, um den Bot mit dem Rust-Server zu koppeln", "commandsItemDesc": "Erhalte die Details eines Gegenstandes.", "commandsLeaderDesc": "<PERSON><PERSON><PERSON>/entziehe einem Teammitglied das Recht zum Anführer.", "commandsLeaderMemberDesc": "Der Name des Teammitgliedes.", "commandsMapAllDesc": "<PERSON>r<PERSON>e die Karte mit Namen der Monumente und Markierungen.", "commandsMapCleanDesc": "<PERSON><PERSON><PERSON><PERSON> die reine Karte.", "commandsMapDesc": "<PERSON>rhalte ein Bild der Karte des derzeit verbundenen Servers.", "commandsMapMarkersDesc": "Erhalte die Karte inklusive Markierungen.", "commandsMapMonumentsDesc": "Erhalte die Karte inklusive Namen der Monumente.", "commandsMarketDesc": "Optionen für Verkaufsautomaten.", "commandsMarketListDesc": "Liste die abonnierten Gegenstände auf.", "commandsMarketOrderDesc": "Die Handelskategorie.", "commandsMarketSearchDesc": "Suche nach einem Gegenstand in Verkaufsautomaten.", "commandsMarketSubscribeDesc": "Abonniere einen Gegenstand in Verkaufsautomaten.", "commandsMarketUnsubscribeDesc": "De-abonniere einen Gegenstand in Verkaufsautomaten.", "commandsPlayersBattlemetricsIdDesc": "Die Battlemetrics-ID des Servers (Standard: Der verbundene Server).", "commandsPlayersDesc": "Erhalte Informationen über Spieler basierend auf Battlemetrics.", "commandsPlayersNameDesc": "Suche nach Spieler auf Battlemetrics basierend auf dem Spielernamen.", "commandsPlayersPlayerIdDesc": "Suche nach Spieler auf Battlemetrics basierend auf der Spieler-ID.", "commandsPlayersPlayerIdPlayerIdDesc": "Die Spieler-ID des Spielers.", "commandsPlayersStatusDesc": "Suche nach Spielern: online/offline/alle", "commandsRecycleDesc": "Zeige das Ergebnis des Recycelns eines Gegenstands an.", "commandsRecycleQuantityDesc": "Die Menge der zu recycelnden Gegenstände.", "commandsRecycleRecyclerTypeDesc": "Der Recyclertyp (Recycler, Schredder, Safe-Zone-Recycler).", "commandsResearchDesc": "Zeige die Kosten an einen Gegenstand zu erforschen.", "commandsResetAlarmsDesc": "Alarme-Kanal zurücksetzen.", "commandsResetDesc": "Discord-<PERSON><PERSON><PERSON><PERSON>.", "commandsResetInformationDesc": "Informationen-Kanal zurücksetzen.", "commandsResetServersDesc": "Server-<PERSON><PERSON>.", "commandsResetSettingsDesc": "Einstellungen-Kanal zurücksetzen.", "commandsResetStorageMonitorsDesc": "Lagermonitore-Kanal zurücksetzen.", "commandsResetSwitchesDesc": "Schalter- und Schaltergruppen-Kanäle zurücksetzen.", "commandsResetTrackersDesc": "Tracker-<PERSON><PERSON> zurücksetzen.", "commandsRoleClearDesc": "<PERSON>ö<PERSON> die Rolle (damit jeder die rustplusplus-Ka<PERSON><PERSON><PERSON> sehen kann).", "commandsRoleDesc": "Festlegen/Löschen einer bestimme Rolle, die es ermöglicht rustplusplus-Inhalte zu sehen.", "commandsRoleSetDesc": "Lege die Rolle fest (damit nur eine bestimmte Rolle die rustplusplus-Kan<PERSON>le sehen kann).", "commandsRoleSetRoleDesc": "<PERSON> Rolle, für die die rustplusplus-Kan<PERSON>le sichtbar sein werden.", "commandsStackDesc": "Zeige die Stapelgröße eines Gegenstandes an.", "commandsStoragemonitorDesc": "Optionen für Lagermonitore.", "commandsStoragemonitorEditDesc": "Bearbeite die Eigenschaften von Lagermonitoren.", "commandsStoragemonitorEditIdDesc": "Die ID des Lagermonitors.", "commandsStoragemonitorEditImageDesc": "Lege das Bild fest, das am besten den Lagermonitor repräsentiert.", "commandsSwitchDesc": "Optionen für intelligente Schalter.", "commandsSwitchEditDesc": "Bearbeite die Eigenschaften von intelligenten Schaltern und Schaltergruppen.", "commandsSwitchEditIdDesc": "Die ID des intelligenten Schalters.", "commandsSwitchEditImageDesc": "Lege das Bild fest, das am besten den intelligenten Schalter repräsentiert.", "commandsUpkeepDesc": "Zeige die Erhaltungskosten eines Gegenstandes an.", "commandsUptimeBotDesc": "Zeige die Betriebsdauer des Bots an.", "commandsUptimeDesc": "Zeige die Betriebsdauer des Bots und des Servers an.", "commandsUptimeServerDesc": "Zeige die Betriebsdauer des Servers an.", "commandsVoiceBotJoinedVoice": "Der Bot ist dem Voice-Channel beigetreten", "commandsVoiceBotLeftVoice": "Der Bot hat den Voice-Channel verlassen", "commandsVoiceDesc": "Sprachbefehle des Bots", "commandsVoiceFemale": "<PERSON><PERSON><PERSON>", "commandsVoiceFemaleDescription": "Setzt das Stimmprofil auf weiblich", "commandsVoiceGenderDesc": "Legt das Stimmprofil des Bots fest.", "commandsVoiceJoin": "Beitreten des Voice-Channels {name} mit der ID {id} des Discord-Servers {guild}", "commandsVoiceJoinDesc": "<PERSON><PERSON>-Channel bei", "commandsVoiceLeave": "Verlassen des Voice-Channels {name} mit der ID {id} des Discord-Servers {guild}", "commandsVoiceLeaveDesc": "Verlässt den Voice-Channel", "commandsVoiceMale": "<PERSON><PERSON><PERSON><PERSON>", "commandsVoiceMaleDescription": "Setzt das Stimmprofil auf männlich", "commandsVoiceNotInVoice": "Du bist in keinem Voice-Channel", "connect": "Verbinden", "connectCap": "VERBINDEN", "connected": "Verbunden", "connectedCap": "VERBUNDEN", "connectedToServer": "MIT SERVER VERBUNDEN.", "connectingCap": "VERBINDE", "connectingToServer": "VERBINDE MIT SERVER ...", "connectionEvents": "Verbindungsereignisse", "connectionRefusedTo": "Verbindung verweigert zu: {id}.", "connectionsCap": "VERBINDUNGEN", "couldNotAddStepTracers": "Konnte Tracer-Schrite nicht hinzufügen.", "couldNotAppendMapMarkers": "Konnte Karten-Markierungen nicht hinzufügen, da rustplus-Info-Instanz nicht festgelegt ist.", "couldNotAppendMapMonuments": "Konnte Karten-Monumente nicht hinzufügen, da rustplus-Info-Instanz nicht festgelegt ist.", "couldNotAppendMapTracers": "Konnte Karten-<PERSON><PERSON> nicht hinz<PERSON>, da rustplus-Info-Instanz nicht festgelegt ist.", "couldNotConnectTo": "Konnte nicht verbinden mit: {id}.", "couldNotCreateCategory": "Konnte Kategorie nicht erstellen: {name}", "couldNotCreateTextChannel": "Konnte Text-Kanal nicht erstellen: {name}", "couldNotDeferInteraction": "Interaktion konnte nicht aufgeschoben werden.", "couldNotDeleteCategory": "Konnte Kategorie nicht löschen: {categoryId}", "couldNotDeleteChannel": "Konnte Kanal nicht löschen: {channelId}", "couldNotDeleteMessage": "Konnte Nachricht nicht löschen: {message}", "couldNotFindAnyPlayers": "Konnte keine Spieler finden.", "couldNotFindCategory": "Konnte Kategorie nicht finden: {category}", "couldNotFindChannel": "Konnte Kanal nicht finden: {channel}", "couldNotFindCraftDetails": "Konnte keine Details zur Herstellung von {name} finden.", "couldNotFindDecayDetails": "Konnte keine Details zum Zerfall von {name} finden.", "couldNotFindDespawnDetails": "Konnte keine Details zum Despawnen von {name} finden.", "couldNotFindGuild": "Konnte Discord-Server {guildId} nicht finden.", "couldNotFindLanguage": "Konnte Sprache nicht finden: {language}", "couldNotFindMessage": "Konnte Nachricht nicht finden: {message}", "couldNotFindPlayer": "Konnte Spieler nicht finden: {name}", "couldNotFindPlayerId": "Spieler mit ID {id} konnte nicht gefunden werden.", "couldNotFindRecycleDetails": "Konnte keine Details zum Recyclen von {name} finden.", "couldNotFindResearchDetails": "Konnte keine Details zur Erforschung von {name} finden.", "couldNotFindRole": "Konnte Rolle nicht finden: {roleId}", "couldNotFindStackDetails": "Konnte keine Details zur Stapelgröße von {name} finden.", "couldNotFindTeammate": "Konnte Teammitglied nicht finden: {name}", "couldNotFindUpkeepDetails": "Konnte keine Details zur Erhaltung von {name} finden.", "couldNotFindUser": "Konnte User nicht finden: {userId}", "couldNotGetChannelWithId": "Konnte Kanal mit ID {id} nicht abrufen.", "couldNotIdentifyMember": "Konnte Teammitglied nicht identifizieren: {name}", "couldNotPerformBulkDelete": "Konnte Massenlöschung im Kanal nicht ausführen: {channel}", "couldNotPerformMessageDelete": "Löschen der Nachricht fehlgeschlagen.", "couldNotPerformMessagesFetch": "Konnte Nachrichten nicht im Kanal abrufen: {channel}", "couldNotRegisterSlashCommands": "Konnte Slash-Befehle für Discord-Server {guildId} nicht registrieren. ", "couldNotSetParent": "Konnte den übergeordneten Kanal nicht festlegen: {channelId}", "craft": "Herstellen", "crate": "<PERSON><PERSON><PERSON><PERSON>", "createGroupCap": "ERSTELLE GRUPPE", "createTrackerCap": "ERSTELLE TRACKER", "credentialsAddedSuccessfully": "FCM-Anmeldedaten wurden erfolgreich für steamId: {steamId} hinzugefügt!", "credentialsAlreadyRegistered": "FCM-Anmeldedaten für steamId: {steamId} sind bereits registriert!", "credentialsCannotStartLiteAlreadyHoster": "FCM-Listener Lite für steamId: {steamId} kann nicht gestartet werden. Ist bereits Hoster.", "credentialsDoNotExist": "FCM-Anmeldedaten für steamId: {steamId} sind nicht vorhanden.", "credentialsHosterNotSetForGuild": "Hoster der FCM-Anmeldedaten ist nicht für Discord-Server {id} festgelegt, bitte lege einen Hoster fest.", "credentialsNotRegistered": "FCM-Anmeldedaten für steamId: {steamId} sind nicht registriert!", "credentialsNotRegisteredForGuild": "FCM-Anmeldedaten sind nicht für den Discord-Server registriert: {id}, kann den FCM-Listener nicht starten.", "credentialsRemovedSuccessfully": "FCM-Anmeldedaten für steamId: {steamId} wurden erfolgreich entfernt!", "credentialsSetHosterSuccessfully": "steamId: {steamId} wurde erfolgreich als Hoster der FCM-Anmeldedaten festgelegt.", "currencySign": "Währungszeichen", "currentCommandDelay": "Aktuelle Befehlsverzögerung: {delay} Sekunden.", "currentItemHp": "Die aktuellen HP des Gegenstandes.", "currentPrefixPlaceholder": "Aktuelles Vorzeichen: {prefix}", "customCommand": "Benutzerdefinierter Befehl", "customTimerEditCargoShipEgressLabel": "Frachtschiff - Dauer bis Austritt (in Sek):", "customTimerEditCrateOilRigUnlockLabel": "Ölbohrinsel gesp. Kiste - Entsperrzeit (Sek):", "customTimerEditDesc": "Bearbei<PERSON><PERSON> von benutzerdefinierten Timern", "customTimersCap": "BENUTZERDEFINIERTE  TIMER", "dash": "Bindestrich", "dayOfWipe": "Tag {day}", "deathCap": "TOT", "decay": "<PERSON>er<PERSON>", "decayTimeForItem": "Dauer bis zum Zerfall von {item} ist {time}.", "decayingCap": "ZERFÄLLT", "deleteUnreachableDevicesCap": "UNERREICHBARE GERÄTE LÖSCHEN", "despawnTime": "Dauer bis zum Despawn", "despawnTimeOfItem": "Dauer bis zum Despawn von {item} ist {time}.", "deviceIsAlreadyOnOff": "{device} ist bereits {status}geschaltet.", "deviceIsCurrentlyOnOff": "{device} ist derzeit {status}geschaltet.", "deviceWasTurnedOnOff": "{device} wurde {status}geschaltet.", "disabledCap": "DEAKTIVIERT", "discoFloor": "Disco-Fußboden", "disconnectCap": "TRENNEN", "disconnected": "Getrennt", "disconnectedCap": "GETRENNT", "disconnectedFromServer": "VOM SERVER GETRENNT.", "discordCap": "DISCORD", "discordUsers": "Discord-User", "displayInformationBattlemetricsAllOnlinePlayers": "<PERSON>len alle Online-<PERSON><PERSON><PERSON> von Battlemetrics im Informationen-Kanal angezeigt werden?", "displayingMap": "Anze<PERSON><PERSON> von {mapName} <PERSON>.", "displayingOnlinePlayers": "Anzeigen der Online-Spieler.", "distanceDirectionGrid": "{distance}m in Richtung {direction}° [{grid}].", "doorController": "Türsteuerung", "dot": "<PERSON><PERSON>", "eastOfGrid": "<PERSON><PERSON><PERSON>", "editCap": "BEARBEITEN", "editing": "Bearbeitung", "editingOf": "<PERSON><PERSON><PERSON><PERSON> von {entity}", "egressInTime": "<PERSON><PERSON>ritt in {time} [{location}].", "eight": "<PERSON><PERSON>", "elevator": "Aufzug", "empty": "<PERSON><PERSON>", "enabledCap": "Aktiviert", "entityId": "Objekt-ID", "equalsSign": "Gleichheitszeichen", "errorCap": "FEHLER", "errorExecutingCommand": "<PERSON><PERSON>f<PERSON> dieses Befehls ist ein Fehler aufgetreten!", "eventCap": "EREIGNIS", "eventInfo": "Ereignis-Informationen", "exclamationMark": "Ausrufezeichen", "failedToScrapeProfileName": "Fehler beim Extrahieren des Profilnamens: {link}.", "failedToScrapeProfilePicture": "Fehler beim Extrahieren des Profilbildes: {link}.", "fcmCredentials": "FCM-Anmeldedaten", "fcmListenerStartHost": "FCM-Listener Hoster startet in 5 Sekunden für Discord-Server: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "FCM-Listener Li<PERSON> startet in 5 Sekunden für Discord-Server: {guildId}, steamId: {steamId}.", "ferryTerminal": "Fährhafen", "fishingVillage": "Fischerdorf", "five": "<PERSON><PERSON><PERSON><PERSON>", "four": "<PERSON><PERSON>", "giantExcavatorPit": "Riesige Baggergrube", "greaterThanSign": "Größer-als-Zeichen", "groupAddSwitchDesc": "<PERSON><PERSON><PERSON> zu {group} hinzu", "groupRemoveSwitchDesc": "Ent<PERSON><PERSON>er aus {group}", "harbor": "Hafen", "hasBeenAliveLongest": "{name} ist am längsten am Leben ({time}).", "hash": "Rautezeichen", "hbhfSensor": "HAFT-Sensor", "heart": "Herz-Symbol", "heater": "Elektrische Heizung", "heavyScientistCalledSetting": "<PERSON>n schwere Wissenschaftler zu einer Ölbohrinsel gerufen werden, sende eine Benachrichtigung.", "heavyScientistsCalledLarge": "Schwere Wissenschaftler wurden zur großen Ölbohrinsel gerufen [{location}].", "heavyScientistsCalledSmall": "Schwere Wissenschaftler wurden zur kleinen Ölbohrinsel gerufen [{location}].", "hideTrademark": "K<PERSON>rzel ausblenden.", "hoster": "Hoster", "hp": "HP", "hpExceedMax": "HP {hp} überschreitet das Maximum von {max}.", "hqmQuarry": "HQM Steinbruch", "ignoreSetAvatar": "Ignored set<PERSON><PERSON>ar", "ignoreSetNickname": "Ignored setNickname", "ignoreSetUsername": "Ignored setNickname", "inGameBotMessagesMuted": "In-Game Bot-<PERSON><PERSON><PERSON><PERSON> stummgeschaltet.", "inGameBotMessagesUnmuted": "In-Game Bot-<PERSON><PERSON><PERSON>ten nicht stummgeschaltet.", "inGameCap": "IN-GAME", "inGameEventInfo": "In-Game Ereignis-Informationen", "inGameTeamNotificationsSetting": "In-Game Teammitglied-Benachrichtigungen.", "inGameTime": "In-Game-Zeit: {time}.", "index": "Index", "infoCap": "INFO", "inside": "<PERSON><PERSON><PERSON>", "interactionEditReplyFailed": "Interaction edit reply failed: {error}", "interactionInvalidChannel": "Interaktion von einem ungültigen Kanal.", "interactionReplyFailed": "Interaction reply failed: {error}", "interactionUpdateFailed": "Interaction update failed: {error}", "invalidBattlemetricsId": "Ungültige Battlemetrics-ID", "invalidGuildOrChannel": "Ungültiger Discord-Server oder Kanal.", "invalidHpInterval": "Ungültige HP-Angabe {hp}.", "invalidId": "Ungültige ID: {id}.", "invalidStructureType": "Ungültiges Baumaterial ({type}).", "invalidSubcommand": "Ungültiger Unterbefehl.", "invalidTimeDistance": "Invalid time distance: {distance}, prev: {prevTime}, new: {newTime}", "isDecaying": "{device} zerfällt!", "isNoLongerConnected": "{device} ist nicht mehr elektrisch verbunden!", "item": "Gegenstand", "itemAvailableInVendingMachine": "{items} ist gerade in einem Verkaufsautomaten [{location}] verfügbar geworden.", "itemAvailableNotifyInGameSetting": "In-Game benach<PERSON><PERSON>gen, wenn ein abonnierter Gegenstand in einem Verkaufsautomaten verfügbar wird?", "junkyard": "Schrottplatz", "justSubscribedToItem": "<PERSON><PERSON><PERSON><PERSON> `{name}` ist jetzt abonniert.", "languageCode": "Sprachkennzeichen: {code}", "languageLangNotSupported": "<PERSON><PERSON> [{language}] wird nicht unterstützt.", "languageNotSupported": "Die Sprache wird nicht unterstützt.", "largeBarn": "Große Sc<PERSON>une", "largeFishingVillage": "Großes Fischerdorf", "largeOilRig": "Große Ölbohrinsel", "largeWoodBox": "Große Holzkiste", "lastTrigger": "Letzte Auslösung", "launchSite": "Raketenstartplatz", "leaderAlreadyLeader": "{name} ist bereits Teamanführer.", "leaderCommandIsDisabled": "Anführerbefehl ist in den Einstellungen deaktiviert.", "leaderCommandOnlyWorks": "Anführerbefehl funktioniert nur, wenn der aktuelle Anführer {name} ist.", "leaderTransferred": "Teamführung wurde an {name} übergeben.", "leavingMapAt": "Verlässt Karte [{location}].", "lessThanSign": "Kleiner-als-Zeichen", "lighthouse": "Leuchtturm", "linkCap": "LINK", "location": "Position", "lockedCrateLargeOilRigUnlocked": "Gesperrte Kiste auf der großen Ölbohrinsel [{location}] wurde entsperrt.", "lockedCrateOilRigUnlockedSetting": "Wenn eine gesperrte Kiste auf einer Ölbohrinsel entsperrt wurde, sende eine Benachrichtigung.", "lockedCrateSmallOilRigUnlocked": "Gesperrte Kiste auf der kleinen Ölbohrinsel [{location}] wurde entsperrt.", "logDiscordCommand": "Discord-<PERSON><PERSON>hl - Discord-Server: {guild}, <PERSON><PERSON>: {channel}, <PERSON><PERSON><PERSON>: {user}, <PERSON><PERSON><PERSON><PERSON>: {message}.", "logDiscordMessage": "Discord-Nachricht - Discord-Server: {guild}, <PERSON><PERSON>: {channel}, <PERSON><PERSON><PERSON>: {user}, <PERSON><PERSON><PERSON><PERSON>: {message}.", "logInGameCommand": "{type} - <PERSON><PERSON><PERSON>: {command}, <PERSON><PERSON><PERSON>: {user}.", "logInGameMessage": "<PERSON><PERSON><PERSON><PERSON>: {message}, <PERSON><PERSON><PERSON>: {user}", "logSmartSwitchGroupValueChange": "Intelligente <PERSON>-Gruppe - Wert: {value}.", "logSmartSwitchValueChange": "Intelligenter <PERSON> - Wert: {value}.", "loggedInAs": "ANGEMELDET ALS: {name}", "makeSureApplicationsCommandsEnabled": "<PERSON><PERSON> sic<PERSON>, dass applications.command beim Erstellen der Einladungs-URL angehakt ist.", "map": "<PERSON><PERSON>", "mapSalt": "Map Salt", "mapSeed": "Map Seed", "mapSize": "Kartengröße", "mapWipeDetectedNotifySetting": "Soll {group} ben<PERSON><PERSON><PERSON><PERSON> werden, wenn Wipe der Karte erkannt wird?", "markerAdded": "Markierung {name} [{location}] wurde hinzugefügt.", "markerDoesNotExist": "Markierung {name} existiert nicht.", "markerLocation": "Mark<PERSON><PERSON> {name} [{location}] befindet sich {distance}m von {player} in Richtung {direction}° entfernt.", "markerRemoved": "Markierung {name} [{location}] wurde entfernt.", "message": "Nachricht", "messageCap": "NACHRICHT", "messageDeletedIn30": "Diese Nachricht wird in 30 Sekunden gelöscht.", "messageEditFailed": "Bearbeiten der Nachricht fehlgeschlagen: {error}", "messageReplyFailed": "Antwort auf Nachricht fehlgeschlagen: {error}", "messageSendFailed": "Senden der Nachricht fehlgeschlagen: {error}", "messageWasSent": "Nachricht wurde gesendet.", "militaryTunnel": "Militärtunnel", "miningOutpost": "Bergbau-Außenposten", "missileSilo": "Raketensilo", "missingArguments": "Fehlende Argumente.", "missingPermission": "Du hast keine Berechtigung dies zu tun.", "missingTimerMessage": "Fehlende Timer-Nachricht.", "modalValueChange": "Dialogfenster-Interaktion - Kontroll-ID: {id}, Wert: {value}.", "more": "weitere", "morePlayers": "{players} ...{number} weitere.", "mutedCap": "STUMM GESCHALTET", "name": "Name", "nameChangeHistory": "Namensänderungsverlauf", "new": "<PERSON>eu", "newVendingMachine": "Neuer Verkaufsautomat lokalisiert [{location}].", "newsCap": "NEWS", "noActiveTimers": "<PERSON><PERSON> aktiven Timer.", "noCommandDelay": "<PERSON>ine <PERSON>hlsverzögerung.", "noCommunicationSmartSwitch": "Konnte nicht mit intelligentem Schalter kommunizieren: {name}", "noData": "<PERSON><PERSON>.", "noDataOnLargeOilRig": "Aktuell keine Daten über große Ölbohrinsel.", "noDataOnSmallOilRig": "Aktuell keine Daten über kleine Ölbohrinsel.", "noDelayCap": "KEINE VERZÖGERUNG", "noItemFound": "Gegenstand konnte in keinem Verkaufsautomaten gefunden werden...", "noItemWithIdFound": "<PERSON>s konnte kein Gegenstand mit ID {id} gefunden werden.", "noItemWithNameFound": "<PERSON>s konnte kein Gegenstand mit Namen `{name}` gefunden werden.", "noNameIdGiven": "Es wurde kein 'Name' oder 'ID' angegeben.", "noOneIsAfk": "Niemand ist AFK.", "noOneIsOffline": "Niemand ist offline.", "noOneIsOnline": "Niemand ist online.", "noRegisteredConnectionEvents": "Noch keine Verbindungsereignisse registriert.", "noRegisteredConnectionEventsUser": "Keine registrierten Verbindungsereignisse für {user}.", "noRegisteredDeathEvents": "Noch keine Todesfälle registriert.", "noRegisteredDeathEventsUser": "<PERSON>ine registrierten Todesfälle für {user}.", "noRegisteredEvents": "Noch keine Ereignisse registriert.", "noRegisteredMarkers": "<PERSON><PERSON> registrierten Markierungen.", "noSavedNotes": "<PERSON><PERSON> gespeicherten Notizen vorhanden.", "noToolCupboardWereFound": "<PERSON>s wurden keine Werkzeugschrank-Lagermonitore gefunden.", "none": "<PERSON><PERSON>", "northEast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "northOfGrid": "<PERSON><PERSON><PERSON><PERSON>", "northWest": "nordwestlich", "notAValidOrderType": "{order} ist keine gültige Handelskategorie.", "notActive": "Nicht aktiv.", "notConnectedToRustServer": "Aktuell mit keinem Rust-Server verbunden.", "notExistInSubscription": "<PERSON><PERSON><PERSON><PERSON> `{name}` ist nicht abonniert.", "notFoundCap": "NICHT GEFUNDEN", "notPartOfRole": "Du bist nicht Teil der {role} <PERSON><PERSON>, weshal<PERSON> du keine Bot-Be<PERSON>hle ausführen kannst.", "notShowingCap": "NICHT ANZEIGEN", "noteCap": "HINWEIS", "noteIdDoesNotExist": "Notiz-ID: {id} existiert nicht.", "noteIdInvalid": "Notiz-ID ist ungültig.", "noteIdWasRemoved": "Notiz-ID: {id} wurde entfernt.", "noteSaved": "<PERSON><PERSON>.", "offCap": "AUS", "offline": "Offline", "offlineTime": "Offline-<PERSON>uer", "oilRig": "Ölbohrinsel", "old": "Ehemals", "onCap": "EIN", "one": "<PERSON><PERSON>", "online": "Online", "onlineTime": "Online-Da<PERSON>", "onlyOneInTeam": "Du bist der Einzige im Team.", "outpost": "Außenposten", "outside": "Draußen", "oxumsGasStation": "Oxum's Tankstelle", "pairing": "koppeln", "patrolHelicopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "patrolHelicopterDestroyedSetting": "<PERSON><PERSON> der Patroullienhubschrauber zerstört wird, sende eine Benachrichtigung.", "patrolHelicopterDetectedSetting": "<PERSON><PERSON> der Patroullienhubschrauber erkannt wird, sende eine Benachrichtigung.", "patrolHelicopterEntersMap": "Patrouillenhubschrauber erscheint auf der Karte [{location}].", "patrolHelicopterLeftMap": "Patroullienhubschrauber hat gerade die Karte verlassen [{location}].", "patrolHelicopterLeftSetting": "<PERSON><PERSON> der Patroullienhubschrauber die Karte verlässt, sende eine Benachrichtigung.", "patrolHelicopterLocatedAt": "Patrouillenhubschrauber hält sich [{location}] auf.", "patrolHelicopterNotCurrentlyOnMap": "Patrouillenhubschrauber befindet sich derzeit nicht auf der Karte.", "patrolHelicopterTakenDown": "Patrouillenhubschrauber wurde abgeschossen [{location}].", "percentSign": "Prozent-Zeichen", "pipe": "Senk<PERSON><PERSON>", "playerHasBeenAliveFor": "{name} ist seit {time} am Leben.", "playerId": "<PERSON><PERSON><PERSON>-<PERSON>", "playerJoinedTheTeam": "{name} ist dem Team beigetreten.", "playerJustConnected": "{name} ist gerade online gekommen.", "playerJustConnectedTo": "{name} wurde gerade mit {server} verbunden.", "playerJustConnectedTracker": "{name} vom Tracker `{tracker}` ist gerade online gekommen.", "playerJustDied": "{name} ist gerade gestorben [{location}].", "playerJustDisconnected": "{name} ist gerade offline gegangen.", "playerJustDisconnectedFrom": "{name} wurde gerade von {server} getrennt.", "playerJustDisconnectedTracker": "{name} vom Tracker `{tracker}` ist gerade offline gegangen.", "playerJustReturned": "{name} ist gerade zurückgekehrt ({time}).", "playerJustWentAfk": "{name} ist gerade AFK gegangen.", "playerLeftTheTeam": "{name} hat das Team verlassen.", "playerNotPairedWithServer": "Anführer-<PERSON><PERSON><PERSON> funk<PERSON>t nicht, weil {name} nicht mit dem Server gekoppelt ist.", "players": "<PERSON><PERSON><PERSON>", "playersSearch": "S<PERSON>lersuche", "plusSign": "Plus-Z<PERSON><PERSON>", "populationPlayers": "Bevölkerung: ({current}/{max}) <PERSON><PERSON>ler.", "populationQueue": "{number} <PERSON><PERSON><PERSON> in der Warteschlange.", "powerPlant": "Kraftwerk", "profile": "Profil", "proxLocation": "{name} ist {distance}m von {caller} in Richtung {direction}° [{location}] entfernt", "quantity": "<PERSON><PERSON>", "questionMark": "Fragezeichen", "ranch": "Ranch", "ratelimited": "RATELIMITED", "reconnectingCap": "WIEDERVERBINDEN", "reconnectingToServer": "WIEDERVERBINDEN MIT SERVER...", "recycle": "Recyceln", "recycleCap": "RECYCLE", "recycler": "Recycler", "remain": "übrig", "removePlayerCap": "SPIELER ENTFERNEN", "removeSwitchCap": "SCHALTER ENTFERNEN", "removedSubscribeItem": "<PERSON><PERSON><PERSON><PERSON> `{name}` wurde de-abon<PERSON><PERSON>.", "research": "<PERSON><PERSON><PERSON>chen", "researchTable": "Forschungstisch", "resetSuccess": "Discord erfolgreich zurückgesetzt.", "responseContainError": "Response contain error property with value: {error}.", "responseIsEmpty": "Response is empty.", "responseIsUndefined": "Response is undefined.", "responseTimeout": "Timeout reached while waiting for response.", "resultRecycling": "Ergebnis des Recyclings", "roleCleared": "rustplusplus-Rolle wurde gelöscht.", "roleSet": "{name} wurde als rustplusplus-Rolle festgelegt.", "rustMonument": "Rust Monument", "rustplusOperational": "RUSTPLUS EINSATZBEREIT.", "safe-zone-recycler": "Safe-Zone-Recycler", "samsite": "FlaRak-Stellung", "satelliteDish": "Parabolantenne", "scrap": "Metallschrott", "searchResult": "Suchergebnis für Gegenstand: **{name}**", "second": "{second} Sekunde", "secondCommandDelay": "{second} Sekunde Befehlsverzögerung.", "seconds": "{seconds} <PERSON><PERSON><PERSON>", "secondsCommandDelay": "{seconds} Sekunden Befehlsverzögerung.", "selectInGamePrefixSetting": "<PERSON><PERSON><PERSON><PERSON> aus, welches Vorzeichen In-Game verwendet werden soll:", "selectLanguageExtendSetting": "<PERSON><PERSON> sic<PERSON>, dass du **/reset discord** ausf<PERSON>hrst, um die neue Sprache zu laden.", "selectLanguageSetting": "<PERSON><PERSON>hle die Sprache, die der Bot verwendet:", "selectMenuValueChange": "Menüauswahl-Interaktion - Kontroll-ID: {id}, Wert: {value}.", "selectTrademarkSetting": "<PERSON><PERSON><PERSON><PERSON> aus, welches Kürzel in jeder In-Game-Nachricht angezeigt werden soll.", "sell": "<PERSON><PERSON><PERSON><PERSON>", "semicolon": "Semikolon", "sentTextToSpeech": "Text-zu-<PERSON><PERSON><PERSON> gesendet.", "server": "Server", "serverId": "Server-ID", "serverInfo": "Server-<PERSON><PERSON>", "serverInvalid": "Die Verbindung zum Server scheint ungültig zu sein. Versuche dich erneut mit dem Server zu koppeln.", "serverJustOffline": "Der Server ist gerade offline gegangen.", "serverJustOnline": "Der Server ist gerade online gegangen.", "serverStatus": "Server-Status", "serviceUnavailable": "Service Unavailable: {error}", "setBotLanguage": "<PERSON><PERSON> die Bot-Sprache auf: {language}.", "seven": "<PERSON><PERSON><PERSON>", "sewerBranch": "Kanalisationszweig", "shouldBotBeMutedSetting": "Soll der Bot im Spiel stummgeschaltet werden?", "shouldCommandsEnabledSetting": "<PERSON>len In-Game-<PERSON><PERSON><PERSON>e aktiviert sein?", "shouldLeaderCommandEnabledSetting": "Soll der Anführerbefehl aktiviert sein?", "shouldLeaderCommandOnlyForPairedSetting": "<PERSON><PERSON> der Anführer-Befehl nur für Personen funktionieren, die mit dem Server gekoppelt sind?", "shouldSmartAlarmNotifyNotConnectedSetting": "Sollen intelligente Alarme auch dann benachrich<PERSON>gen, wenn sie nicht auf dem aktuell verbundenen Rust-Server eingerichtet sind?", "shouldSmartAlarmsNotifyInGameSetting": "Sollen intelligente Alarme In-Game benachrichtigen?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "<PERSON>len intelligente Schalter und intelligente Schalter-Gruppen In-Game benachrichtigen, wenn diese via Discord betätigt werden?", "showingBlacklist": "Anzeigen der Blacklist.", "showingSubscriptionList": "Zeige die Liste der abonnierten Gegenstände an.", "shredder": "<PERSON>hredder", "sirenLight": "Alarm-Lampe", "six": "Se<PERSON>", "slash": "Schrägstrich", "slashCommandInteraction": "Slash-Befehl Interaktion - Discord-Server: {guild}, Kanal: {channel}, <PERSON><PERSON><PERSON>: {user}, <PERSON><PERSON>hl: {command}, Kontroll-ID: {id}.", "slashCommandValueChange": "Slash-Befehl Interaktion - Kontroll-ID: {id}, Wert: {value}.", "slashCommandsSuccessRegister": "Erfolgreich Anwendungsbefehle für Discord-Server: {guildId} registriert.", "slots": "Pl<PERSON><PERSON>", "smallOilRig": "Kleine Ölbohrinsel", "smartAlarm": "Intelligenter Alarm", "smartAlarmEditSuccess": "Intelligenter Alarm `{name}` erfolg<PERSON>ich bearbeitet.", "smartAlarmNotifyExtendSetting": "- Diese Alarm-Benachrichtigungen verwenden den Titel und die Nachricht, die dem Smart Alarm In-Game gegeben wurde.\n- Diese Smart Alarme sind möglicherweise nicht im Alarme-Textkanal in Discord verfügbar.", "smartDeviceNotFound": "{device} konnte nicht gefunden werden! Entweder wurde er zerstört oder {user} hat Zugriff auf den Werkzeugschrank verloren.", "smartSwitch": "<PERSON><PERSON><PERSON>", "smartSwitchAutoDay": "Intelligenter <PERSON> wird nur während des Tages an sein.", "smartSwitchAutoNight": "Intelligenter <PERSON> wird nur während der Nacht an sein.", "smartSwitchAutoOff": "Falls Schalter an ist, wird er in spätestens 5min ausgesch.", "smartSwitchAutoOffAnyOnline": "<PERSON><PERSON><PERSON> wird deaktiviert, wenn irgendein Teammitglied online ist.", "smartSwitchAutoOffProximity": "Schalter wird deaktiviert, wenn Teammitglied in der Nähe ist.", "smartSwitchAutoOn": "Falls Schalter aus ist, wird er in spätestens 5min eingesch.", "smartSwitchAutoOnAnyOnline": "Schalter wird aktiviert, wenn irgendein Teammitglied online ist.", "smartSwitchAutoOnProximity": "Schalter wird aktiviert, wenn Teammitglied in der Nähe ist.", "smartSwitchEditProximityLabel": "Proximity-Einstellung (Meter):", "smartSwitchEditSuccess": "Intelligenter <PERSON> `{name}` erfolg<PERSON>ich bearbeitet.", "smartSwitchNormal": "Intelligenter Schalter funktioniert standardmäßig.", "smilyFace": "<PERSON><PERSON>", "somethingWrongWithConnection": "Bei der Verbindung ist etwas schiefgelaufen.", "southEast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "southOfGrid": "<PERSON><PERSON><PERSON><PERSON>", "southWest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sprinkler": "Sprinkler", "stackSize": "Stapelgröße", "stackSizeOfItem": "Stapelgröße von {item} ist {quantity}x", "status": "Status", "statusNotConnectedToServer": "**STATUS** `NICHT MIT SERVER VERBUNDEN!`", "statusNotElectronicallyConnected": "**STATUS** `NICHT ELEKTRISCH VERBUNDEN!`", "statusNotFound": "**STATUS**: NICHT GEFUNDEN", "steamId": "SteamID", "stoneQuarry": "<PERSON><PERSON><PERSON><PERSON>", "storageMonitor": "Lagermonitor", "storageMonitorEditSuccess": "Lagermonitor `{name}` erfol<PERSON><PERSON><PERSON> bearbeitet.", "streamerMode": "Streamer-Modus", "subscribeToChangesBattlemetrics": "Abonniere diverse Änderungen auf Battlemetrics.", "subscriptionList": "Auflistung der abonnierten Gegenstände", "subscriptionListEmpty": "<PERSON>ine Gegenstände abonniert.", "sulfurQuarry": "Schwe<PERSON><PERSON> Steinbruch", "switches": "<PERSON><PERSON><PERSON>", "teamMember": "<PERSON><PERSON><PERSON><PERSON>", "teamMemberInfo": "Teammitglied-Informationen", "theDome": "Kugeltank", "theIdOfTheItem": "Die ID des Gegenstandes.", "theNameOfTheItem": "Der Name des Gegenstandes.", "theNameOfThePlayer": "Der Name des Spielers.", "three": "<PERSON><PERSON>", "tilde": "<PERSON><PERSON>", "time": "Zeit", "timeBeforeCargoEntersEgress": "{time} bis das Frachtschiff [{location}] in die Austrittsphase eintritt.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} bis gesperrte Kiste auf der großen Ölbohrinsel [{location}] entsperrt wird.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} bis gesperrte Kiste auf der kleinen Ölbohrinsel [{location}] entsperrt wird.", "timeCap": "ZEIT", "timeFormatInvalid": "Zeitformat ungültig.", "timeLeftTimer": "{id}: Verbleibende Zeit: {time}, Nachricht: {message}", "timeSinceAlarmWasTriggered": "Der Alarm {alarm} wurde vor {time} ausgelöst.", "timeSinceCargoLeft": "{time} seit Frachtschiff die Karte verlassen hat.", "timeSinceChinook47OnMap": "{time} seit Transporthubschrauber zuletzt auf der Karte war.", "timeSinceHeavyScientistsOnLarge": "{time} seit schwere Wissenschaftler zuletzt zur großen Ölbohrinsel gerufen wurden.", "timeSinceHeavyScientistsOnSmall": "{time} seit schwere Wissenschaftler zuletzt zur kleinen Ölbohrinsel gerufen wurden.", "timeSinceLast": "{time} seit Letztem.", "timeSinceLastEvent": "{time} seit letztem Ereignis.", "timeSinceLastSinceDestroyedLong": "{time1} seit Patrouillenhubschrauber zuletzt auf der Karte war, {time2} seit er zuletzt abgeschossen wurde ({location}).", "timeSinceLastSinceDestroyedShort": "{time1} seit Letztem.\n{time2} seit Zerstörung ({location}).", "timeSincePatrolHelicopterWasOnMap": "{time} seit Patrouillenhubschrauber zuletzt auf der Karte war.", "timeSinceTravelingVendorWasOnMap": "{time} seit der Reisende Händler auf der Karte war.", "timeSinceWipe": "{time} seit Wipe.", "timeTill": "Dauer bis {event}", "timeTillDaylight": "{time} bis Tagesanbruch.", "timeTillNightfall": "{time} bis Nachteinbruch.", "timeTillStructureDecay": "{time} bis Wand ({type}) zerfällt.", "timeUntilUnlocksAt": "{time} bis Entsperrung [{location}].", "timer": "Timer: {message}.", "timerIdDoesNotExist": "Timer-ID: {id} existiert nicht.", "timerIdInvalid": "Timer-ID ist ungültig.", "timerRemoved": "Timer-ID: {id} wurde entfernt.", "timerSet": "Timer er<PERSON><PERSON><PERSON> für {time}.", "tokensDidNotReplenish": "<PERSON><PERSON><PERSON> did not replenish in time.", "toolCupboard": "Werkzeugschrank", "total": "Gesamt", "tracker": "Tracker", "trackerAddPlayerDesc": "<PERSON><PERSON><PERSON> zu `{tracker}` hi<PERSON><PERSON><PERSON><PERSON>", "trackerRemovePlayerDesc": "<PERSON><PERSON><PERSON><PERSON> von `{tracker}`", "trademarkShownBeforeMessage": "{trademark} wird vor Nachrichten angezeigt.", "trainYard": "Güterbahnhof", "travelingVendor": "<PERSON><PERSON><PERSON>", "travelingVendorDetectedSetting": "Wenn der Reisende Händler erkannt wird, sende eine Benachrichtigung.", "travelingVendorHaltedAt": "Der Reisende Händler hat angehalten [{location}].", "travelingVendorHaltedSetting": "Wenn der Reisende Händler anhält, sende eine Benachrichtigung.", "travelingVendorLeftSetting": "<PERSON><PERSON> der Reisende Händler die Karte verlassen hat, sende eine Benachrichtigung.", "travelingVendorLocatedAt": "Der Reisende Händler hält sich bei {location} auf.", "travelingVendorLeftMap": "Der Reisende Händler hat gerade die Karte verlassen [{location}].", "travelingVendorNotCurrentlyOnMap": "Der Reisende Händler ist derzeit nicht auf der Karte.", "travelingVendorResumedAt": "Der Reisende Händler bewegt sich weiter [{location}].", "travelingVendorSpawnedAt": "Der Reisende Händler ist gespawnt [{location}].", "turnOffCap": "AUSSCHALTEN", "turnOnCap": "EINSCHALTEN", "turningGroupOnOff": "Schalte Gruppe {group} {status}.", "two": "Zwei", "type": "<PERSON><PERSON>", "unavailable": "Nicht verfügbar", "underscore": "<PERSON><PERSON>tric<PERSON>", "underwater": "<PERSON><PERSON> Was<PERSON>", "underwaterLab": "<PERSON>terwasser-Labor", "unhandledRejection": "Unhandled Rejection: {error}", "unknown": "Unbekannt", "unknownInteraction": "Unbekannte Interaktion...", "unmutedCap": "NICHT STUMM", "updateCap": "UPDATE", "upkeep": "Instandhaltung", "upkeepForItem": "Erhaltung für {item} kostet {cost}.", "userAddedToBlacklist": "{user} wurde zur Blacklist hinzugefügt.", "userAlreadyInBlacklist": "{user} ist bereits auf der Blacklist.", "userButtonInteraction": "Schaltflächen-Interaktion - Discord-Server: {guild}, Kanal: {channel}, <PERSON>utzer: {user}, Benutzerdefinierte ID: {customid}, Kontroll-ID: {id}.", "userButtonInteractionSuccess": "Schaltflächen-Interaktion - Kontroll-ID: {id} ERFOLGREICH", "userJustConnected": "{name} ist gerade online gekommen.", "userModalInteraction": "Dialogfenster-Interaktion - Discord-Server: {guild}, Kanal: {channel}, <PERSON><PERSON><PERSON>: {user}, Benutzerdefinierte ID: {customid}, Kontroll-ID: {id}.", "userModalInteractionSuccess": "Dialogfenster-Interaktion - Kontroll-ID: {id} ERFOLGREICH", "userNotInBlacklist": "{user} ist nicht auf der Blacklist.", "userNotRegistered": "{user} ist nicht registriert.", "userPartOfBlacklist": "Kontroll-ID: {id}, {user} ist auf der Blacklist.", "userPartOfBlacklistDiscord": "Gesperrter Benutzer! Discord-Server: {guild}, Kanal: {channel}, <PERSON><PERSON><PERSON>: {user}, <PERSON><PERSON><PERSON><PERSON>: {message}.", "userPartOfBlacklistInGame": "Gesperrter Benutzer! Benutzer: {user}, <PERSON><PERSON><PERSON>t: {message}.", "userRemovedFromBlacklist": "{user} wur<PERSON> entfernt.", "userSaid": "{user} sagte, {text}", "userSelectMenuInteraction": "Menüauswahl-Interaktion - Discord-Server: {guild}, Kanal: {channel}, <PERSON><PERSON><PERSON>: {user}, Benutzerdefinierte ID: {customid}, Kontroll-ID: {id}.", "userSelectMenuInteractionSuccess": "Menüauswahl-Interaktion - Kontroll-ID: {id} ERFOLGREICH", "userTurnedOnOffSmartSwitchFromDiscord": "{user} hat intelligenten <PERSON> {name} via Discord {status}geschaltet.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} hat intelligente Schaltergruppe {name} via Discord {status}geschaltet.", "value": "Wert", "vendingMachine": "Verkaufsautomat", "vendingMachineDetectedSetting": "Wenn ein neuer Verkaufsautomat erkannt wird, sende eine Benachrichtigung.", "voiceCap": "VOICE", "warningCap": "WARNUNG", "waterTreatmentPlant": "Klärwerk", "websiteCap": "WEBSEITE", "websocketClosedBeforeConnection": "WebSocket wurde geschlossen bevor die Verbindung eingerichtet werden konnte.", "westOfGrid": "<PERSON><PERSON>", "wipe": "Wipe", "wipeDetected": "Wipe erkannt!", "yield": "Ertrag", "youAreAlreadyLeader": "Du bist bereits Anführer.", "youAreNotPairedWithServer": "Anführer-<PERSON><PERSON><PERSON> funktion<PERSON> nicht, weil du nicht mit dem Server gekoppelt bist."}