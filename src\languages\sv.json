{"24HoursInGameTimePassed": "24 framgångsrika timmar har passerat i spelet.", "abandonedCabins": "Övergivna Stugor", "abandonedMilitaryBase": "Övergiven Militärbas", "abandonedSupermarket": "Övergiven Stormarknad", "addPlayerCap": "LÄGG TILL SPELARE", "addSwitchCap": "LÄGG TILL STRÖMBRYTARE", "afkCap": "AFK", "airfield": "Flygfält", "alarmHaveNotBeenTriggeredYet": "Alarmet {alarm} har inte blivit utlöst ännu.", "alias": "<PERSON><PERSON>", "aliasAlreadyExist": "Namnet är upptaget.", "aliasIndexCouldNotBeFound": "Namn index kunde inte bli hittad.", "aliasWasAdded": "<PERSON><PERSON> är tillagt.", "aliasWasRemoved": "<PERSON><PERSON> borttaget.", "aliases": "<PERSON><PERSON>", "all": "<PERSON>a", "allTeammatesAreDead": "Alla dina lagkamrater är döda.", "alreadySubscribedToItem": "Prenumererar redan på föremålet {name}.", "ampersand": "<PERSON><PERSON><PERSON><PERSON>", "andMorePlayers": "... och {number} spelare till.", "any": "Any", "apostrophe": "<PERSON><PERSON><PERSON><PERSON>", "arcticResearchBase": "Arktisk Forskningsbas", "asterisk": "Asterisk", "asteriskCctvDesc": "*'s means that you need a numerical code that is different for every map", "atLocation": "På {location}.", "atSign": "Snabel a", "autoDayCap": "AUTO-DAG", "autoNightCap": "AUTO-NATT", "autoOffAnyOnlineCap": "AUTO-OFF-ANY-ONLINE", "autoOffCap": "AUTO AV", "autoOffProximityCap": "AUTO-OFF-PROXIMITY", "autoOnAnyOnlineCap": "AUTO-ON-ANY-ONLINE", "autoOnCap": "AUTO PÅ", "autoOnProximityCap": "AUTO-ON-PROXIMITY", "autoSettingCap": "AUTO-INSTÄLLNING", "automaticallyTurnBackOnOff": " Ändrar automatiskt tillbaka till {status} om {time}.", "automaticallyTurningBackOnOff": "Ändrar automatiskt {device} till {status}.", "autoturret": "Autoturret", "badGateway": "Dålig Gateway: {error}", "banditCamp": "Banditläger", "baseIsUnderAttack": "<PERSON> bas är under attack!", "battlemetricsApiRequestFailed": "Battlemetrics API Request Failed: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics Server {server} failed to update.", "battlemetricsGlobalLoginCap": "GLOBAL INLOGGNING", "battlemetricsGlobalLogoutCap": "GLOBAL UTLOGGNING", "battlemetricsGlobalNameChangesCap": "GLOBAL NAME CHANGES", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "Battlemetrics instance is missing id and name.", "battlemetricsInstanceCouldNotBeFound": "Battlemetrics Instance for {id} could not be found.", "battlemetricsOnlinePlayers": "Battlemetrics Online Players", "battlemetricsPlayersLogin": "Battlemetrics Players Login", "battlemetricsPlayersLogout": "Battlemetrics Players Logout", "battlemetricsPlayersNameChanged": "Battlemetrics Players Name Changed", "battlemetricsServerNameChanged": "Battlemetrics Server Name Changed", "battlemetricsServerNameChangesCap": "SERVER NAME CHANGES", "battlemetricsTrackerNameChangesCap": "TRACKER NAME CHANGES", "battlemetricsTrackerPlayerNameChanged": "Battlemetrics Tracker Player Name Changed", "blacklist": "Blacklist", "boomBox": "Boom Box", "bot": "bot", "broadcaster": "Sändare", "buttonValueChange": "Button Interaction - VerifyId: {id}, Value: {value}.", "buy": "buy", "calculated": "Calculated", "cargoAt": "At {location}.", "cargoLeavingMapAt": "Cargo Ship is leaving the map at {location}.", "cargoLocatedAt": "Cargo Ship is located at {location}.", "cargoNotCurrentlyOnMap": "Lastfartyget finns för närvarande inte på kartan.", "cargoShipDetectedSetting": "<PERSON><PERSON><PERSON> ett lastfartyg upptäcks, skicka en notifikation.", "cargoShipDockingAtHarbor": "<PERSON><PERSON> Lastfartyg lämnade precis kartan vid {location}", "cargoShipDockingAtHarborSetting": "<PERSON><PERSON><PERSON> ett lastfartyg upptäcks, skicka en notifikation.", "cargoShipEgressSetting": "<PERSON><PERSON><PERSON> last<PERSON>get går in i utträdesstadiet, skicka en notifikation.", "cargoShipEntersEgressStage": "Lastfartyget bör vara i utträdesstadiet på {location}.", "cargoShipEntersMap": "<PERSON><PERSON>g kommer in på kartan från {location}.", "cargoShipLeftHarbor": "<PERSON><PERSON> Lastfartyg lämnade precis kartan vid {location}", "cargoShipLeftMap": "<PERSON><PERSON> Lastfartyg lämnade precis kartan vid {location}.", "cargoShipLeftSetting": "<PERSON><PERSON><PERSON> ett lastfartyg har lämnat kartan, skicka en notifikation.", "cargoShipLocated": "<PERSON><PERSON> Lastfartyg ligger vid {location}.", "cargoship": "Lastfartyg", "ceilingLight": "Takljus", "channelNameActivity": "aktiviteter", "channelNameAlarms": "larm", "channelNameCommands": "kommandon", "channelNameEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "channelNameInformation": "information", "channelNameServers": "servrar", "channelNameSettings": "inställningar", "channelNameStorageMonitors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "channelNameSwitchGroups": "strömbrytargrupper", "channelNameSwitches": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "channelNameTeamchat": "lagchatt", "channelNameTrackers": "sp<PERSON><PERSON><PERSON>", "chinook47": "Chinook 47", "chinook47DetectedSetting": "<PERSON><PERSON><PERSON> en Chinook 47 kommer in på kartan, skicka en notifikation.", "chinook47EntersMap": "Chinook 47 kommer in på kartan från {location} för att lämna en låst låda.", "chinook47LeftMap": "Chinook 47 lämnade kartan vid {location}.", "chinook47Located": "Chinook 47 ligger vid {location}.", "chinook47NotOnMap": "Chinook 47 finns inte på kartan just nu.", "christmasLights": "Jul<PERSON><PERSON><PERSON>", "circumflex": "Cirkumflex", "clanTag": "Clan Tag", "codes": "<PERSON><PERSON>", "colon": "Kolon", "comma": "Kommatecken", "commandCap": "KOMMANDO", "commandDelaySetting": "<PERSON><PERSON><PERSON> det finnas en kommandofördröjning? Hur länge?", "commandNotPossibleDiscord": "Kommandot är inte möjligt genom discord.", "commandSyntaxAdd": "add", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "alive", "commandSyntaxArmored": "Bepansrad", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "connection", "commandSyntaxConnections": "connections", "commandSyntaxCraft": "skapa", "commandSyntaxDeath": "death", "commandSyntaxDeaths": "deaths", "commandSyntaxDecay": "decay", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "events", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "language", "commandSyntaxLarge": "large", "commandSyntaxLeader": "leader", "commandSyntaxList": "list", "commandSyntaxMarker": "marker", "commandSyntaxMarkers": "markers", "commandSyntaxMarket": "market", "commandSyntaxMetal": "metal", "commandSyntaxMute": "mute", "commandSyntaxNote": "note", "commandSyntaxNotes": "notes", "commandSyntaxOff": "off", "commandSyntaxOffline": "offline", "commandSyntaxOn": "on", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "player", "commandSyntaxPlayers": "players", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "recycle", "commandSyntaxRemove": "remove", "commandSyntaxResearch": "research", "commandSyntaxSearch": "search", "commandSyntaxSend": "send", "commandSyntaxSmall": "small", "commandSyntaxStack": "stack", "commandSyntaxStatus": "status", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "stone", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "team", "commandSyntaxTime": "time", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendor", "commandSyntaxTwig": "twig", "commandSyntaxUnmute": "unmute", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "upkeep", "commandSyntaxUptime": "uptime", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "wood", "commandsAlarmDesc": "Operationer p<PERSON> <PERSON>.", "commandsAlarmEditDesc": "<PERSON><PERSON>a egenskaperna för ett Smart Larm.", "commandsAlarmEditIdDesc": "ID:t för det <PERSON>a Larmet.", "commandsAlarmEditImageDesc": "<PERSON><PERSON><PERSON> in en bild som bäst representerar Smarta Larmet.", "commandsAliasAddAliasDesc": "The alias to use.", "commandsAliasAddDesc": "Add an alias.", "commandsAliasAddValueDesc": "The command/sequence of characters.", "commandsAliasDesc": "Create an alias for a command/sequence of characters.", "commandsAliasRemoveDesc": "Remove an alias.", "commandsAliasRemoveIndexDesc": "The index of the alias to remove.", "commandsAliasShowDesc": "Show all registered aliases.", "commandsBlacklistAddDesc": "Add user to the blacklist.", "commandsBlacklistDesc": "Blacklist a user from using the bot.", "commandsBlacklistDiscordUserDesc": "The discord user.", "commandsBlacklistRemoveDesc": "Remove user from the blacklist.", "commandsBlacklistShowDesc": "Show blacklisted users.", "commandsBlacklistSteamidDesc": "The steamid of the user.", "commandsCctvDesc": "Display CCTV codes for a monument", "commandsCraftDesc": "Display the cost to craft an item.", "commandsCraftQuantityDesc": "The quantity of items to craft.", "commandsCredentialsAddDesc": "Lägg till FCM-uppgifter.", "commandsCredentialsDesc": "St<PERSON>ll in/rensa FCM-uppgifterna för användarkontot.", "commandsCredentialsRemoveDesc": "Ta bort FCM-uppgifter.", "commandsCredentialsRemoveSteamIdDesc": "SteamId för de FCM-uppgifter som ska tas bort.", "commandsCredentialsSetHosterDesc": "St<PERSON>ll in värd för FCM-uppgifterna.", "commandsCredentialsSetHosterSteamIdDesc": "SteamId för värden av FCM-uppgifterna.", "commandsCredentialsShowDesc": "Visa de registrerade FCM-uppgifterna.", "commandsDecayDesc": "Display the decay time of an item.", "commandsDespawnDesc": "Display the despawn time of an item.", "commandsHelpCommandList": "Kommandolista", "commandsHelpDesc": "Visa hjälpmeddelande.", "commandsHelpHowToCredentials": "Så här registrerar du FCM-uppgifterna", "commandsHelpHowToPairServer": "<PERSON><PERSON> här parar du botten med Rust Server", "commandsItemDesc": "Get the details of an item.", "commandsLeaderDesc": "Ge eller ta ledarskapet från/till en lagkamrat.", "commandsLeaderMemberDesc": "Namnet på lagkamraten.", "commandsMapAllDesc": "Skaffa karta med både monumentnamn och markörer.", "commandsMapCleanDesc": "<PERSON><PERSON><PERSON><PERSON> en tom karta.", "commandsMapDesc": "Skaffa en bild på kartan för den anslutna servern.", "commandsMapMarkersDesc": "<PERSON><PERSON><PERSON>a karta som inkluderar mark<PERSON>.", "commandsMapMonumentsDesc": "Skaffa karta som inkluderar monumentnamn.", "commandsMarketDesc": "Operationer för varuautomater i spelet.", "commandsMarketListDesc": "Visa prenumerationslistan.", "commandsMarketOrderDesc": "The order type.", "commandsMarketSearchDesc": "Sök efter ett föremål i varuautomater.", "commandsMarketSubscribeDesc": "Prenumerera på ett föremål i varuautomater.", "commandsMarketUnsubscribeDesc": "Avsluta prenumerationen på ett föremål i varuautomater.", "commandsPlayersBattlemetricsIdDesc": "The Battlemetrics ID of the server (default: The connected server).", "commandsPlayersDesc": "Få spelar information baserat på Battlemetrics.", "commandsPlayersNameDesc": "Search for a player on Battlemetrics based on player name.", "commandsPlayersPlayerIdDesc": "Search for a player on Battlemetrics based on player id.", "commandsPlayersPlayerIdPlayerIdDesc": "The player id of the player.", "commandsPlayersStatusDesc": "Search for players that are online/offline/any.", "commandsRecycleDesc": "Display the output of recycling an item.", "commandsRecycleQuantityDesc": "The quantity of items to recycle.", "commandsRecycleRecyclerTypeDesc": "The recycler type (recycler, shredder, safe-zone-recycler).", "commandsResearchDesc": "Display the cost to research an item.", "commandsResetAlarmsDesc": "Å<PERSON>täll larmkanalen.", "commandsResetDesc": "Återställ Discord-kanaler.", "commandsResetInformationDesc": "Återställ informationskanalen.", "commandsResetServersDesc": "Återställ serverkanalen.", "commandsResetSettingsDesc": "Återställ inställningskanalen.", "commandsResetStorageMonitorsDesc": "Återställ kanalen för fö<PERSON>ringsmonitorer.", "commandsResetSwitchesDesc": "Å<PERSON>täll strömbrytar och strömbrytargrupps-kanalerna.", "commandsResetTrackersDesc": "Återställ spårningskanalen.", "commandsRoleClearDesc": "Clear the role (to allow everyone to see the rustplusplus channels).", "commandsRoleDesc": "Set/Clear a specific role that will be able to see the rustplusplus category content.", "commandsRoleSetDesc": "<PERSON><PERSON>ll in rollen.", "commandsRoleSetRoleDesc": "The role rustplusplus channels will be visible to.", "commandsStackDesc": "Display the stack size of an item.", "commandsStoragemonitorDesc": "Operationer <PERSON><PERSON><PERSON>.", "commandsStoragemonitorEditDesc": "Redigera egenskaperna för en Förvaringsmonitor.", "commandsStoragemonitorEditIdDesc": "ID:t på Förvaringsmonitor.", "commandsStoragemonitorEditImageDesc": "<PERSON><PERSON><PERSON> in en bild som bäst representerar Förvaringsmonitor.", "commandsSwitchDesc": "Operationer <PERSON><PERSON><PERSON>römbrytare.", "commandsSwitchEditDesc": "<PERSON><PERSON>a egenskaperna för en Smart Strömbrytare", "commandsSwitchEditIdDesc": "ID:t på den Smarta Strömbrytaren.", "commandsSwitchEditImageDesc": "<PERSON><PERSON><PERSON> in en bild som bäst representerar den Smarta Strömbrytaren.", "commandsUpkeepDesc": "Display the upkeep cost of an item.", "commandsUptimeBotDesc": "Display uptime of bot.", "commandsUptimeDesc": "Display uptime of the bot and server.", "commandsUptimeServerDesc": "Display uptime of server.", "commandsVoiceBotJoinedVoice": "<PERSON> Bot has joined the Voicechannel", "commandsVoiceBotLeftVoice": "The <PERSON><PERSON> has left the Voicechannel", "commandsVoiceDesc": "Bot Voice Commands", "commandsVoiceFemale": "Female", "commandsVoiceFemaleDescription": "Sets the voiceactor gender to Female", "commandsVoiceGenderDesc": "Sets the <PERSON><PERSON>'s voiceactor gender.", "commandsVoiceJoin": "Joining voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceJoinDesc": "Joins the Voicechannel", "commandsVoiceLeave": "Leaving voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceLeaveDesc": "Leaves the Voicechannel", "commandsVoiceMale": "Male", "commandsVoiceMaleDescription": "Sets the voiceactor gender to Male", "commandsVoiceNotInVoice": "You are not in a voicechannel", "connect": "<PERSON><PERSON><PERSON>", "connectCap": "ANSLUT", "connected": "Connected", "connectedCap": "ANSLUTEN", "connectedToServer": "ANSLUTEN TILL SERVERN.", "connectingCap": "ANSLUTER", "connectingToServer": "ANSLUTER TILL SERVERN...", "connectionEvents": "Connection Events", "connectionRefusedTo": "Anslutning vägrades till: {id}.", "connectionsCap": "ANSLUTNINGAR", "couldNotAddStepTracers": "Could not add step tracers.", "couldNotAppendMapMarkers": "Det gick inte att lägga till kartmarkörer, rustplus infoinstansen är inte inställd.", "couldNotAppendMapMonuments": "Det gick inte att lägga till kartmonument, rustplus infoinstansen är inte inställd.", "couldNotAppendMapTracers": "Could not append map tracers, rustplus info instance is not set.", "couldNotConnectTo": "Kunde inte ansluta till: {id}.", "couldNotCreateCategory": "Det gick inte att skapa kategorin: {name}.", "couldNotCreateTextChannel": "Det gick inte att skapa textkanalen: {name}.", "couldNotDeferInteraction": "Kunde inte skjuta upp interaktionen.", "couldNotDeleteCategory": "Could not delete category: {categoryId}", "couldNotDeleteChannel": "Could not delete channel: {channelId}", "couldNotDeleteMessage": "Kunde inte radera meddelandet: {message}.", "couldNotFindAnyPlayers": "Kunde inte hitta några spelare.", "couldNotFindCategory": "<PERSON>nde inte hitta kategorin: {category}.", "couldNotFindChannel": "<PERSON>nde inte hitta kanalen: {channel}.", "couldNotFindCraftDetails": "Could not find craft details for {name}.", "couldNotFindDecayDetails": "Could not find decay details for {name}.", "couldNotFindDespawnDetails": "Could not find despawn details for {name}.", "couldNotFindGuild": "Kunde inte hitta guilden: {guildId}.", "couldNotFindLanguage": "<PERSON>nde inte hitta språket: {language}.", "couldNotFindMessage": "<PERSON>nde inte hitta meddelandet: {message}.", "couldNotFindPlayer": "Kunde inte hitta en spelare: {name}.", "couldNotFindPlayerId": "Could not find player with id {id}.", "couldNotFindRecycleDetails": "Could not find recycle details for {name}.", "couldNotFindResearchDetails": "Could not find research details for {name}.", "couldNotFindRole": "<PERSON>nde inte hitta rollen: {roleId}.", "couldNotFindStackDetails": "Could not find stack details for {name}.", "couldNotFindTeammate": "Kunde inte hitta lagkamraten: {name}.", "couldNotFindUpkeepDetails": "Could not find upkeep details for {name}.", "couldNotFindUser": "<PERSON>nde inte hitta användaren: {userId}.", "couldNotGetChannelWithId": "Kunde inte hämta kanalen med id: {id}.", "couldNotIdentifyMember": "Kunde inte identifiera lagkamraten: {name}.", "couldNotPerformBulkDelete": "Kunde inte utföra massradering på kanalen: {channel}.", "couldNotPerformMessageDelete": "Kunde inte utföra meddelanderadering.", "couldNotPerformMessagesFetch": "Kunde inte hämta meddelanden på kanalen: {channel}.", "couldNotRegisterSlashCommands": "Kunde inte registrera Slash-kommandon för guilden: {guildId}. ", "couldNotSetParent": "Kunde inte ställa in förälder för kanalen: {ChannelId}.", "craft": "Craft", "crate": "Låda", "createGroupCap": "SKAPA GRUPP", "createTrackerCap": "SKAPA SPÅRARE", "credentialsAddedSuccessfully": "FCM-uppgifter för steamId: {steamId} har lagts till framgångsrikt!", "credentialsAlreadyRegistered": "FCM-uppgifter för steamId: {steamId} är redan registrerade!", "credentialsCannotStartLiteAlreadyHoster": "Kan inte starta FCM-lyssnare för steamId: {steamId}. Är redan värd.", "credentialsDoNotExist": "FCM-uppgifter för steamId: {steamId} finns inte.", "credentialsHosterNotSetForGuild": "FCM-uppgifter värd är inte inställd för guilden: {id}, vänligen ange en värd.", "credentialsNotRegistered": "FCM-uppgifter för steamId: {steamId} är inte registrerade!", "credentialsNotRegisteredForGuild": "FCM-uppgifter är inte registrerade för guilden: {id}, kan inte starta FCM-lyssnaren.", "credentialsRemovedSuccessfully": "FCM-uppgifterna för steamId: {steamId} togs bort framgångsrikt!", "credentialsSetHosterSuccessfully": "FCM-uppgifter värd satt framgångsrikt till steamId: {steamId}.", "currencySign": "Valutatecken", "currentCommandDelay": "Aktuell kommandofördröjning: {delay} sekunder.", "currentItemHp": "The current HP of the item.", "currentPrefixPlaceholder": "<PERSON>uvarande Prefix: {prefix}.", "customCommand": "Anpassat Kommando", "customTimerEditCargoShipEgressLabel": "Utstigningst<PERSON> fö<PERSON> (sekunder):", "customTimerEditCrateOilRigUnlockLabel": "Oljeplattformens upplåsningstid (sekunder):", "customTimerEditDesc": "Redigering av anpassade timers", "customTimersCap": "ANPASSADE TIMERS", "dash": "Streck Symbol", "dayOfWipe": "Dag {day}", "deathCap": "DÖD", "decay": "Decay", "decayTimeForItem": "Decay time for {item} is {time}.", "decayingCap": "FÖRFALLER", "deleteUnreachableDevicesCap": "DELETE UNREACHABLE DEVICES", "despawnTime": "Despawn Time", "despawnTimeOfItem": "Despawn time of {item} is {time}.", "deviceIsAlreadyOnOff": "{device} is already {status}.", "deviceIsCurrentlyOnOff": "{device} är för n<PERSON> {status}.", "deviceWasTurnedOnOff": "{device} sattes {status}.", "disabledCap": "INAKTIVERAD", "discoFloor": "Disco Golv", "disconnectCap": "KOPPLA IFRÅN", "disconnected": "Disconnected", "disconnectedCap": "BORTKOPPLAD", "disconnectedFromServer": "BORTKOPPLAD FRÅN SERVERN.", "discordCap": "DISCORD", "discordUsers": "Discord Users", "displayInformationBattlemetricsAllOnlinePlayers": "Should all online players from Battlemetrics be displayed in the information channel?", "displayingMap": "Visar {mapName} kartan.", "displayingOnlinePlayers": "<PERSON><PERSON> an<PERSON><PERSON><PERSON> s<PERSON>.", "distanceDirectionGrid": "{distance}m i riktning {direction}° [{grid}].", "doorController": "<PERSON><PERSON><PERSON>", "dot": "<PERSON><PERSON>", "eastOfGrid": "Öster om rutan", "editCap": "REDIGERA", "editing": "Editing", "editingOf": "Editing of {entity}", "egressInTime": "Egress in {time} at {location}.", "eight": "<PERSON><PERSON>", "elevator": "Hiss", "empty": "<PERSON>", "enabledCap": "AKTIVERAD", "entityId": "Entity ID", "equalsSign": "Likhetstecken", "errorCap": "FEL", "errorExecutingCommand": "Det uppstod ett fel när det här kommandot kördes!", "eventCap": "HÄNDELSE", "eventInfo": "Händelseinformation", "exclamationMark": "Utropstecken", "failedToScrapeProfileName": "Det gick inte att hämta profilnamnet: {link}.", "failedToScrapeProfilePicture": "Det gick inte att hämta profilbilden: {link}.", "fcmCredentials": "FCM-uppgifter", "fcmListenerStartHost": "FCM-lyssnare Host startar om 5 sekunder för guildId: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "FCM-lyssnare Lite startar om 5 sekunder för guildId: {guildId}, steamId: {steamId}.", "ferryTerminal": "Ferry Terminal", "fishingVillage": "Fiskeby", "five": "<PERSON><PERSON>", "four": "Fyra", "giantExcavatorPit": "Gigantisk Grävmaskinsgrop", "greaterThanSign": "St<PERSON><PERSON> än tecken", "groupAddSwitchDesc": "Lägg till Strömbrytare till {group}", "groupRemoveSwitchDesc": "<PERSON> bort Strömbrytare från {group}", "harbor": "<PERSON><PERSON>", "hasBeenAliveLongest": "{name} har levt längst ({time}).", "hash": "Hash", "hbhfSensor": "HBHF Sensor", "heart": "Hjärta", "heater": "Värma<PERSON>", "heavyScientistCalledSetting": "<PERSON><PERSON><PERSON> tungt utrustade forskare kallas till Oljeplattformen, skicka en notifikation.", "heavyScientistsCalledLarge": "Tungt utrustade forskare har kallats till Stora Oljeplattformen vid {location}.", "heavyScientistsCalledSmall": "Tungt utrustade forskare har kallats till Lilla Oljeplattformen vid {location}.", "hideTrademark": "<PERSON><PERSON><PERSON><PERSON>", "hoster": "<PERSON><PERSON><PERSON>", "hp": "HP", "hpExceedMax": "Hp {hp} is exceeding max of {max}.", "hqmQuarry": "HQM Stenbrott", "ignoreSetAvatar": "Ignorerade setAvatar", "ignoreSetNickname": "Ignorerade setNickname", "ignoreSetUsername": "Ignorerade setUsername", "inGameBotMessagesMuted": "Botmeddelanden i spelet har dämpats.", "inGameBotMessagesUnmuted": "Botmeddelanden i spelet har odämpats.", "inGameCap": "I SPELET", "inGameEventInfo": "Händelseinformation i spelet", "inGameTeamNotificationsSetting": "Aviseringar om lagkamrater i spelet", "inGameTime": "Tid i spelet: {time}.", "index": "Index", "infoCap": "INFO", "inside": "Inside", "interactionEditReplyFailed": "<PERSON><PERSON><PERSON><PERSON> att redigera interaktionssvaret: {error}.", "interactionInvalidChannel": "Interaktion från en ogiltig kanal.", "interactionReplyFailed": "<PERSON><PERSON><PERSON><PERSON> att svara på interaktionen: {error}.", "interactionUpdateFailed": "<PERSON><PERSON><PERSON><PERSON> att uppdatera interaktionen: {error}.", "invalidBattlemetricsId": "Invalid Battlemetrics ID.", "invalidGuildOrChannel": "Ogiltig guild eller kanal.", "invalidHpInterval": "Invalid HP interval {hp}.", "invalidId": "Ogiltigt ID: {id}.", "invalidStructureType": "Invalid Structure type {type}.", "invalidSubcommand": "Ogiltigt underkommando.", "invalidTimeDistance": "Ogiltigt tidsavstånd: {distance}, föregående: {prevTime}, ny: {newTime}", "isDecaying": "{device} förfaller!", "isNoLongerConnected": "{device} är inte längre elektroniskt kopplad!", "item": "Föremål", "itemAvailableInVendingMachine": "{items} blev precis tillgänglig i en varuautomat vid [{location}].", "itemAvailableNotifyInGameSetting": "<PERSON><PERSON><PERSON> ett föremål från prenumerationslistan blir tillgängligt i en varuautomat, meddela i spelet?", "junkyard": "Skrotupplag", "justSubscribedToItem": "Prenumererade precis på föremålet {name}.", "languageCode": "S<PERSON>råk kod: {code}", "languageLangNotSupported": "Språket {language} stöds inte.", "languageNotSupported": "Språket stöds inte.", "largeBarn": "Stor Lada", "largeFishingVillage": "Stora <PERSON>", "largeOilRig": "Stora Oljeplattformen", "largeWoodBox": "<PERSON>or Trälåda", "lastTrigger": "Last Trigger", "launchSite": "Uppskjutningsplatsen", "leaderAlreadyLeader": "{name} är <PERSON>an la<PERSON>.", "leaderCommandIsDisabled": "Ledar kommandot är inaktiverat i inställningar.", "leaderCommandOnlyWorks": "<PERSON>ar kommandot funkar bara om nuvarande lagledare är {name}.", "leaderTransferred": "lagledarskap överfördes till {name}.", "leavingMapAt": "Leaving at {location}.", "lessThanSign": "Mindre än tecken", "lighthouse": "<PERSON><PERSON><PERSON>", "linkCap": "LÄNK", "location": "Plats", "lockedCrateLargeOilRigUnlocked": "Låst låda vid den stora oljeplattformen vid {location} har låsts upp.", "lockedCrateOilRigUnlockedSetting": "N<PERSON>r en låst låda vid oljeplattformen är upplåst, skicka en notifikation.", "lockedCrateSmallOilRigUnlocked": "Låst låda vid den lilla oljeplattformen vid {location} has låsts upp.", "logDiscordCommand": "Discord Command - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logDiscordMessage": "Discord Message - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logInGameCommand": "{type} - Command: {command}, User: {user}.", "logInGameMessage": "Message: {message}, User: {user}", "logSmartSwitchGroupValueChange": "Smart Switch Group - Value: {value}.", "logSmartSwitchValueChange": "Smart Switch - Value: {value}.", "loggedInAs": "INLOGGAD SOM: {name}", "makeSureApplicationsCommandsEnabled": "Se till att applications.commands är markerat när du skapar inbjudningsadressen.", "map": "Karta", "mapSalt": "<PERSON><PERSON><PERSON><PERSON>", "mapSeed": "Kartfrö", "mapSize": "Kartstorlek", "mapWipeDetectedNotifySetting": "<PERSON><PERSON><PERSON>, ska {group} bli notifierade?", "markerAdded": "<PERSON><PERSON><PERSON> {name} vid [{location}] lades till.", "markerDoesNotExist": "<PERSON><PERSON><PERSON> {name} finns inte.", "markerLocation": "<PERSON><PERSON><PERSON> {name} vid [{location}] är {distance}m från {player} i riktning {direction}°.", "markerRemoved": "<PERSON><PERSON><PERSON> {name} vid [{location}] togs bort.", "message": "Meddelande", "messageCap": "MESSAGE", "messageDeletedIn30": "Detta meddelande kommer att raderas om 30 sekunder.", "messageEditFailed": "Redigering av meddelandet misslyckades: {error}", "messageReplyFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> misslyckades: {error}", "messageSendFailed": "Sändning av meddelande misslyckades: {error}", "messageWasSent": "Meddelandet skickades.", "militaryTunnel": "<PERSON><PERSON><PERSON><PERSON>", "miningOutpost": "Gruvutpost", "missileSilo": "Missile Silo", "missingArguments": "<PERSON><PERSON><PERSON> argument.", "missingPermission": "Du har inte behörighet att göra detta.", "missingTimerMessage": "Timermeddelande saknas.", "modalValueChange": "Modal Interaction - VerifyId: {id}, Value: {value}.", "more": "mer", "morePlayers": "{players} ...{number} mer.", "mutedCap": "DÄMPAD", "name": "<PERSON><PERSON>", "nameChangeHistory": "Namnändringshistorik", "new": "New", "newVendingMachine": "Ny varuautomat finns vid {location}.", "newsCap": "NYHETER", "noActiveTimers": "Inga aktiva timers.", "noCommandDelay": "Ingen kommandofördröjning.", "noCommunicationSmartSwitch": "Kunde inte kommunicera med Smarta Strömbrytaren: {name}", "noData": "Ingen data.", "noDataOnLargeOilRig": "Ingen data på stora oljeplattformen.", "noDataOnSmallOilRig": "Ingen data på lilla oljeplattformen.", "noDelayCap": "INGEN FÖRDRÖJNING", "noItemFound": "Föremålet kunde inte hittas i någon varuautomat.", "noItemWithIdFound": "Inget föremål med ID:t {id} kunde hittas.", "noItemWithNameFound": "Inget föremål med namnet {name} kunde hittas.", "noNameIdGiven": "Inget 'namn' eller 'id' gavs.", "noOneIsAfk": "Ingen är AFK.", "noOneIsOffline": "Ingen är inte ansluten.", "noOneIsOnline": "Ingen är ansluten.", "noRegisteredConnectionEvents": "Inga registrerade anslutningshändelser ännu.", "noRegisteredConnectionEventsUser": "Inga registrerade anslutningshändelser för {user}.", "noRegisteredDeathEvents": "Inga registrerade dödshändelser än.", "noRegisteredDeathEventsUser": "Inga registrerade dödshä<PERSON>lser för {user}.", "noRegisteredEvents": "No registered events yet.", "noRegisteredMarkers": "Inga registrerade mark<PERSON>.", "noSavedNotes": "Inga sparade anteckningar.", "noToolCupboardWereFound": "Inga verktygsskåps monitorer hittades.", "none": "Ingen", "northEast": "<PERSON><PERSON><PERSON>", "northOfGrid": "<PERSON>r om rutan", "northWest": "<PERSON><PERSON><PERSON><PERSON>", "notAValidOrderType": "{order} is not a valid order type.", "notActive": "Inte aktiv.", "notConnectedToRustServer": "Inte för närvarande ansluten till en rust server.", "notExistInSubscription": "Föremålet {name} finns inte i prenumerationslistan.", "notFoundCap": "INTE HITTAD", "notPartOfRole": "Du är inte del av rollen {role}, d<PERSON><PERSON><PERSON><PERSON> kan du inte använda botkommandon.", "notShowingCap": "VISAR INTE", "noteCap": "ANTECKNINGAR", "noteIdDoesNotExist": "Antecknings ID: {id} finns inte.", "noteIdInvalid": "Antecknings ID är ogiltigt.", "noteIdWasRemoved": "Antecknings ID: {id} togs bort.", "noteSaved": "Anteckning sparad.", "offCap": "AV", "offline": "Offline", "offlineTime": "Offline time", "oilRig": "Oljeplattform", "old": "Old", "onCap": "PÅ", "one": "En", "online": "Online", "onlineTime": "Online time", "onlyOneInTeam": "Du är ensam i laget.", "outpost": "Utpost", "outside": "Outside", "oxumsGasStation": "Oxums bensinstation", "pairing": "<PERSON><PERSON>", "patrolHelicopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "patrolHelicopterDestroyedSetting": "<PERSON><PERSON><PERSON> Patrullhelikoptern förstörs, skicka en notifikation.", "patrolHelicopterDetectedSetting": "<PERSON><PERSON><PERSON>hel<PERSON><PERSON> upptäcks, skicka en notifikation.", "patrolHelicopterEntersMap": "Patrullhelikoptern kommer in på kartan från {location}.", "patrolHelicopterLeftMap": "Patrullhelikoptern lämnade precis kartan vid {location}.", "patrolHelicopterLeftSetting": "<PERSON><PERSON><PERSON> Patrullhelikoptern lämnar kartan, skicka en notifikation.", "patrolHelicopterLocatedAt": "Patrullhelikoptern finns vid {location}.", "patrolHelicopterNotCurrentlyOnMap": "Patrullhelikoptern finns för närvarande inte på kartan.", "patrolHelicopterTakenDown": "Patrullhelikoptern togs ner vid {location}.", "percentSign": "Precenttecken", "pipe": "<PERSON><PERSON><PERSON>", "playerHasBeenAliveFor": "{name} har levt i {time}.", "playerId": "Player ID", "playerJoinedTheTeam": "{name} gick med i laget.", "playerJustConnected": "{name} an<PERSON><PERSON><PERSON><PERSON> precis.", "playerJustConnectedTo": "{name} an<PERSON><PERSON><PERSON><PERSON> precis till {server}.", "playerJustConnectedTracker": "{name} just connected from tracker {tracker}.", "playerJustDied": "{name} dog precis vid {location}.", "playerJustDisconnected": "{name} kopplade precis från.", "playerJustDisconnectedFrom": "{name} kopplade precis från servern {server}.", "playerJustDisconnectedTracker": "{name} just disconnected from tracker {tracker}.", "playerJustReturned": "{name} kom precis tillbaka ({time}).", "playerJustWentAfk": "{name} gick precis AFK.", "playerLeftTheTeam": "{name} lämna<PERSON> laget.", "playerNotPairedWithServer": "Leader-kommandot fungerar inte eftersom {name} inte har parat med servern.", "players": "<PERSON><PERSON><PERSON>", "playersSearch": "Players Search", "plusSign": "Plustecken", "populationPlayers": "Population: ({current}/{max}) spelare.", "populationQueue": "{number} spelare i kö.", "powerPlant": "Kraftverk", "profile": "Profile", "proxLocation": "{name} är {distance}m från {caller} i riktning {direction}° [{location}]", "quantity": "Kvantitet", "questionMark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranch": "Ranch", "ratelimited": "HASTIGHETSBEGRÄNSAD", "reconnectingCap": "ÅTERANSLUTER", "reconnectingToServer": "ÅTERANSLUTER TILL SERVERN...", "recycle": "Recycle", "recycleCap": "ÅTERVINNA", "recycler": "Recycler", "remain": "kvar", "removePlayerCap": "TA BORT SPELARE", "removeSwitchCap": "TA BORT STRÖMBRYTARE", "removedSubscribeItem": "Föremålet {name} har tagits bort från prenumerationslistan.", "research": "Research", "researchTable": "Research Table", "resetSuccess": "Återställning av Discord var framgångsrikt.", "responseContainError": "<PERSON><PERSON><PERSON> innehåller felegenskap med värde: {error}.", "responseIsEmpty": "<PERSON><PERSON><PERSON> är tomt.", "responseIsUndefined": "<PERSON><PERSON><PERSON> är odefinierat.", "responseTimeout": "Timeout nåddes i väntan på svar.", "resultRecycling": "Resultat av återvinning", "roleCleared": "rustplusplus role has been cleared.", "roleSet": "rustplusplus role has been set to {name}.", "rustMonument": "Rust Monument", "rustplusOperational": "RUSTPLUS OPERATIV.", "safe-zone-recycler": "Safe Zone Recycler", "samsite": "SAM site", "satelliteDish": "Parabolantenn", "scrap": "Scrap", "searchResult": "Sök resultat för föremålet: **{name}**", "second": "{second} sekund", "secondCommandDelay": "{second} sekund kommandof<PERSON><PERSON><PERSON><PERSON><PERSON>.", "seconds": "{seconds} sekunder", "secondsCommandDelay": "{seconds} se<PERSON>nder kommand<PERSON><PERSON><PERSON>.", "selectInGamePrefixSetting": "Välj vilket kommandoprefix som skall användas i spelet:", "selectLanguageExtendSetting": "Se till att du kör **/reset discord** för att lyckas ladda det nya språket.", "selectLanguageSetting": "Välj vilket språk som boten skall använda:", "selectMenuValueChange": "Select Menu Interaction - VerifyId: {id}, Value: {value}.", "selectTrademarkSetting": "Välj vilket varumärke som ska visas i varje meddelande i spelet.", "sell": "sell", "semicolon": "Semikolon", "sentTextToSpeech": "Skicka Text-Till-Tal.", "server": "server", "serverId": "Server ID", "serverInfo": "Server information", "serverInvalid": "Anslutningen till servern verkar vara ogiltig. Försök att återkoppla till servern.", "serverJustOffline": "Servern gick precis offline.", "serverJustOnline": "<PERSON>n gick precis online.", "serverStatus": "Server Status", "serviceUnavailable": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>gäng<PERSON>g: {error}", "setBotLanguage": "<PERSON><PERSON><PERSON> in bot-språket till: {language}.", "seven": "<PERSON><PERSON>", "sewerBranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shouldBotBeMutedSetting": "Ska boten dämpas i spelet?", "shouldCommandsEnabledSetting": "Ska kommandon i spelet aktiveras?", "shouldLeaderCommandEnabledSetting": "Ska leader kommandot vara aktiverat?", "shouldLeaderCommandOnlyForPairedSetting": "<PERSON><PERSON> ledarkommandot endast fungera för personer som är parade med servern?", "shouldSmartAlarmNotifyNotConnectedSetting": "Ska Smarta Larm meddela även om de inte är inställda på den anslutna rust servern?", "shouldSmartAlarmsNotifyInGameSetting": "Ska Smarta Larm notifiera i spelet?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Should Smart Switches and Smart Switch Groups notify In-Game when they are changed from discord?", "showingBlacklist": "Showing the blacklist.", "showingSubscriptionList": "Visar prenumerationslistan.", "shredder": "Shredder", "sirenLight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "six": "Sex", "slash": "Snedstreck", "slashCommandInteraction": "Slash Command Interaction - Guild: {guild}, Channel: {channel}, User: {user}, Command: {command}, VerifyId: {id}.", "slashCommandValueChange": "Slash Command Interaction - VerifyId: {id}, Value: {value}.", "slashCommandsSuccessRegister": "Registrerade application commands framgångsrikt för guilden: {guildId}.", "slots": "Slots", "smallOilRig": "Lilla O<PERSON>plattform<PERSON>", "smartAlarm": "Smart Larm", "smartAlarmEditSuccess": "Redigerade det Smarta Larmet {name} framgångsrikt.", "smartAlarmNotifyExtendSetting": "Dessa la<PERSON>meddelanden kommer att använda titeln och meddelandet som ges till det Smarta Larmet i spelet.\n- Dessa Smarta Larm kanske inte är tillgängliga i larmtextkanalen i oenighet.", "smartDeviceNotFound": "{device} kunde inte hittas! Antingen har den förstörts eller så har {user} förlorat åtkomst till verktygsskåpet.", "smartSwitch": "<PERSON> Strömbrytare", "smartSwitchAutoDay": "Smart Strömbrytare kommer endast att vara aktiv under dagen.", "smartSwitchAutoNight": "Smart Strömbrytare kommer endast att vara aktiv under natten.", "smartSwitchAutoOff": "Smart Switch will automatically go inactive during update cycle.", "smartSwitchAutoOffAnyOnline": "Smart Switch will automatically go inactive if any teammate is online.", "smartSwitchAutoOffProximity": "Smart Switch will automatically go inactive if teammate is in proximity.", "smartSwitchAutoOn": "Smart Switch will automatically go active during update cycle.", "smartSwitchAutoOnAnyOnline": "Smart Switch will automatically go active if any teammate is online.", "smartSwitchAutoOnProximity": "Smart Switch will automatically go active if teammate is in proximity.", "smartSwitchEditProximityLabel": "Proximity Setting (meters):", "smartSwitchEditSuccess": "Den Smarta Strömbrytaren {name} har redigerats framgångsrikt.", "smartSwitchNormal": "Den Smarta Strömbrytaren fungerar som vanligt.", "smilyFace": "<PERSON>", "somethingWrongWithConnection": "<PERSON><PERSON><PERSON> gick fel med anslutningen.", "southEast": "<PERSON><PERSON><PERSON><PERSON>", "southOfGrid": "<PERSON><PERSON><PERSON> om rutan", "southWest": "Sydväst", "sprinkler": "Sprinkler", "stackSize": "Stack Size", "stackSizeOfItem": "Stack size of {item} is {quantity}x.", "status": "Status", "statusNotConnectedToServer": "**STATUS** `INTE ANSLUTEN TILL SERVERN!`", "statusNotElectronicallyConnected": "**STATUS** `INTE ELEKTRONISKT KOPPLAD!`", "statusNotFound": "**STATUS**: INTE HITTAD", "steamId": "SteamID", "stoneQuarry": "St<PERSON><PERSON>tt", "storageMonitor": "Förvaringsmonitor", "storageMonitorEditSuccess": "Förvaringsmonitorn {name} har redigerats framgångsrikt.", "streamerMode": "Streamer Mode", "subscribeToChangesBattlemetrics": "Subscribe to different changes on Battlemetrics.", "subscriptionList": "Prenumerationslistan", "subscriptionListEmpty": "Föremåls prenumerationslistan är tom.", "sulfurQuarry": "<PERSON><PERSON><PERSON> sten<PERSON>tt", "switches": "Strömbrytare", "teamMember": "Lagkamrat", "teamMemberInfo": "Lagkamratsinformation", "theDome": "Kupolen", "theIdOfTheItem": "The id of the item.", "theNameOfTheItem": "The name of the item.", "theNameOfThePlayer": "The name of the player.", "three": "Tre", "tilde": "<PERSON><PERSON>", "time": "Tid", "timeBeforeCargoEntersEgress": "{time} before Cargo Ship at {location} enters egress stage.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} innan låst låda på den stora oljeplattformen ({location}) l<PERSON>ses upp.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} innan låst låda på den lilla oljeplattformen ({location}) låses upp.", "timeCap": "TID", "timeFormatInvalid": "Tidsformat är ogiltigt.", "timeLeftTimer": "{id}: Tid kvar: {time}, Meddelande: {message}", "timeSinceAlarmWasTriggered": "The alarm {alarm} was triggered {time} ago.", "timeSinceCargoLeft": "{time} sedan lastfartyget lämnade kartan.", "timeSinceChinook47OnMap": "{time} sedan se<PERSON><PERSON> Chinook 47 fanns på kartan.", "timeSinceHeavyScientistsOnLarge": "{time} sedan tungt utrustade forskare senast kallades till stora oljeplattformen.", "timeSinceHeavyScientistsOnSmall": "{time} sedan tungt utrustade forskare senast kallades till lilla oljeplattformen.", "timeSinceLast": "{time} sedan sist.", "timeSinceLastEvent": "{time} sedan senaste hä<PERSON>.", "timeSinceLastSinceDestroyedLong": "{time1} since the last Patrol Helicopter was on the map, {time2} since it last got downed{location}.", "timeSinceLastSinceDestroyedShort": "{time1} since last.\n{time2} since destroyed{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} sedan patrullhelikoptern fanns på kartan.", "timeSinceTravelingVendorWasOnMap": "{time} since the Traveling Vendor was on the map.", "timeSinceWipe": "{time} sedan kartan rensades.", "timeTill": "Tid till {event}", "timeTillDaylight": "{time} innan dag.", "timeTillNightfall": "{time} innan natt.", "timeTillStructureDecay": "{time} before {type} wall decay.", "timeUntilUnlocksAt": "{time} tills l<PERSON><PERSON> upp vid {location}.", "timer": "Timer: {message}.", "timerIdDoesNotExist": "Timer ID: {id} finns inte.", "timerIdInvalid": "Timer ID är og<PERSON>.", "timerRemoved": "Timer ID: {id} togs bort.", "timerSet": "<PERSON>r instä<PERSON>d på {time}.", "tokensDidNotReplenish": "Tokens fylldes inte på i tid.", "toolCupboard": "Verktygsskåp", "total": "Total", "tracker": "Tracker", "trackerAddPlayerDesc": "<PERSON><PERSON><PERSON> till spelare till {tracker}", "trackerRemovePlayerDesc": "Ta bort spelare från {tracker}", "trademarkShownBeforeMessage": "{trademark} kommer att visas före meddelanden.", "trainYard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travelingVendor": "Traveling Vendor", "travelingVendorDetectedSetting": "When the Traveling Vendor is detected, send a notification.", "travelingVendorHaltedAt": "The Traveling Vendor stopped at {location}.", "travelingVendorHaltedSetting": "When the Traveling Vendor stops moving, send a notification.", "travelingVendorLeftSetting": "When the Traveling Vendor left the map, send a notification.", "travelingVendorLocatedAt": "The Traveling Vendor is located at {location}.", "travelingVendorLeftMap": "The Traveling Vendor just left the map at {location}.", "travelingVendorNotCurrentlyOnMap": "The Traveling Vendor is not currently on the map.", "travelingVendorResumedAt": "The Traveling Vendor resumed moving at {location}.", "travelingVendorSpawnedAt": "The Traveling Vendor spawned at {location}.", "turnOffCap": "STÄNG AV", "turnOnCap": "SÄTT PÅ", "turningGroupOnOff": "Sätter gruppen {group} {status}.", "two": "Två", "type": "<PERSON><PERSON>", "unavailable": "Otillgänglig", "underscore": "Understreck", "underwater": "Underwater", "underwaterLab": "Undervattenslaboratorier", "unhandledRejection": "Ohanterat avslag: {error}", "unknown": "Okä<PERSON>", "unknownInteraction": "Okänd interaktion...", "unmutedCap": "ODÄMPAD", "updateCap": "UPDATE", "upkeep": "<PERSON><PERSON><PERSON><PERSON>", "upkeepForItem": "Upkeep for {item} is {cost}.", "userAddedToBlacklist": "{user} was added to blacklist.", "userAlreadyInBlacklist": "{user} already in blacklist.", "userButtonInteraction": "Button Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Button Interaction - VerifyId: {id} SUCCESS", "userJustConnected": "{name} an<PERSON><PERSON><PERSON><PERSON> precis.", "userModalInteraction": "Modal Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Modal Interaction - VerifyId: {id} SUCCESS", "userNotInBlacklist": "{user} not in blacklist.", "userNotRegistered": "{user} är inte registrerad.", "userPartOfBlacklist": "VerifyId: {id}, {user} is part of the blacklist.", "userPartOfBlacklistDiscord": "Blacklisted User! Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "userPartOfBlacklistInGame": "Blacklisted User! User: {user}, Message: {message}.", "userRemovedFromBlacklist": "{user} was removed from blacklist.", "userSaid": "{user} sa, {text}", "userSelectMenuInteraction": "Select Menu Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Select Menu Interaction - VerifyId: {id} SUCCESS", "userTurnedOnOffSmartSwitchFromDiscord": "{user} turned Smart Switch {name} {status} from discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} turned Smart Switch Group {name} {status} from discord.", "value": "Value", "vendingMachine": "Varuautomat", "vendingMachineDetectedSetting": "<PERSON><PERSON>r en ny varuautomat upptäcks, skicka en notifikation.", "voiceCap": "VOICE", "warningCap": "VARNING", "waterTreatmentPlant": "Vattenreningsverk", "websiteCap": "HEMSIDA", "websocketClosedBeforeConnection": "WebSocket stängdes innan en anslutning upprättades.", "westOfGrid": "<PERSON><PERSON>st om rutan", "wipe": "Ren<PERSON>ning", "wipeDetected": "Rensning upptäcktes!", "yield": "Yield", "youAreAlreadyLeader": "<PERSON> <PERSON>r redan la<PERSON>.", "youAreNotPairedWithServer": "Leader-kommand<PERSON> fungerar inte eftersom du inte är parad med servern."}