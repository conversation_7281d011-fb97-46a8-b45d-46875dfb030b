{"24HoursInGameTimePassed": "Oyun içi 24 saat başarıyla geçildi.", "abandonedCabins": "<PERSON><PERSON><PERSON>", "abandonedMilitaryBase": "<PERSON><PERSON>", "abandonedSupermarket": "Süpermarket", "addPlayerCap": "OYUNCU EKLE", "addSwitchCap": "ŞALTER EKLE", "afkCap": "AFK", "airfield": "Havaalanı", "alarmHaveNotBeenTriggeredYet": "The alarm {alarm} have not been triggered yet.", "alias": "<PERSON><PERSON>", "aliasAlreadyExist": "Alias already exist.", "aliasIndexCouldNotBeFound": "Alias index could not be found.", "aliasWasAdded": "<PERSON><PERSON> was added.", "aliasWasRemoved": "<PERSON><PERSON> was removed.", "aliases": "Aliases", "all": "<PERSON><PERSON><PERSON><PERSON>", "allTeammatesAreDead": "Tüm takım arkadaşların ölü.", "alreadySubscribedToItem": "{name} <PERSON><PERSON><PERSON> zaten takip ediliyor.", "ampersand": "Ve", "andMorePlayers": "... ve {number} daha o<PERSON>.", "any": "Any", "apostrophe": "<PERSON><PERSON><PERSON>", "arcticResearchBase": "Arktik Araştırma Üssü", "asterisk": "Yıld<PERSON>z", "asteriskCctvDesc": "*'s means that you need a numerical code that is different for every map", "atLocation": "{location} konumunda.", "atSign": "Kuyruklu 'a'", "autoDayCap": "OTO-GÜNDÜZ", "autoNightCap": "OTO-GECE", "autoOffAnyOnlineCap": "AUTO-OFF-ANY-ONLINE", "autoOffCap": "AUTO-OFF", "autoOffProximityCap": "AUTO-OFF-PROXIMITY", "autoOnAnyOnlineCap": "AUTO-ON-ANY-ONLINE", "autoOnCap": "AUTO-ON", "autoOnProximityCap": "AUTO-ON-PROXIMITY", "autoSettingCap": "OTO AYARLAR: ", "automaticallyTurnBackOnOff": " {time} içinde {status} durumuna otomatik olarak ayarlandı.", "automaticallyTurningBackOnOff": "{device}, {status} durumuna geri ayarlandı.", "autoturret": "<PERSON><PERSON>", "badGateway": "<PERSON><PERSON><PERSON><PERSON> yol: {error}", "banditCamp": "<PERSON><PERSON><PERSON>", "baseIsUnderAttack": "Üssün saldırı altında!", "battlemetricsApiRequestFailed": "Battlemetrics API Request Failed: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics Server {server} failed to update.", "battlemetricsGlobalLoginCap": "KÜRESEL OTURUMU AÇ", "battlemetricsGlobalLogoutCap": "KÜRESEL OTURUMU KAPAT", "battlemetricsGlobalNameChangesCap": "KÜRESEL İSİM DEĞİŞİKLİKLERİ", "battlemetricsId": "Battlemetrics <PERSON><PERSON>", "battlemetricsIdAndNameMissing": "Battlemetrics instance is missing id and name.", "battlemetricsInstanceCouldNotBeFound": "Battlemetrics Instance for {id} could not be found.", "battlemetricsOnlinePlayers": "Battlemetrics Online Players", "battlemetricsPlayersLogin": "Battlemetrics Players Login", "battlemetricsPlayersLogout": "Battlemetrics Players Logout", "battlemetricsPlayersNameChanged": "Battlemetrics Players Name Changed", "battlemetricsServerNameChanged": "Battlemetrics Server Name Changed", "battlemetricsServerNameChangesCap": "SUNUCU ADI DEĞİŞİKLİKLERİ", "battlemetricsTrackerNameChangesCap": "TAKİPÇİ ADI DEĞİŞİKLİKLERİ", "battlemetricsTrackerPlayerNameChanged": "Battlemetrics Tracker Player Name Changed", "blacklist": "<PERSON>e", "boomBox": "Boom Box", "bot": "bot", "broadcaster": "Yayıncı", "buttonValueChange": "Button Interaction - VerifyId: {id}, Value: {value}.", "buy": "<PERSON><PERSON>n al", "calculated": "Hesaplandı", "cargoAt": "{location} konumunda.", "cargoLeavingMapAt": "Kargo Gemisi haritadan {location} konumundan ayrılıyor.", "cargoLocatedAt": "<PERSON><PERSON> {location} konumunda.", "cargoNotCurrentlyOnMap": "Kargo gemisi haritada değil.", "cargoShipDetectedSetting": "Kargo gemisi görüldüğünde bildirim gönder.", "cargoShipDockingAtHarbor": "Cargo ship just docked at the Harbor at {location}", "cargoShipDockingAtHarborSetting": "When Cargo Ship is docked at a harbor, send a notification.", "cargoShipEgressSetting": "Kargo gemisi çıkış aşamasına geçtiğinde bildirim gönder.", "cargoShipEntersEgressStage": "Kargo gemisi {location} geldiğinde tahminen çıkış aşamasına geçecek.", "cargoShipEntersMap": "Kargo gemisi haritaya {location} konunmundan giriş yapacak.", "cargoShipLeftHarbor": "Cargo ship just left the Harbor at {location}", "cargoShipLeftMap": "Kargo gemisi {location} konumundan haritadan ayrıldı.", "cargoShipLeftSetting": "Kargo gemisi haritadan ayrıldığında bildirim gönder.", "cargoShipLocated": "Kargo gemisi {location} konumunda.", "cargoship": "<PERSON><PERSON> gemisi", "ceilingLight": "<PERSON><PERSON>", "channelNameActivity": "<PERSON><PERSON><PERSON>", "channelNameAlarms": "alarmlar", "channelNameCommands": "komutlar", "channelNameEvents": "o<PERSON><PERSON>", "channelNameInformation": "bilgi", "channelNameServers": "sunucular", "channelNameSettings": "<PERSON><PERSON><PERSON>", "channelNameStorageMonitors": "<PERSON><PERSON><PERSON>", "channelNameSwitchGroups": "Grupları değiştir", "channelNameSwitches": "<PERSON><PERSON><PERSON><PERSON>", "channelNameTeamchat": "<PERSON><PERSON>", "channelNameTrackers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chinook47": "Chinook 47", "chinook47DetectedSetting": "Chinook 47 haritaya girdiğinde bildirim gönder.", "chinook47EntersMap": "Chinook 47 {location} konumundan kilitli sandık bırakmak için haritaya giriş yaptı.", "chinook47LeftMap": "Chinook 47 {location} konumundan haritadan ayrıldı..", "chinook47Located": "Chinook 47 {location} konumunda.", "chinook47NotOnMap": "Chinook 47 haritada değil.", "christmasLights": "<PERSON>", "circumflex": "Düzeltme İşareti", "clanTag": "Klan Etiketi", "codes": "<PERSON><PERSON><PERSON>", "colon": "Kolon", "comma": "Virg<PERSON><PERSON>", "commandCap": "KOMUT", "commandDelaySetting": "<PERSON><PERSON><PERSON> gecikmesi olsun mu? ne kadar olsun?", "commandNotPossibleDiscord": "Discord üzerinden komut verilemez.", "commandSyntaxAdd": "add", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "alive", "commandSyntaxArmored": "zırhlı", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "connection", "commandSyntaxConnections": "connections", "commandSyntaxCraft": "<PERSON><PERSON>", "commandSyntaxDeath": "death", "commandSyntaxDeaths": "deaths", "commandSyntaxDecay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "language", "commandSyntaxLarge": "large", "commandSyntaxLeader": "leader", "commandSyntaxList": "list", "commandSyntaxMarker": "marker", "commandSyntaxMarkers": "markers", "commandSyntaxMarket": "market", "commandSyntaxMetal": "metal", "commandSyntaxMute": "mute", "commandSyntaxNote": "note", "commandSyntaxNotes": "notes", "commandSyntaxOff": "kapalı", "commandSyntaxOffline": "offline", "commandSyntaxOn": "açık", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "player", "commandSyntaxPlayers": "players", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "recycle", "commandSyntaxRemove": "remove", "commandSyntaxResearch": "research", "commandSyntaxSearch": "search", "commandSyntaxSend": "send", "commandSyntaxSmall": "small", "commandSyntaxStack": "stack", "commandSyntaxStatus": "status", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "stone", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "team", "commandSyntaxTime": "time", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendor", "commandSyntaxTwig": "twig", "commandSyntaxUnmute": "unmute", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "upkeep", "commandSyntaxUptime": "uptime", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "wood", "commandsAlarmDesc": "Akıllı Alarm işlemleri.", "commandsAlarmEditDesc": "Akıllı Alarm açıklamasını düzenle.", "commandsAlarmEditIdDesc": "Akıllı Alarm Kimliği.", "commandsAlarmEditImageDesc": "Akıllı Alarm resmi a<PERSON>.", "commandsAliasAddAliasDesc": "The alias to use.", "commandsAliasAddDesc": "Add an alias.", "commandsAliasAddValueDesc": "<PERSON><PERSON><PERSON>/dizi karak<PERSON>i.", "commandsAliasDesc": "Bir komut karakter/dizi i<PERSON>in bir takma ad oluşturun.", "commandsAliasRemoveDesc": "Bir takma adını kaldırın.", "commandsAliasRemoveIndexDesc": "The index of the alias to remove.", "commandsAliasShowDesc": "<PERSON><PERSON><PERSON> kayıtlı takma adları göster.", "commandsBlacklistAddDesc": "Kullanıcıyı kara listeye ekle.", "commandsBlacklistDesc": "Blacklist a user from using the bot.", "commandsBlacklistDiscordUserDesc": "Discord kullanıcısı.", "commandsBlacklistRemoveDesc": "Remove user from the blacklist.", "commandsBlacklistShowDesc": "Show blacklisted users.", "commandsBlacklistSteamidDesc": "The steamid of the user.", "commandsCctvDesc": "Display CCTV codes for a monument", "commandsCraftDesc": "Display the cost to craft an item.", "commandsCraftQuantityDesc": "The quantity of items to craft.", "commandsCredentialsAddDesc": "FCM Bilgilerini ekle.", "commandsCredentialsDesc": "Kullanıcı hesabı için FCM Bilgilerini ayarla/temizle.", "commandsCredentialsRemoveDesc": "FCM Bilgilerini temizle.", "commandsCredentialsRemoveSteamIdDesc": "Kaldırılacak kimlik bilgilerinin Steam64ID bilgisi.", "commandsCredentialsSetHosterDesc": "FCM Bilgi sağlayıcısını ayarla.", "commandsCredentialsSetHosterSteamIdDesc": "FCM bilgi sağlayıcısının Steam64ID bilgisi.", "commandsCredentialsShowDesc": "Kayıtlı FCM bilgisini göster.", "commandsDecayDesc": "Display the decay time of an item.", "commandsDespawnDesc": "Display the despawn time of an item.", "commandsHelpCommandList": "<PERSON><PERSON><PERSON>", "commandsHelpDesc": "<PERSON>ım mesajını göster.", "commandsHelpHowToCredentials": "Kimlik Bilgileri Nasıl <PERSON>r", "commandsHelpHowToPairServer": "Bot ile Rust Sunucusu Nasıl Eşleştirilir", "commandsItemDesc": "Get the details of an item.", "commandsLeaderDesc": "Takım liderliğini al yada ver.", "commandsLeaderMemberDesc": "Takım arkadaşının adı.", "commandsMapAllDesc": "Haritayı işaretler ve anıt adları ile göster.", "commandsMapCleanDesc": "<PERSON><PERSON><PERSON> ha<PERSON> g<PERSON>ö<PERSON>.", "commandsMapDesc": "Bağlı olunan sunucunun haritasını göster.", "commandsMapMarkersDesc": "Haritayı işaretler ile göster.", "commandsMapMonumentsDesc": "Haritayı anıt adları ile göster.", "commandsMarketDesc": "Market işlemleri.", "commandsMarketListDesc": "Abonelik listesini göster.", "commandsMarketOrderDesc": "The order type.", "commandsMarketSearchDesc": "Marketlerde bir öge ara.", "commandsMarketSubscribeDesc": "Marketlerde bir ögeye abone olun.", "commandsMarketUnsubscribeDesc": "Marketlerde bir ögeye aboneliğini kaldırın.", "commandsPlayersBattlemetricsIdDesc": "The Battlemetrics ID of the server (default: The connected server).", "commandsPlayersDesc": "Battlemetrics üzerinden oyuncu bilgisini alın.", "commandsPlayersNameDesc": "Search for a player on Battlemetrics based on player name.", "commandsPlayersPlayerIdDesc": "Search for a player on Battlemetrics based on player id.", "commandsPlayersPlayerIdPlayerIdDesc": "The player id of the player.", "commandsPlayersStatusDesc": "Search for players that are online/offline/any.", "commandsRecycleDesc": "Display the output of recycling an item.", "commandsRecycleQuantityDesc": "The quantity of items to recycle.", "commandsRecycleRecyclerTypeDesc": "The recycler type (recycler, shredder, safe-zone-recycler).", "commandsResearchDesc": "Display the cost to research an item.", "commandsResetAlarmsDesc": "Alarm kanalını sıfırla.", "commandsResetDesc": "Discord kanallarını yenile.", "commandsResetInformationDesc": "Bilgi kanalını sıfırla.", "commandsResetServersDesc": "<PERSON><PERSON><PERSON> kanalını sıfırla.", "commandsResetSettingsDesc": "Ayarlar kanalını sıfırla.", "commandsResetStorageMonitorsDesc": "Depolama izleme kanalını sıfırla.", "commandsResetSwitchesDesc": "Anahtarları sıfırlayın ve Grup kanallarını değiştirin.", "commandsResetTrackersDesc": "İzleyici kanalını sıfırla.", "commandsRoleClearDesc": "Clear the role (to allow everyone to see the rustplusplus channels).", "commandsRoleDesc": "Set/Clear a specific role that will be able to see the rustplusplus category content.", "commandsRoleSetDesc": "<PERSON><PERSON>.", "commandsRoleSetRoleDesc": "The role rustplusplus channels will be visible to.", "commandsStackDesc": "Display the stack size of an item.", "commandsStoragemonitorDesc": "Depolama monitörü işlemleri.", "commandsStoragemonitorEditDesc": "Depolama monitörü açıklamasını düzenle.", "commandsStoragemonitorEditIdDesc": "Depolama monitörü kimliği.", "commandsStoragemonitorEditImageDesc": "<PERSON><PERSON><PERSON> monitö<PERSON><PERSON> resmi <PERSON>.", "commandsSwitchDesc": "Akıllı Şalter işlemleri.", "commandsSwitchEditDesc": "Akıllı Şalter açıklamasını düzenle.", "commandsSwitchEditIdDesc": "Akıllı Şalter kimliği.", "commandsSwitchEditImageDesc": "Akıllı Şalter resmi.", "commandsUpkeepDesc": "Display the upkeep cost of an item.", "commandsUptimeBotDesc": "Display uptime of bot.", "commandsUptimeDesc": "Display uptime of the bot and server.", "commandsUptimeServerDesc": "Display uptime of server.", "commandsVoiceBotJoinedVoice": "<PERSON> Bot has joined the Voicechannel", "commandsVoiceBotLeftVoice": "The <PERSON><PERSON> has left the Voicechannel", "commandsVoiceDesc": "Bot Voice Commands", "commandsVoiceFemale": "Kadın", "commandsVoiceFemaleDescription": "Sets the voiceactor gender to Female", "commandsVoiceGenderDesc": "Sets the <PERSON><PERSON>'s voiceactor gender.", "commandsVoiceJoin": "Joining voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceJoinDesc": "Joins the Voicechannel", "commandsVoiceLeave": "Leaving voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceLeaveDesc": "Leaves the Voicechannel", "commandsVoiceMale": "<PERSON><PERSON><PERSON>", "commandsVoiceMaleDescription": "Sets the voiceactor gender to Male", "commandsVoiceNotInVoice": "You are not in a voicechannel", "connect": "Bağlan", "connectCap": "BAĞLAN", "connected": "Bağlanıldı", "connectedCap": "BAĞLI", "connectedToServer": "SUNUCUYA BAĞLI.", "connectingCap": "BAĞLANIYOR", "connectingToServer": "SUNUCUYA BAĞLANIYOR...", "connectionEvents": "Bağlantı Onayları", "connectionRefusedTo": "Bağlantı reddedildi: {id}.", "connectionsCap": "BAĞLANTILAR", "couldNotAddStepTracers": "Could not add step tracers.", "couldNotAppendMapMarkers": "<PERSON><PERSON> işaretleri a<PERSON>, rustplus bilgisi ayarlı değil.", "couldNotAppendMapMonuments": "<PERSON><PERSON> anı<PERSON> a<PERSON>, rustplus bilgisi ayarlı değil.", "couldNotAppendMapTracers": "Could not append map tracers, rustplus info instance is not set.", "couldNotConnectTo": "Bağlantı kurulamadı: {id}.", "couldNotCreateCategory": "<PERSON><PERSON><PERSON>: {name}", "couldNotCreateTextChannel": "<PERSON><PERSON><PERSON> kanalı oluşturulamadı: {name}", "couldNotDeferInteraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "couldNotDeleteCategory": "Could not delete category: {categoryId}", "couldNotDeleteChannel": "Could not delete channel: {channelId}", "couldNotDeleteMessage": "<PERSON><PERSON>: {message}", "couldNotFindAnyPlayers": "Her<PERSON><PERSON> bir oyuncu bulunamadı.", "couldNotFindCategory": "<PERSON><PERSON><PERSON> b<PERSON>namad<PERSON>: {category}", "couldNotFindChannel": "Kanal bulunamadı: {channel}", "couldNotFindCraftDetails": "Could not find craft details for {name}.", "couldNotFindDecayDetails": "Could not find decay details for {name}.", "couldNotFindDespawnDetails": "Could not find despawn details for {name}.", "couldNotFindGuild": "Grup bulunamadı: {guildId}", "couldNotFindLanguage": "Dil bulunamadı: {language}", "couldNotFindMessage": "<PERSON><PERSON> b<PERSON> {message}", "couldNotFindPlayer": "<PERSON><PERSON><PERSON> bulunamadı {name}.", "couldNotFindPlayerId": "Could not find player with id {id}.", "couldNotFindRecycleDetails": "Could not find recycle details for {name}.", "couldNotFindResearchDetails": "Could not find research details for {name}.", "couldNotFindRole": "Rol bulunamadı: {roleId}", "couldNotFindStackDetails": "Could not find stack details for {name}.", "couldNotFindTeammate": "Takım arkadaşı bulunamadı: {name}.", "couldNotFindUpkeepDetails": "Could not find upkeep details for {name}.", "couldNotFindUser": "Kullanıcı bulunamadı: {userId}", "couldNotGetChannelWithId": "<PERSON>u kimlik ile bir kanal bulunamadı: {id}.", "couldNotIdentifyMember": "Takım üyesi tanımlanamadı: {name}.", "couldNotPerformBulkDelete": "Toplu silme işlemi kanalda yapılamadı: {channel}", "couldNotPerformMessageDelete": "<PERSON><PERSON> silme işlemi yapılamadı.", "couldNotPerformMessagesFetch": "Mesajlar kanaldan alınamadı: {channel}", "couldNotRegisterSlashCommands": "Grup için komutlar kaydedilemedi: {guildId}. ", "couldNotSetParent": "Kanal için üstler ayarlanamadı: {channelId}", "craft": "<PERSON><PERSON>", "crate": "<PERSON><PERSON><PERSON>", "createGroupCap": "GRUP OLUŞTUR", "createTrackerCap": "TAKİPÇİ OLUŞTUR", "credentialsAddedSuccessfully": "FCM Bilgileri bu Steam64ID için {steamId} başarıyla kaydedildi!", "credentialsAlreadyRegistered": "FCM Bilgileri bu Steam64ID için {steamId} zaten kayıtlı!", "credentialsCannotStartLiteAlreadyHoster": "FCM takipçisi başlatılamıyor. {steamId} Zaten yayıncı.", "credentialsDoNotExist": "FCM Bilgileri bu Steam64ID için: {steamId} bulunamıyor.", "credentialsHosterNotSetForGuild": "FCM bilgileri bu grup için ayarlı değil {id}, lütfen bir yayıncı ayarlayın.", "credentialsNotRegistered": "FCM Bilgileri bu Steam64ID için {steamId} kayıtlı değil!", "credentialsNotRegisteredForGuild": "Bu grup için FCM bilgileri ayarlı değil: {id}, FCM Takipçisi başlatılamıyor.", "credentialsRemovedSuccessfully": "FCM Bilgileri bu Steam64ID için {steamId} başarıyla silindi!", "credentialsSetHosterSuccessfully": "FCM bilgileri sağlayıcısı bu Steam64ID için {steamId} başarıyla ayarlandı.", "currencySign": "Para birimi işareti", "currentCommandDelay": "Mevcut Ko<PERSON>: {delay} saniye.", "currentItemHp": "The current HP of the item.", "currentPrefixPlaceholder": "Mevcut önek: {prefix}", "customCommand": "<PERSON><PERSON>", "customTimerEditCargoShipEgressLabel": "Kargo <PERSON> süresi (saniye):", "customTimerEditCrateOilRigUnlockLabel": "Son<PERSON>j kutusu a<PERSON><PERSON> (saniye):", "customTimerEditDesc": "<PERSON>zel zamanlayıcıları ayarla", "customTimersCap": "ÖZEL ZAMANLAMA", "dash": "<PERSON><PERSON><PERSON>", "dayOfWipe": "<PERSON><PERSON><PERSON> {day}", "deathCap": "ÖLÜM", "decay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decayTimeForItem": "Decay time for {item} is {time}.", "decayingCap": "ERİME", "deleteUnreachableDevicesCap": "DELETE UNREACHABLE DEVICES", "despawnTime": "Despawn Time", "despawnTimeOfItem": "Despawn time of {item} is {time}.", "deviceIsAlreadyOnOff": "{device} is already {status}.", "deviceIsCurrentlyOnOff": "{device} şu anda {status}.", "deviceWasTurnedOnOff": "{device} ayarlandı {status}.", "disabledCap": "DEVREDIŞI", "discoFloor": "<PERSON><PERSON>", "disconnectCap": "BAĞLANTIYI KES", "disconnected": "Bağlantı Kesildi", "disconnectedCap": "BAĞLANTI KESİLDİ", "disconnectedFromServer": "SUNUCUDAN BAĞLANTI KESİLDİ.", "discordCap": "DISCORD", "discordUsers": "Discord Kullanıcısı", "displayInformationBattlemetricsAllOnlinePlayers": "Should all online players from Battlemetrics be displayed in the information channel?", "displayingMap": "{mapName} haritası gösteriliyor.", "displayingOnlinePlayers": "çevrimiçi oyuncular gösteriliyor.", "distanceDirectionGrid": "{direction}° [{location}] yönünde {distance}m", "doorController": "Kapı kontrolcüsü", "dot": "Nokta", "eastOfGrid": "<PERSON><PERSON><PERSON><PERSON>", "editCap": "DÜZENLE", "editing": "Düzenleniyor", "editingOf": "Editing of {entity}", "egressInTime": "Egress in {time} at {location}.", "eight": "Sekiz", "elevator": "<PERSON><PERSON><PERSON><PERSON>", "empty": "Boş", "enabledCap": "AKTİF", "entityId": "Entity ID", "equalsSign": "<PERSON><PERSON><PERSON><PERSON>", "errorCap": "HATA", "errorExecutingCommand": "Bu komut çalıştırılırken bir hata oluş<PERSON>!", "eventCap": "ETKİNLİK", "eventInfo": "Etkinlik Bilgisi", "exclamationMark": "Ünlem İşareti", "failedToScrapeProfileName": "Profil adı getirilemedi: {link}.", "failedToScrapeProfilePicture": "Profil resmi getirilemedi: {link}.", "fcmCredentials": "FCM Bilgileri", "fcmListenerStartHost": "FCM-Takipçisi 5 saniye içinde bu grup için başlıyor: {guildId}, Steam64Id: {steamId}.", "fcmListenerStartLite": "FCM-Takipçisi Beta 5 saniye içinde bu grup için başlıyor: {guildId}, Steam64Id: {steamId}.", "ferryTerminal": "Feribot Terminali", "fishingVillage": "Balıkçılık Köyü", "five": "Beş", "four": "<PERSON><PERSON><PERSON>", "giantExcavatorPit": "Devasa Kazıcı Çukuru", "greaterThanSign": "Büyüktür işareti", "groupAddSwitchDesc": "{group} g<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> e<PERSON>.", "groupRemoveSwitchDesc": "{group} grubundan şalter sil.", "harbor": "Liman", "hasBeenAliveLongest": "{name} en uzun süre hayatta kalan. ({time}).", "hash": "Hash", "hbhfSensor": "HBHF Sensörü", "heart": "<PERSON><PERSON><PERSON>", "heater": "Isıtıcı", "heavyScientistCalledSetting": "Şondaj kulesinde ağır bilim adamları çağırıldığında bildirim gönder.", "heavyScientistsCalledLarge": "Büyük sondaj kulesinde bilim adamları çağırıldı - {location}.", "heavyScientistsCalledSmall": "Küçük sondaj kulesinde bilim adamları çağırıldı - {location}.", "hideTrademark": "Markayı gizle.", "hoster": "Yayıncı", "hp": "HP", "hpExceedMax": "Hp {hp} is exceeding max of {max}.", "hqmQuarry": "HQ Madeni", "ignoreSetAvatar": "Avatar ayarlamayı yoksay.", "ignoreSetNickname": "Takma ad ayarlamayı yoksay", "ignoreSetUsername": "Kullanıcı adı ayarlamayı yoksay", "inGameBotMessagesMuted": "Oyun içi bot mesajları susturuldu.", "inGameBotMessagesUnmuted": "Oyun içi bot mesajları artık susturulmuyor.", "inGameCap": "OYUN-İÇİ", "inGameEventInfo": "Oyun içi etkinlik <PERSON>i", "inGameTeamNotificationsSetting": "Oyun içi takım arkadaşı bilgisi.", "inGameTime": "<PERSON><PERSON> i<PERSON> zaman: {time}.", "index": "<PERSON><PERSON>", "infoCap": "BİLGİ", "inside": "İçinde", "interactionEditReplyFailed": "Düzenleme isteği başarısız oldu.: {error}", "interactionInvalidChannel": "Geçersiz bir kanaldan <PERSON>.", "interactionReplyFailed": "Cevap isteği başarısız oldu.: {error}", "interactionUpdateFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> isteği başarısız oldu.: {error}", "invalidBattlemetricsId": "Invalid Battlemetrics ID.", "invalidGuildOrChannel": "Geçersiz grup yada kanal.", "invalidHpInterval": "Invalid HP interval {hp}.", "invalidId": "Gerç<PERSON><PERSON>: {id}.", "invalidStructureType": "Invalid Structure type {type}.", "invalidSubcommand": "Geçersiz alt komut.", "invalidTimeDistance": "Geçersiz zaman aralığı: {distance}, eski: {prevTime}, yeni: {newTime}", "isDecaying": "{device} erimeye başladı!", "isNoLongerConnected": "{device} elektrik bağlantısını kaybetti!", "item": "Öge", "itemAvailableInVendingMachine": "{items}, [{location}] adresindeki bir Otomatta kull<PERSON>ıma sunuldu.", "itemAvailableNotifyInGameSetting": "Abonelik listesinden bir öğe bir Otomatta kullanılabilir olduğunda, Oyun İçi bilgilendirilsin mi?", "junkyard": "Hurdalık", "justSubscribedToItem": "Ögeye abone olundu - {name}.", "languageCode": "Dil Kodu: {code}", "languageLangNotSupported": "{language} bu dil desteklenmiyor.", "languageNotSupported": "<PERSON>l desteklenmiyor.", "largeBarn": "Büyük ahır", "largeFishingVillage": "Büyük balıkçılık köyü", "largeOilRig": "B<PERSON>yük sondaj k<PERSON>", "largeWoodBox": "Büyük sandık", "lastTrigger": "<PERSON>", "launchSite": "Fırlatma Üssü", "leaderAlreadyLeader": "{name} zaten takım lideri.", "leaderCommandIsDisabled": "Takım lideri komutu ayarlardan devre dışı bırakılmış.", "leaderCommandOnlyWorks": "<PERSON><PERSON><PERSON><PERSON> liderdi komutu sadece lider {name} ise çalı<PERSON>ır.", "leaderTransferred": "<PERSON><PERSON><PERSON>m lideri devredildi -> {name}.", "leavingMapAt": "{location} konumundan ayrılıyor.", "lessThanSign": "Küçüktür işareti", "lighthouse": "<PERSON><PERSON> feneri", "linkCap": "LINK", "location": "<PERSON><PERSON>", "lockedCrateLargeOilRigUnlocked": "{location} - b<PERSON><PERSON><PERSON>k sondaj kulesinde kilitli sandık açıldı.", "lockedCrateOilRigUnlockedSetting": "Son<PERSON>j kulesinde kilitli sandık açıldığında bildirim gönder.", "lockedCrateSmallOilRigUnlocked": "Küçük sondaj kulesi kutusu açıldı. {location}", "logDiscordCommand": "Discord Command - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logDiscordMessage": "Discord Message - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logInGameCommand": "{type} - Command: {command}, User: {user}.", "logInGameMessage": "Message: {message}, User: {user}", "logSmartSwitchGroupValueChange": "Smart Switch Group - Value: {value}.", "logSmartSwitchValueChange": "Smart Switch - Value: {value}.", "loggedInAs": "GİRİŞ YAPILDI: {name}", "makeSureApplicationsCommandsEnabled": "botu davet ederken applications.commands işaretli olduğundan emin olun.", "map": "<PERSON><PERSON>", "mapSalt": "<PERSON><PERSON>", "mapSeed": "<PERSON><PERSON>", "mapSize": "<PERSON><PERSON>", "mapWipeDetectedNotifySetting": "Wipe tespit edildiğinde {group} grubu bildirim alsın mı?", "markerAdded": "[{location}] konumunda {name} işareti eklendi.", "markerDoesNotExist": "İşaretçi {name} bulunmuyor.", "markerLocation": "[{location}] konumundaki {name} i<PERSON><PERSON><PERSON><PERSON>, {player}'dan {direction}° yönünde {distance}m.", "markerRemoved": "[{location}] konumundaki {name} işaretçisi kaldırıldı.", "message": "<PERSON><PERSON>", "messageCap": "MESAJ", "messageDeletedIn30": "Bu mesaj 30 saniye içinde silinecektir.", "messageEditFailed": "<PERSON><PERSON> b<PERSON>ı<PERSON>ız: {error}", "messageReplyFailed": "<PERSON><PERSON> ceva<PERSON>ı başarıs<PERSON>z: {error}", "messageSendFailed": "<PERSON><PERSON> başarı<PERSON>ız: {error}", "messageWasSent": "<PERSON><PERSON>.", "militaryTunnel": "<PERSON><PERSON>", "miningOutpost": "Madencilik", "missileSilo": "<PERSON><PERSON><PERSON>", "missingArguments": "<PERSON><PERSON><PERSON>.", "missingPermission": "<PERSON><PERSON><PERSON> ya<PERSON>ak için izniniz yok.", "missingTimerMessage": "<PERSON><PERSON>p zamanlayıcı mesajı.", "modalValueChange": "Modal Interaction - VerifyId: {id}, Value: {value}.", "more": "daha", "morePlayers": "{players} ...{number} daha.", "mutedCap": "SUSTURULDU", "name": "Ad", "nameChangeHistory": "Ad Değiştirme Geçmiş", "new": "<PERSON><PERSON>", "newVendingMachine": "{location} konumunda yeni market açıldı.", "newsCap": "HABERLER", "noActiveTimers": "Aktif bir zamanlayıcı yok.", "noCommandDelay": "<PERSON><PERSON>t gecikmesi yok.", "noCommunicationSmartSwitch": "Akıllı şalter ile iletişim kurulamadı: {name}", "noData": "Veri yok.", "noDataOnLargeOilRig": "Büyük sondaj kulesi i<PERSON>in veri yok.", "noDataOnSmallOilRig": "Küçük sondaj kulesi için veri yok.", "noDelayCap": "GECİKME YOK", "noItemFound": "Öge herhangi bir markette bulunamadı...", "noItemWithIdFound": "{id} kimlik numaralı bir öge bulunamadı.", "noItemWithNameFound": "{name} adlı bir öge bulunamadı.", "noNameIdGiven": "Herhangi bir ad yada kimlik numarası verilmedi.", "noOneIsAfk": "AFK birisi yok.", "noOneIsOffline": "Çevrimdışı birisi yok.", "noOneIsOnline": "Çevrimiçi birisi yok.", "noRegisteredConnectionEvents": "Henüz kayıtlı bağlantı olayı yok.", "noRegisteredConnectionEventsUser": "{user} i<PERSON><PERSON> ka<PERSON> bağlantı olayı yok.", "noRegisteredDeathEvents": "Henüz kayıtlı ölüm olayı yok.", "noRegisteredDeathEventsUser": "{user} i<PERSON><PERSON> ka<PERSON>lüm olayı yok.", "noRegisteredEvents": "No registered events yet.", "noRegisteredMarkers": "Kayıtlı bir işaretçi yok.", "noSavedNotes": "<PERSON><PERSON><PERSON><PERSON> bir not yok.", "noToolCupboardWereFound": "Alet dolabı monitörü yok.", "none": "Boş", "northEast": "<PERSON><PERSON><PERSON>", "northOfGrid": "<PERSON><PERSON><PERSON><PERSON>", "northWest": "<PERSON><PERSON><PERSON>", "notAValidOrderType": "{order} is not a valid order type.", "notActive": "<PERSON><PERSON><PERSON>.", "notConnectedToRustServer": "Herhangi bir rust sunucusuna bağlı değil.", "notExistInSubscription": "{name} öğesi abonelik listesinde yok.", "notFoundCap": "BULUNAMADI", "notPartOfRole": "{role} r<PERSON><PERSON><PERSON><PERSON><PERSON> bir <PERSON><PERSON><PERSON>, bu nedenle bot komutlarını çalıştıramazsınız.", "notShowingCap": "GÖSTERİLMİYOR", "noteCap": "NOT", "noteIdDoesNotExist": "Not Kimliği: {id} bulunamıyor.", "noteIdInvalid": "Not Kimliği geçersiz.", "noteIdWasRemoved": "Not Kimliği: {id} silindi.", "noteSaved": "Not kayded<PERSON><PERSON>.", "offCap": "KAPALI", "offline": "Çevrim dışı", "offlineTime": "Çevrim dışı zamanı", "oilRig": "<PERSON><PERSON><PERSON>", "old": "<PERSON><PERSON>", "onCap": "AÇIK", "one": "Bir", "online": "<PERSON><PERSON><PERSON> i<PERSON>i", "onlineTime": "Çevrim içi süresi", "onlyOneInTeam": "Takımda sadece sen varsın.", "outpost": "Karakol", "outside": "Outside", "oxumsGasStation": "Oxum Gaz İstasyonu", "pairing": "Eşleştirme", "patrolHelicopter": "Helikopter", "patrolHelicopterDestroyedSetting": "Helikopter patladığında bildirim gönder.", "patrolHelicopterDetectedSetting": "Helikopter görüldüğünde bildirim gönder.", "patrolHelicopterEntersMap": "Helikopter {location} konumundan giriş yaptı.", "patrolHelicopterLeftMap": "Helikopter {location} konumundan çıkış yaptı.", "patrolHelicopterLeftSetting": "Helikopter haritadan ayrıldığında bildirim gönder.", "patrolHelicopterLocatedAt": "Helikopter {location} konumunda.", "patrolHelicopterNotCurrentlyOnMap": "Helikopter haritada değil.", "patrolHelicopterTakenDown": "Helikopter {location} konumunda düşürüldü.", "percentSign": "Yüzde işareti", "pipe": "<PERSON><PERSON>", "playerHasBeenAliveFor": "{name}, {time} z<PERSON><PERSON><PERSON><PERSON> hay<PERSON>a.", "playerId": "<PERSON><PERSON><PERSON>", "playerJoinedTheTeam": "{name} takı<PERSON> katıldı.", "playerJustConnected": "{name} <PERSON><PERSON><PERSON> b<PERSON>.", "playerJustConnectedTo": "{name} <PERSON><PERSON><PERSON><PERSON> b<PERSON> - {server}.", "playerJustConnectedTracker": "{name} az <PERSON>nce {tracker} izleyicisinden bağlandı.", "playerJustDied": "{name} öldü, {location}.", "playerJustDisconnected": "{name} o<PERSON><PERSON> ç<PERSON>ı.", "playerJustDisconnectedFrom": "{name} o<PERSON><PERSON> ç<PERSON>ı - {server}.", "playerJustDisconnectedTracker": "{name} i<PERSON><PERSON><PERSON><PERSON>'nin {tracker} ile o<PERSON> bağlantısı az önce kesildi.", "playerJustReturned": "{name} <PERSON><PERSON><PERSON> dö<PERSON>ü - ({time}).", "playerJustWentAfk": "{name} AFK oldu.", "playerLeftTheTeam": "{name} ta<PERSON><PERSON><PERSON><PERSON>.", "playerNotPairedWithServer": "{name} <PERSON><PERSON><PERSON><PERSON> eşleştirilmediğinden Lider komutu çalışmıyor.", "players": "Oyuncular", "playersSearch": "Oyuncu Ara", "plusSign": "Artı işareti", "populationPlayers": "Oyuncu: ({current}/{max})", "populationQueue": "{number} o<PERSON><PERSON> s<PERSON>.", "powerPlant": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "proxLocation": "{name}, {caller} {direction}° [{location}] yönünde {distance}m", "quantity": "<PERSON><PERSON><PERSON>", "questionMark": "<PERSON><PERSON>", "ranch": "<PERSON><PERSON><PERSON>", "ratelimited": "HIZ LİMİTİ", "reconnectingCap": "TEKRAR BAĞLANILIYOR", "reconnectingToServer": "SUNUCUYA TEKRAR BAĞLANILIYOR...", "recycle": "<PERSON><PERSON>", "recycleCap": "DÖNÜŞTÜRÜCÜ", "recycler": "<PERSON><PERSON>", "remain": "kaldı", "removePlayerCap": "OYUNCU SİL", "removeSwitchCap": "ŞALTER SİL", "removedSubscribeItem": "{name} <PERSON><PERSON><PERSON> a<PERSON>ten kaldırıldı", "research": "Araştır", "researchTable": "Araştırma Masası", "resetSuccess": "Discord ba<PERSON><PERSON><PERSON><PERSON> ye<PERSON>len<PERSON>.", "responseContainError": "<PERSON><PERSON>t bu hatayı taşıyor: {error}.", "responseIsEmpty": "<PERSON><PERSON><PERSON> bo<PERSON>.", "responseIsUndefined": "<PERSON>vap <PERSON>.", "responseTimeout": "Cevap beklenirken zamanaşımı.", "resultRecycling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleCleared": "rustplusplus rolü te<PERSON>.", "roleSet": "rustplusplus role has been set to {name}.", "rustMonument": "Rust Monument", "rustplusOperational": "RUSTPLUS OPERASYONEL.", "safe-zone-recycler": "Safe Zone Recycler", "samsite": "SAM Site", "satelliteDish": "Uyd<PERSON>", "scrap": "<PERSON><PERSON>", "searchResult": "<PERSON><PERSON>: **{name}**", "second": "{second} saniye", "secondCommandDelay": "{second} saniye gecik<PERSON>.", "seconds": "{seconds} saniye", "secondsCommandDelay": "{seconds} saniye gecikme.", "selectInGamePrefixSetting": "Oyun içi komut önekini seçin:", "selectLanguageExtendSetting": "**/reset discord** komutunu çalıştırmayı unutmayın.", "selectLanguageSetting": "Botun kullanacağı dili seçin:", "selectMenuValueChange": "Select Menu Interaction - VerifyId: {id}, Value: {value}.", "selectTrademarkSetting": "<PERSON>yun içi mesaj mark<PERSON>ı<PERSON> seçin.", "sell": "sat", "semicolon": "Noktalı virgül", "sentTextToSpeech": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "server": "<PERSON><PERSON><PERSON>", "serverId": "<PERSON><PERSON><PERSON>", "serverInfo": "<PERSON><PERSON><PERSON>", "serverInvalid": "Bağlanılmaya çalışılan sunucu geçersiz olabilir tekrardan eşleştirme yapın.", "serverJustOffline": "<PERSON><PERSON><PERSON> kapan<PERSON>.", "serverJustOnline": "<PERSON><PERSON><PERSON> a<PERSON>ıldı.", "serverStatus": "<PERSON><PERSON><PERSON>", "serviceUnavailable": "<PERSON><PERSON>: {error}", "setBotLanguage": "<PERSON><PERSON> a<PERSON>: {language}.", "seven": "<PERSON><PERSON>", "sewerBranch": "Ka<PERSON><PERSON><PERSON><PERSON>", "shouldBotBeMutedSetting": "Oyun içi komutlar susturulsun mu?", "shouldCommandsEnabledSetting": "Oyun içi komutlar aktif olsun mu?", "shouldLeaderCommandEnabledSetting": "Takım lideri komutu aktif olsun mu?", "shouldLeaderCommandOnlyForPairedSetting": "Lider komutu yalnızca sunucuyla eşleştirilmiş kişiler için mi çalışmalı?", "shouldSmartAlarmNotifyNotConnectedSetting": "Akıllı alarmlar farklı sunucuya ait olsada haber versin mi?", "shouldSmartAlarmsNotifyInGameSetting": "Akıllı Alamlar oyun içi bildirim göndersin mi?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Should Smart Switches and Smart Switch Groups notify In-Game when they are changed from discord?", "showingBlacklist": "Kara liste gösteriliyor.", "showingSubscriptionList": "Abonelik listesi gösteriliyor.", "shredder": "Parçalayıcı", "sirenLight": "<PERSON><PERSON>", "six": "6", "slash": "<PERSON><PERSON><PERSON>", "slashCommandInteraction": "Slash Command Interaction - Guild: {guild}, Channel: {channel}, User: {user}, Command: {command}, VerifyId: {id}.", "slashCommandValueChange": "Slash Command Interaction - VerifyId: {id}, Value: {value}.", "slashCommandsSuccessRegister": "Uygulama komutları bu sunucu için başarıyla kaydedildi: {guildId}.", "slots": "Slot", "smallOilRig": "Küçük Sondaj <PERSON>i", "smartAlarm": "Akıllı Alarm", "smartAlarmEditSuccess": "Akıllı Alarm başarıyla düzenlendi - {name}.", "smartAlarmNotifyExtendSetting": "- Alarmlar oyun içinde belirlenen başlığı ve mesajı kullanır.\n- Alarmlar discord kanalında gözükmeyebilir.", "smartDeviceNotFound": "{device} bulunamadı! Yokedilmiş olabilir yada {user} alet dolabı <PERSON><PERSON> ka<PERSON>.", "smartSwitch": "Akıllı Şalter", "smartSwitchAutoDay": "Akıllı şalter sadece gündüzleri aktif olur.", "smartSwitchAutoNight": "Akıllı şalter sadece geceleri aktif olur.", "smartSwitchAutoOff": "Smart Switch will automatically go inactive during update cycle.", "smartSwitchAutoOffAnyOnline": "Smart Switch will automatically go inactive if any teammate is online.", "smartSwitchAutoOffProximity": "Smart Switch will automatically go inactive if teammate is in proximity.", "smartSwitchAutoOn": "Smart Switch will automatically go active during update cycle.", "smartSwitchAutoOnAnyOnline": "Smart Switch will automatically go active if any teammate is online.", "smartSwitchAutoOnProximity": "Smart Switch will automatically go active if teammate is in proximity.", "smartSwitchEditProximityLabel": "Proximity Setting (meters):", "smartSwitchEditSuccess": "Akıllı şalter başarı<PERSON> düzenlendi - {name}.", "smartSwitchNormal": "Akıllı şalter normal olarak çalışıyor.", "smilyFace": "<PERSON><PERSON><PERSON>", "somethingWrongWithConnection": "Bağlantı ile ilgili bir şeyler yanlış gitti.", "southEast": "<PERSON><PERSON><PERSON>", "southOfGrid": "<PERSON><PERSON><PERSON><PERSON>", "southWest": "<PERSON><PERSON><PERSON>", "sprinkler": "Yağmurluk", "stackSize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stackSizeOfItem": "Stack size of {item} is {quantity}x.", "status": "Durum", "statusNotConnectedToServer": "**DURUM** `SUNUCUYA BAĞLI DEĞİL!`", "statusNotElectronicallyConnected": "**DURUM** `ELEKTRİK İLE BAĞLI DEĞİL`", "statusNotFound": "**DURUM**: BULUNAMADI", "steamId": "Steam Kimliği", "stoneQuarry": "<PERSON><PERSON>", "storageMonitor": "<PERSON><PERSON><PERSON>", "storageMonitorEditSuccess": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> d<PERSON>len<PERSON> - {name}.", "streamerMode": "Yayıncı Modu", "subscribeToChangesBattlemetrics": "Battlemetrics'te<PERSON> far<PERSON> değişikliklere abone olun.", "subscriptionList": "Abonelik listesi", "subscriptionListEmpty": "Öge abonelik listesi boş.", "sulfurQuarry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switches": "<PERSON><PERSON><PERSON><PERSON>", "teamMember": "Takım <PERSON>i", "teamMemberInfo": "Takım Üyesi Bilgisi", "theDome": "<PERSON><PERSON><PERSON>", "theIdOfTheItem": "Öğ<PERSON>n k<PERSON>ği.", "theNameOfTheItem": "Öğenin adı.", "theNameOfThePlayer": "Oyuncunun adı.", "three": "Üç", "tilde": "Yaklaşık", "time": "Saat", "timeBeforeCargoEntersEgress": "{time} before Cargo Ship at {location} enters egress stage.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} son<PERSON> büyük sondaj kulesi kutusu ({location}) konumunda açılacak.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} sonra küçük sondaj kulesi kutusu ({location}) konumunda açılacak.", "timeCap": "ZAMAN", "timeFormatInvalid": "Zaman formatı geçersiz.", "timeLeftTimer": "{id}: <PERSON><PERSON><PERSON>: {time}, <PERSON>j: {message}", "timeSinceAlarmWasTriggered": "The alarm {alarm} was triggered {time} ago.", "timeSinceCargoLeft": "{time} önce kargo gemisi haritadan ayrıldı.", "timeSinceChinook47OnMap": "{time} <PERSON><PERSON> Chinook 47 haritada görüldü.", "timeSinceHeavyScientistsOnLarge": "{time} önce ağır bilim adam<PERSON>ı büyük sondaj k<PERSON>e çağırıldı.", "timeSinceHeavyScientistsOnSmall": "{time} önce ağır bilim adamları küçük sondaj kules<PERSON>e çağırıldı.", "timeSinceLast": "{time} önce.", "timeSinceLastEvent": "{time} <PERSON>nce son et<PERSON><PERSON>.", "timeSinceLastSinceDestroyedLong": "{time1} since the last Patrol Helicopter was on the map, {time2} since it last got downed{location}.", "timeSinceLastSinceDestroyedShort": "{time1} since last.\n{time2} since destroyed{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} önce helikopter haritaydı.", "timeSinceTravelingVendorWasOnMap": "{time} since the Traveling Vendor was on the map.", "timeSinceWipe": "{time} <PERSON><PERSON> wipe atıldı.", "timeTill": "<PERSON><PERSON><PERSON> kaldı {event}", "timeTillDaylight": "{time} <PERSON><PERSON> gündüz <PERSON>.", "timeTillNightfall": "{time} sonra gece o<PERSON>.", "timeTillStructureDecay": "{time} before {type} wall decay.", "timeUntilUnlocksAt": "{time} sonra açılacak {location}.", "timer": "Zamanlayıcı: {message}.", "timerIdDoesNotExist": "Zamanlayıcı Kimliği: {id} bulunamadı.", "timerIdInvalid": "Zamanlayıcı Kimliği geçersiz.", "timerRemoved": "Zamanlayıcı Kimliği: {id} silindi.", "timerSet": "Zamanlayıcı ayarlandı - {time}.", "tokensDidNotReplenish": "Tokenler zamanında yenilenmedi.", "toolCupboard": "<PERSON>et <PERSON>", "total": "Toplam", "tracker": "İzleyici", "trackerAddPlayerDesc": "<PERSON><PERSON><PERSON> - {tracker}", "trackerRemovePlayerDesc": "<PERSON><PERSON><PERSON> - {tracker}", "trademarkShownBeforeMessage": "{trademark} mesajlardan önce gösterilecek.", "trainYard": "<PERSON><PERSON>", "travelingVendor": "Traveling Vendor", "travelingVendorDetectedSetting": "When the Traveling Vendor is detected, send a notification.", "travelingVendorHaltedAt": "The Traveling Vendor stopped at {location}.", "travelingVendorHaltedSetting": "When the Traveling Vendor stops moving, send a notification.", "travelingVendorLeftSetting": "When the Traveling Vendor left the map, send a notification.", "travelingVendorLocatedAt": "The Traveling Vendor is located at {location}.", "travelingVendorLeftMap": "The Traveling Vendor just left the map at {location}.", "travelingVendorNotCurrentlyOnMap": "The Traveling Vendor is not currently on the map.", "travelingVendorResumedAt": "The Traveling Vendor resumed moving at {location}.", "travelingVendorSpawnedAt": "The Traveling Vendor spawned at {location}.", "turnOffCap": "AKTİF", "turnOnCap": "DEVREDIŞI", "turningGroupOnOff": "{group} ayarlandı {status}.", "two": "<PERSON><PERSON>", "type": "Tip", "unavailable": "<PERSON><PERSON><PERSON>", "underscore": "Vurgu", "underwater": "Sualtı", "underwaterLab": "Su altı laboratuvarı", "unhandledRejection": "İşlenmemiş Hata: {error}", "unknown": "Bilinmeyen", "unknownInteraction": "Bilinmeyen etkileşim...", "unmutedCap": "SUSTURULMAMIŞ", "updateCap": "GÜNCELLE", "upkeep": "Bakım Masrafı", "upkeepForItem": "Upkeep for {item} is {cost}.", "userAddedToBlacklist": "{user} kara listeye eklendi.", "userAlreadyInBlacklist": "{user} zaten kara listede.", "userButtonInteraction": "Button Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Button Interaction - VerifyId: {id} SUCCESS", "userJustConnected": "{name} <PERSON><PERSON><PERSON> b<PERSON>.", "userModalInteraction": "Modal Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Modal Interaction - VerifyId: {id} SUCCESS", "userNotInBlacklist": "{user} kara <PERSON><PERSON>.", "userNotRegistered": "{user} ka<PERSON><PERSON><PERSON><PERSON> de<PERSON>.", "userPartOfBlacklist": "VerifyId: {id}, {user} is part of the blacklist.", "userPartOfBlacklistDiscord": "Blacklisted User! Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "userPartOfBlacklistInGame": "Blacklisted User! User: {user}, Message: {message}.", "userRemovedFromBlacklist": "{user} was removed from blacklist.", "userSaid": "{user} dedi, {text}", "userSelectMenuInteraction": "Select Menu Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Select Menu Interaction - VerifyId: {id} SUCCESS", "userTurnedOnOffSmartSwitchFromDiscord": "{user} turned Smart Switch {name} {status} from discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} turned Smart Switch Group {name} {status} from discord.", "value": "<PERSON><PERSON><PERSON>", "vendingMachine": "Market", "vendingMachineDetectedSetting": "Yeni market açıldığında bildirim gönder.", "voiceCap": "SES", "warningCap": "UYARI", "waterTreatmentPlant": "Su Arıtma Tesisi", "websiteCap": "WEBSITE", "websocketClosedBeforeConnection": "Websocket bağlantı sağlanamadan kapatıldı.", "westOfGrid": "Hari<PERSON>ın <PERSON>", "wipe": "Wipe", "wipeDetected": "Wipe tespit edildi!", "yield": "<PERSON><PERSON><PERSON>", "youAreAlreadyLeader": "Zaten lidersin.", "youAreNotPairedWithServer": "Sunucuyla eşleşmediğiniz için lider komutu çalışmıyor."}