{"24HoursInGameTimePassed": "24 Úspěš<PERSON><PERSON><PERSON> hern<PERSON>ch hodin uplynulo.", "abandonedCabins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "abandonedMilitaryBase": "Opuštěná vojenská základna", "abandonedSupermarket": "Opuštěný supermarket", "addPlayerCap": "PŘIDAT HRÁČE", "addSwitchCap": "PŘIDAT SPÍNAČ", "afkCap": "AFK", "airfield": "Letiště", "alarmHaveNotBeenTriggeredYet": "Budík {alarm} zatím nebyl aktivován.", "alias": "<PERSON><PERSON>", "aliasAlreadyExist": "<PERSON><PERSON> už existuje.", "aliasIndexCouldNotBeFound": "Index aliasu nebyl nalezen.", "aliasWasAdded": "<PERSON><PERSON>.", "aliasWasRemoved": "<PERSON><PERSON>.", "aliases": "<PERSON><PERSON><PERSON>", "all": "vše", "allTeammatesAreDead": "Všichni tví spoluhráči jsou mrtví.", "alreadySubscribedToItem": "<PERSON><PERSON> přihlášeno k odběru {name}.", "ampersand": "Ampersand", "andMorePlayers": "... a {number} da<PERSON><PERSON><PERSON><PERSON> hr<PERSON>.", "any": "Jakýkoliv", "apostrophe": "<PERSON><PERSON><PERSON><PERSON>", "arcticResearchBase": "Arktická výzkumná základna", "asterisk": "Hvězdička", "asteriskCctvDesc": "* <PERSON><PERSON><PERSON><PERSON><PERSON>, že potřebujete číselný kód, který je jiný pro každou mapu", "atLocation": "Na {location}.", "atSign": "Na znamení", "autoDayCap": "AUTO-DAY", "autoNightCap": "AUTO-NIGHT", "autoOffAnyOnlineCap": "AUTO-OFF-ANY-ONLINE", "autoOffCap": "AUTO-VYPNUTÍ", "autoOffProximityCap": "AUTO-OFF-PROXIMITY", "autoOnAnyOnlineCap": "AUTO-ON-ANY-ONLINE", "autoOnCap": "AUTO-ZAPNUTÍ", "autoOnProximityCap": "AUTO-ON-PROXIMITY", "autoSettingCap": "AUTOMATICKÉ NASTAVENÍ: ", "automaticallyTurnBackOnOff": " Automaticky změněno na {status} za {time}.", "automaticallyTurningBackOnOff": "Automaticky měním {device} na {status}.", "autoturret": "Automatická Palebná Věž", "badGateway": "Bad Gateway: {error}", "banditCamp": "Tábor banditů", "baseIsUnderAttack": "Üssün saldırı altında!", "battlemetricsApiRequestFailed": "Battlemetrics API požadavek selhal: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics Server {server} se nepodařilo aktualizovat.", "battlemetricsGlobalLoginCap": "GLOBÁLNÍ LOGIN", "battlemetricsGlobalLogoutCap": "GLOBÁLNÍ LOGOUT", "battlemetricsGlobalNameChangesCap": "GLOBÁLNÍ ZMĚNY JMEN", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "Battlemetrics instanci chybí id a jméno.", "battlemetricsInstanceCouldNotBeFound": "Battlemetrics Instance pro {id} nebyla na<PERSON>.", "battlemetricsOnlinePlayers": "Battlemetrics Online Hráči", "battlemetricsPlayersLogin": "Battlemetrics Přih<PERSON><PERSON>š<PERSON><PERSON>", "battlemetricsPlayersLogout": "Battlemetrics <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlemetricsPlayersNameChanged": "Battlemetrics Změna Jmen u Hráčů", "battlemetricsServerNameChanged": "Battlemetrics <PERSON><PERSON><PERSON><PERSON>", "battlemetricsServerNameChangesCap": "ZMĚNY NÁZVU SERVERU", "battlemetricsTrackerNameChangesCap": "ZMĚNY NÁZVU TRACKERU", "battlemetricsTrackerPlayerNameChanged": "Battlemetrics <PERSON><PERSON><PERSON><PERSON>", "blacklist": "Blacklist", "boomBox": "Boom Box", "bot": "bot", "broadcaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buttonValueChange": "Tlačítko Interakce - VerifyId: {id}, Hodnota: {value}.", "buy": "kou<PERSON>", "calculated": "Vypočítáno", "cargoAt": "Na {location}.", "cargoLeavingMapAt": "Cargo Ship opouští mapu na {location}.", "cargoLocatedAt": "Cargo Ship se nachází na {location}.", "cargoNotCurrentlyOnMap": "Cargo Ship není na mapě.", "cargoShipDetectedSetting": "<PERSON><PERSON>ž Cargo <PERSON> je de<PERSON>, p<PERSON><PERSON><PERSON> notif<PERSON>.", "cargoShipDockingAtHarbor": "Cargo ship just docked at the Harbor at {location}", "cargoShipDockingAtHarborSetting": "When Cargo Ship is docked at a harbor, send a notification.", "cargoShipEgressSetting": "Když Cargo Ship začne opouštět mapu, po<PERSON><PERSON> notifika<PERSON>.", "cargoShipEntersEgressStage": "Cargo Ship by m<PERSON>la opustit mapu na {location}.", "cargoShipEntersMap": "Cargo Ship vjede na mapu na {location}.", "cargoShipLeftHarbor": "Cargo ship just left the Harbor at {location}", "cargoShipLeftMap": "Cargo Ship opustila mapu na {location}.", "cargoShipLeftSetting": "Když Cargo Ship opustí mapu, po<PERSON><PERSON> notifikaci.", "cargoShipLocated": "Cargo Ship je na {location}.", "cargoship": "Cargoship", "ceilingLight": "Stropní světlo", "channelNameActivity": "aktivita", "channelNameAlarms": "alarmy", "channelNameCommands": "commands", "channelNameEvents": "events", "channelNameInformation": "informace", "channelNameServers": "servers", "channelNameSettings": "nastavení", "channelNameStorageMonitors": "skladovací Monitory", "channelNameSwitchGroups": "switchGroups", "channelNameSwitches": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "channelNameTeamchat": "t<PERSON><PERSON><PERSON><PERSON> chat", "channelNameTrackers": "trackery", "chinook47": "Chinook 47", "chinook47DetectedSetting": "Když na mapu vstoupí Chinook 47, p<PERSON><PERSON><PERSON><PERSON> notifikaci.", "chinook47EntersMap": "Chinook 47 vstupuje na mapu z {location} aby vyhodil uzamčenou bednu.", "chinook47LeftMap": "Chinook 47 opustil mapu na {location}.", "chinook47Located": "Chinook 47 je na {location}.", "chinook47NotOnMap": "Chinook 47 momentálně není na mapě.", "christmasLights": "Vánoční světýlka", "circumflex": "Circumflex", "clanTag": "Clan tag", "codes": "<PERSON><PERSON><PERSON>", "colon": "Dvojtečka", "comma": "<PERSON><PERSON><PERSON>", "commandCap": "PŘÍKAZ", "commandDelaySetting": "<PERSON><PERSON><PERSON> by do<PERSON><PERSON><PERSON> ke zpoždění příkazu? Jak d<PERSON>?", "commandNotPossibleDiscord": "Příkaz není možný skrze discord.", "commandSyntaxAdd": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "<PERSON><PERSON><PERSON>", "commandSyntaxArmored": "armored", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "připojení", "commandSyntaxConnections": "connections", "commandSyntaxCraft": "craft", "commandSyntaxDeath": "smrt", "commandSyntaxDeaths": "smrti", "commandSyntaxDecay": "decay", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "eventy", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "jazyk", "commandSyntaxLarge": "<PERSON><PERSON><PERSON>", "commandSyntaxLeader": "vů<PERSON>ce", "commandSyntaxList": "list", "commandSyntaxMarker": "marker", "commandSyntaxMarkers": "markery", "commandSyntaxMarket": "market", "commandSyntaxMetal": "metal", "commandSyntaxMute": "ztišit", "commandSyntaxNote": "poznámka", "commandSyntaxNotes": "poznámky", "commandSyntaxOff": "vypnout", "commandSyntaxOffline": "offline", "commandSyntaxOn": "zapnout", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxPlayers": "<PERSON>r<PERSON><PERSON><PERSON>", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "recycle", "commandSyntaxRemove": "odstranit", "commandSyntaxResearch": "research", "commandSyntaxSearch": "vyhledat", "commandSyntaxSend": "odes<PERSON>", "commandSyntaxSmall": "<PERSON><PERSON><PERSON>", "commandSyntaxStack": "stack", "commandSyntaxStatus": "status", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "stone", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "t<PERSON>m", "commandSyntaxTime": "čas", "commandSyntaxTimer": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxTimers": "časovače", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendor", "commandSyntaxTwig": "twig", "commandSyntaxUnmute": "unmute", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "upkeep", "commandSyntaxUptime": "uptime", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "wood", "commandsAlarmDesc": "Operace s chytr<PERSON><PERSON> alarmy.", "commandsAlarmEditDesc": "Upravit popis chyt<PERSON>u.", "commandsAlarmEditIdDesc": "ID chytrého alarmu.", "commandsAlarmEditImageDesc": "Nastavte o<PERSON>r<PERSON><PERSON><PERSON>, kter<PERSON> nejlépe reprezentuje chytrý alarm.", "commandsAliasAddAliasDesc": "Alias k použití.", "commandsAliasAddDesc": "<PERSON><PERSON><PERSON><PERSON> alias.", "commandsAliasAddValueDesc": "Příkaz/posloupnost znaků.", "commandsAliasDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> alias pro příkaz/posloupnost znaků.", "commandsAliasRemoveDesc": "<PERSON>ds<PERSON><PERSON><PERSON> alias.", "commandsAliasRemoveIndexDesc": "Index aliasu k odstranění.", "commandsAliasShowDesc": "Zobrazit všechny registrované aliasy.", "commandsBlacklistAddDesc": "Přidat uživatele na blacklist.", "commandsBlacklistDesc": "Zakázat uživateli používat bota.", "commandsBlacklistDiscordUserDesc": "Uživatel Discordu.", "commandsBlacklistRemoveDesc": "Odebrat uživatele z blacklistu.", "commandsBlacklistShowDesc": "Zobrazit uživatele na blacklistu.", "commandsBlacklistSteamidDesc": "<PERSON><PERSON>.", "commandsCctvDesc": "Zobrazit CCTV kódy pro monument", "commandsCraftDesc": "Zobrazit náklady na výrobu položky.", "commandsCraftQuantityDesc": "Množství předmětů k výrobě.", "commandsCredentialsAddDesc": "Přidat FCM údaje.", "commandsCredentialsDesc": "Přidat/vymazat FCM údaje pro uživatelský účet.", "commandsCredentialsRemoveDesc": "Odstranit přístupové údaje FCM.", "commandsCredentialsRemoveSteamIdDesc": "SteamId of the FCM Credentials to remove.", "commandsCredentialsSetHosterDesc": "Set the hoster of FCM Credentials.", "commandsCredentialsSetHosterSteamIdDesc": "SteamID hostitele FCM údajů.", "commandsCredentialsShowDesc": "Zobrazit aktuálně registrované FCM údaje.", "commandsDecayDesc": "Zobrazit čas rozkladu položky.", "commandsDespawnDesc": "Zobrazit čas despawnu položky.", "commandsHelpCommandList": "Seznam příkazů", "commandsHelpDesc": "Zobrazit nápovědu.", "commandsHelpHowToCredentials": "Jak registrovat FCM údaje", "commandsHelpHowToPairServer": "Jak spárovat bota s Rust serverem", "commandsItemDesc": "Získat podrobnosti o položce.", "commandsLeaderDesc": "<PERSON><PERSON>te vedení z/na člena týmu.", "commandsLeaderMemberDesc": "<PERSON><PERSON><PERSON><PERSON> tý<PERSON>.", "commandsMapAllDesc": "Získejte mapu včetně názvů monumentů a markerů.", "commandsMapCleanDesc": "Získejte čistou mapu.", "commandsMapDesc": "Získejte obrázek mapy aktuálně připojeného serveru.", "commandsMapMarkersDesc": "Získejte mapu včetně markerů.", "commandsMapMonumentsDesc": "Získejte mapu včetně názvů monumentů.", "commandsMarketDesc": "Operace pro In-Game Vending Machines.", "commandsMarketListDesc": "Zobrazit seznam odběrů.", "commandsMarketOrderDesc": "Typ o<PERSON>dn<PERSON>ky.", "commandsMarketSearchDesc": "Hledat položku ve Vendich Machines.", "commandsMarketSubscribeDesc": "Přihlaste se k odběru položky ve Vending Machines.", "commandsMarketUnsubscribeDesc": "Odhlaste se z odběru položky ve Vending Machines.", "commandsPlayersBattlemetricsIdDesc": "Battlemetrics ID serveru (výchozí: Připojený server).", "commandsPlayersDesc": "Získejte informace o hráči/hráčích na základě Battlemetrics.", "commandsPlayersNameDesc": "Hledejte hráče na Battlemetrics podle jména hráče.", "commandsPlayersPlayerIdDesc": "Hledejte hráče na Battlemetrics podle id hráče.", "commandsPlayersPlayerIdPlayerIdDesc": "Player id hrá<PERSON>e.", "commandsPlayersStatusDesc": "Vyhledejte hráče, kteří jsou online/offline/libovolní.", "commandsRecycleDesc": "Zobrazit výsledek recyklace položky.", "commandsRecycleQuantityDesc": "Množství položek k recyklaci.", "commandsRecycleRecyclerTypeDesc": "The recycler type (recycler, shredder, safe-zone-recycler).", "commandsResearchDesc": "Zobrazit náklady na výzkum položky.", "commandsResetAlarmsDesc": "Resetovat kanál alarms.", "commandsResetDesc": "<PERSON><PERSON><PERSON><PERSON> Discord kaná<PERSON>.", "commandsResetInformationDesc": "Resetovat kanál information.", "commandsResetServersDesc": "Resetovat kanál servers.", "commandsResetSettingsDesc": "Resetovat kanál settings.", "commandsResetStorageMonitorsDesc": "Resetovat kanál storage monitors.", "commandsResetSwitchesDesc": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON><PERSON> switches a switchGroups.", "commandsResetTrackersDesc": "<PERSON><PERSON>ovat kanál trackers.", "commandsRoleClearDesc": "Vymazat roli (pro umožnění všem vidět kanály rustplusplus).", "commandsRoleDesc": "Nastavit/Vymazat specific<PERSON> roli, kter<PERSON> bude moci vidět obsah kategorie rustplusplus.", "commandsRoleSetDesc": "Nastavit roli.", "commandsRoleSetRoleDesc": "Role k<PERSON><PERSON> bude moci vidět ka<PERSON><PERSON> rustplusplus.", "commandsStackDesc": "Zobrazí velikost stacku položky.", "commandsStoragemonitorDesc": "Operace na Storage Monitorech.", "commandsStoragemonitorEditDesc": "Upravit vlastnosti Storage Monitoru.", "commandsStoragemonitorEditIdDesc": "ID Storage Monitoru.", "commandsStoragemonitorEditImageDesc": "Nastavení o<PERSON>, který nejlépe reprezentuje Storage Monitor.", "commandsSwitchDesc": "Operace na Smart Switche.", "commandsSwitchEditDesc": "Upravit vlastnosti Smart Switche.", "commandsSwitchEditIdDesc": "ID Smart Switche.", "commandsSwitchEditImageDesc": "Nastaven<PERSON> o<PERSON>, <PERSON><PERSON><PERSON> ne<PERSON> reprezent<PERSON><PERSON> Switch.", "commandsUpkeepDesc": "Zobrazit hodnotu upkeepu položky.", "commandsUptimeBotDesc": "Zobrazit uptime bota.", "commandsUptimeDesc": "Zobrazit uptime bota a serveru.", "commandsUptimeServerDesc": "Zobrazit uptime serveru.", "commandsVoiceBotJoinedVoice": "Bot se připojil k Voicechannelu", "commandsVoiceBotLeftVoice": "Bot opustil Voicechannel", "commandsVoiceDesc": "Hlasové příkazy bota", "commandsVoiceFemale": "Ž<PERSON>", "commandsVoiceFemaleDescription": "Nastavení pohlaví hlasového aktéra na ženu", "commandsVoiceGenderDesc": "Nastavení pohlaví bota hlasového aktéra.", "commandsVoiceJoin": "Připojuji se k hlasovému ka<PERSON> {name} s ID {id} v klanu {guild}", "commandsVoiceJoinDesc": "Připojil se k Voicechannelu", "commandsVoiceLeave": "Opouštím hlasový kanál {name} s ID {id} v klanu {guild}", "commandsVoiceLeaveDesc": "Opustil Voicechannel", "commandsVoiceMale": "Muž", "commandsVoiceMaleDescription": "Nastaví pohlaví hlasového aktéra na mužského pohlaví", "commandsVoiceNotInVoice": "Nejsi připojen do hlasového kanálu", "connect": "Connect", "connectCap": "PŘIPOJIT", "connected": "Připojeno", "connectedCap": "PŘIPOJENO", "connectedToServer": "PŘIPOJENO KSERVERU.", "connectingCap": "PŘIPOJOVÁNÍ", "connectingToServer": "PŘIPOJOVÁNÍ NA SERVER...", "connectionEvents": "Události připojení", "connectionRefusedTo": "Připojení odmítnuto k: {id}.", "connectionsCap": "PŘIPOJENÍ", "couldNotAddStepTracers": "Nelze přidat stopovací body.", "couldNotAppendMapMarkers": "Nelze připojit značky mapy, instance rustplus info není nastavena.", "couldNotAppendMapMonuments": "Nelze připojit monumenty mapy, instance rustplus info není nastavena.", "couldNotAppendMapTracers": "Nelze připojit mapové tracery, instance rustplus info není nastavena.", "couldNotConnectTo": "<PERSON><PERSON><PERSON> se připojit k: {id}.", "couldNotCreateCategory": "Nelze vytvořit kategorii: {name}", "couldNotCreateTextChannel": "Could not create text channel: {name}", "couldNotDeferInteraction": "Could not defer interaction.", "couldNotDeleteCategory": "Kategorii nelze odstranit: {categoryId}", "couldNotDeleteChannel": "Ka<PERSON>l nelze odstranit: {channelId}", "couldNotDeleteMessage": "Could not delete message: {message}", "couldNotFindAnyPlayers": "Could not find any players.", "couldNotFindCategory": "Could not find category: {category}", "couldNotFindChannel": "Could not find channel: {channel}", "couldNotFindCraftDetails": "Podrobnosti o craftu pro {name} nelze najít.", "couldNotFindDecayDetails": "Podrobnosti o decayu pro {name} nelze najít.", "couldNotFindDespawnDetails": "Podrobnosti o despawnu pro {name} nelze najít.", "couldNotFindGuild": "Could not find guild: {guildId}", "couldNotFindLanguage": "Could not find language: {language}", "couldNotFindMessage": "Could not find message {message}", "couldNotFindPlayer": "Could not find a player {name}.", "couldNotFindPlayerId": "Nelze najít hráče s id {id}.", "couldNotFindRecycleDetails": "Nelze najít podrobnosti o recyklaci pro {name}.", "couldNotFindResearchDetails": "Podrobnosti výzkumu pro {name} nelze najít.", "couldNotFindRole": "Could not find role: {roleId}", "couldNotFindStackDetails": "Nelze najít podrobnosti o stacku pro {name}.", "couldNotFindTeammate": "Could not find teammate: {name}.", "couldNotFindUpkeepDetails": "Nelze najít podrobnosti o upkeepu pro {name}.", "couldNotFindUser": "Could not find user: {userId}", "couldNotGetChannelWithId": "Could not get channel with id: {id}.", "couldNotIdentifyMember": "Could not identify team member: {name}.", "couldNotPerformBulkDelete": "Could not perform bulkDelete on channel: {channel}", "couldNotPerformMessageDelete": "Could not perform message delete.", "couldNotPerformMessagesFetch": "Could not perform messages fetch on channel: {channel}", "couldNotRegisterSlashCommands": "Could not register Slash Commands for guild: {guildId}. ", "couldNotSetParent": "Could not set parent for channel: {channelId}", "craft": "Craft", "crate": "Crate", "createGroupCap": "CREATE GROUP", "createTrackerCap": "CREATE TRACKER", "credentialsAddedSuccessfully": "FCM Credentials were added successfully for steamId: {steamId}!", "credentialsAlreadyRegistered": "FCM Credentials for steamId: {steamId} are already registered!", "credentialsCannotStartLiteAlreadyHoster": "Cannot start FCM Listener Lite for steamId: {steamId}. Already hoster.", "credentialsDoNotExist": "FCM Credentials for steamId: {steamId} does not exist.", "credentialsHosterNotSetForGuild": "FCM Credentials hoster is not set for guild {id}, please set a hoster.", "credentialsNotRegistered": "FCM Credentials for steamId: {steamId} is not registered!", "credentialsNotRegisteredForGuild": "FCM Credentials are not registered for guild: {id}, cannot start FCM-listener.", "credentialsRemovedSuccessfully": "FCM Credentials for steamId: {steamId} was removed successfully!", "credentialsSetHosterSuccessfully": "FCM Credentials hoster was successfully set to steamId: {steamId}.", "currencySign": "Currency Sign", "currentCommandDelay": "Current Command Delay: {delay} seconds.", "currentItemHp": "Součastné HP předmětu.", "currentPrefixPlaceholder": "Current Prefix: {prefix}", "customCommand": "Custom Command", "customTimerEditCargoShipEgressLabel": "Cargoship egress time (seconds):", "customTimerEditCrateOilRigUnlockLabel": "OilRig Locked Crate unlock time (seconds):", "customTimerEditDesc": "Editing of Custom Timers", "customTimersCap": "CUSTOM TIMERS", "dash": "Dash", "dayOfWipe": "Day {day}", "deathCap": "DEATH", "decay": "Decay", "decayTimeForItem": "<PERSON>as decaye pro {item} je {time}.", "decayingCap": "DECAYING", "deleteUnreachableDevicesCap": "ODSTRANIT NEDOSAHNUTELNÁ ZAŘÍZENÍ", "despawnTime": "Čas <PERSON>", "despawnTimeOfItem": "<PERSON>as despawnu pro {item} je {time}.", "deviceIsAlreadyOnOff": "{device} is already {status}.", "deviceIsCurrentlyOnOff": "{device} is currently {status}.", "deviceWasTurnedOnOff": "{device} was turned {status}.", "disabledCap": "DISABLED", "discoFloor": "Discofloor", "disconnectCap": "DISCONNECT", "disconnected": "Odpojeno", "disconnectedCap": "DISCONNECTED", "disconnectedFromServer": "DISCONNECTED FROM SERVER.", "discordCap": "DISCORD", "discordUsers": "Discord už<PERSON>", "displayInformationBattlemetricsAllOnlinePlayers": "<PERSON><PERSON><PERSON> by se v information kanálu zobrazit všichni online hráči z Battlemetrics?", "displayingMap": "Displaying {mapName} map.", "displayingOnlinePlayers": "Displaying online players.", "distanceDirectionGrid": "{distance}m in direction {direction}° [{grid}].", "doorController": "Door Controller", "dot": "Dot", "eastOfGrid": "East of grid", "editCap": "EDIT", "editing": "<PERSON><PERSON><PERSON>", "editingOf": "Úprava {entity}", "egressInTime": "Opuštení za {time} na {location}.", "eight": "Eight", "elevator": "Elevator", "empty": "Empty", "enabledCap": "ENABLED", "entityId": "ID Entity", "equalsSign": "Equals Sign", "errorCap": "ERROR", "errorExecutingCommand": "There was an error while executing this command!", "eventCap": "EVENT", "eventInfo": "Event Information", "exclamationMark": "Exclamation Mark", "failedToScrapeProfileName": "Failed to scrape profile name: {link}.", "failedToScrapeProfilePicture": "Failed to scrape profile picture: {link}.", "fcmCredentials": "FCM Credentials", "fcmListenerStartHost": "FCM-listener Host will start in 5 seconds for guildId: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "FCM-listener Li<PERSON> will start in 5 seconds for guildId: {guildId}, steamId: {steamId}.", "ferryTerminal": "Ferry Terminal", "fishingVillage": "Fishing Village", "five": "Five", "four": "Four", "giantExcavatorPit": "Giant Excavator Pit", "greaterThanSign": "Greater-than Sign", "groupAddSwitchDesc": "Add Switch to {group}", "groupRemoveSwitchDesc": "Re<PERSON>ve <PERSON> from {group}", "harbor": "Harbor", "hasBeenAliveLongest": "{name} has been alive the longest ({time}).", "hash": "Hash", "hbhfSensor": "HBHF Sensor", "heart": "Heart", "heater": "Heater", "heavyScientistCalledSetting": "When Heavy Scientists are called to Oil Rig, send a notification.", "heavyScientistsCalledLarge": "Heavy Scientists got called to the Large Oil Rig at {location}.", "heavyScientistsCalledSmall": "Heavy Scientists got called to the Small Oil Rig at {location}.", "hideTrademark": "Hide trademark.", "hoster": "Hoster", "hp": "HP", "hpExceedMax": "Hp {hp} př<PERSON><PERSON>je maximum {max}.", "hqmQuarry": "HQM Quarry", "ignoreSetAvatar": "Ignored set<PERSON><PERSON>ar", "ignoreSetNickname": "Ignored setNickname", "ignoreSetUsername": "Ignored setUsername", "inGameBotMessagesMuted": "In-Game bot messages muted.", "inGameBotMessagesUnmuted": "In-Game bot messages unmuted.", "inGameCap": "IN-GAME", "inGameEventInfo": "In-game event information", "inGameTeamNotificationsSetting": "In-Game teammate notifications.", "inGameTime": "In-Game time: {time}.", "index": "Index", "infoCap": "INFO", "inside": "Uvnitř", "interactionEditReplyFailed": "Interaction edit reply failed: {error}", "interactionInvalidChannel": "Interaction from an invalid channel.", "interactionReplyFailed": "Interaction reply failed: {error}", "interactionUpdateFailed": "Interaction update failed: {error}", "invalidBattlemetricsId": "Neplatný Battlemetrics ID.", "invalidGuildOrChannel": "Invalid guild or channel.", "invalidHpInterval": "Neplatný interval HP {hp}.", "invalidId": "Invalid ID: {id}.", "invalidStructureType": "Neplatný typ struktury {type}.", "invalidSubcommand": "Invalid subcommand.", "invalidTimeDistance": "Invalid time distance: {distance}, prev: {prevTime}, new: {newTime}", "isDecaying": "{device} is decaying!", "isNoLongerConnected": "{device} is no longer electrically connected!", "item": "<PERSON><PERSON>", "itemAvailableInVendingMachine": "{items} just became available in a Vending Machine at [{location}].", "itemAvailableNotifyInGameSetting": "When an item from the subscription list becomes available in a Vending Machine, notify In-Game?", "junkyard": "Junkyard", "justSubscribedToItem": "Just subscribed to item {name}.", "languageCode": "Language code: {code}", "languageLangNotSupported": "The language {language} is not supported.", "languageNotSupported": "The language is not supported.", "largeBarn": "Large Barn", "largeFishingVillage": "Large Fishing Village", "largeOilRig": "Large Oil Rig", "largeWoodBox": "Large Wood Box", "lastTrigger": "Poslední aktivace", "launchSite": "Launch Site", "leaderAlreadyLeader": "{name} is already team leader.", "leaderCommandIsDisabled": "Leader command is disabled in settings.", "leaderCommandOnlyWorks": "Leader command only works if the current leader is {name}.", "leaderTransferred": "Team leadership was transferred to {name}.", "leavingMapAt": "Opouští na {location}.", "lessThanSign": "Less-than Sign", "lighthouse": "Lighthouse", "linkCap": "LINK", "location": "Location", "lockedCrateLargeOilRigUnlocked": "Locked Crate at Large Oil Rig at {location} has been unlocked.", "lockedCrateOilRigUnlockedSetting": "When a Locked Crate at Oil Rig is unlocked, send a notification.", "lockedCrateSmallOilRigUnlocked": "Locked Crate at Small Oil Rig at {location} has been unlocked.", "logDiscordCommand": "Discord příkaz - Klan: {guild}, ka<PERSON><PERSON>: {channel}, Uživatel: {user}, Zpráva: {message}.", "logDiscordMessage": "Discord Zpráva - Klan: {guild}, ka<PERSON><PERSON>: {channel}, <PERSON><PERSON><PERSON>l: {user}, Zpráva: {message}.", "logInGameCommand": "{type} - <PERSON><PERSON><PERSON><PERSON>: {command}, Uživatel: {user}.", "logInGameMessage": "Zpráva: {message}, <PERSON><PERSON><PERSON>l: {user}", "logSmartSwitchGroupValueChange": "<PERSON><PERSON><PERSON> Smart Switch - Hodnota: {value}.", "logSmartSwitchValueChange": "Smart Switch - Hodnota: {value}.", "loggedInAs": "LOGGED IN AS: {name}", "makeSureApplicationsCommandsEnabled": "Make sure applications.commands is checked when creating the invite URL.", "map": "Map", "mapSalt": "Map Salt", "mapSeed": "Map Seed", "mapSize": "Map Size", "mapWipeDetectedNotifySetting": "When Map Wipe is detected, should {group} be notified?", "markerAdded": "Marker {name} at [{location}] was added.", "markerDoesNotExist": "Marker {name} does not exist.", "markerLocation": "Marker {name} at [{location}] is {distance}m from {player} in direction {direction}°.", "markerRemoved": "Marker {name} at [{location}] was removed.", "message": "Message", "messageCap": "ZPRÁVA", "messageDeletedIn30": "This message will be deleted in 30 seconds.", "messageEditFailed": "Message edit failed: {error}", "messageReplyFailed": "Message reply failed: {error}", "messageSendFailed": "Message send failed: {error}", "messageWasSent": "Message was sent.", "militaryTunnel": "Military Tunnel", "miningOutpost": "Mining Outpost", "missileSilo": "Missile Silo", "missingArguments": "Missing arguments.", "missingPermission": "You don't have permission to do this.", "missingTimerMessage": "Missing timer message.", "modalValueChange": "Modal Interakce - VerifyId: {id}, Hodnota: {value}.", "more": "more", "morePlayers": "{players} ...{number} more.", "mutedCap": "MUTED", "name": "Name", "nameChangeHistory": "Name Change History", "new": "Nový", "newVendingMachine": "New Vending Machine located at {location}.", "newsCap": "NEWS", "noActiveTimers": "No active timers.", "noCommandDelay": "No command delay.", "noCommunicationSmartSwitch": "Could not communicate with Smart Switch: {name}", "noData": "No data.", "noDataOnLargeOilRig": "No current data on Large Oil Rig.", "noDataOnSmallOilRig": "No current data on Small Oil Rig.", "noDelayCap": "NO DELAY", "noItemFound": "Item could not be found in any Vending Machines...", "noItemWithIdFound": "No item with id {id} could be found.", "noItemWithNameFound": "No item with name {name} could be found.", "noNameIdGiven": "No 'name' or 'id' was given.", "noOneIsAfk": "No one is AFK.", "noOneIsOffline": "No one is offline.", "noOneIsOnline": "No one is online.", "noRegisteredConnectionEvents": "No registered connection events yet.", "noRegisteredConnectionEventsUser": "No registered connection events for {user}.", "noRegisteredDeathEvents": "No registered death events yet.", "noRegisteredDeathEventsUser": "No registered death events for {user}.", "noRegisteredEvents": "No registered events yet.", "noRegisteredMarkers": "No registered markers.", "noSavedNotes": "There are no saved notes.", "noToolCupboardWereFound": "No Tool Cupboard monitors were found.", "none": "None", "northEast": "North East", "northOfGrid": "North of grid", "northWest": "North West", "notAValidOrderType": "{order} is not a valid order type.", "notActive": "Not active.", "notConnectedToRustServer": "Not currently connected to a rust server.", "notExistInSubscription": "Item {name} does not exist in subscription list.", "notFoundCap": "NOT FOUND", "notPartOfRole": "You are not part of the {role} role, therefore you can't run bot commands.", "notShowingCap": "NOT SHOWING", "noteCap": "NOTE", "noteIdDoesNotExist": "Note ID: {id} does not exist.", "noteIdInvalid": "Note ID is invalid.", "noteIdWasRemoved": "Note ID: {id} was removed.", "noteSaved": "Note saved.", "offCap": "OFF", "offline": "Offline", "offlineTime": "Offline time", "oilRig": "Oil Rig", "old": "Old", "onCap": "ON", "one": "One", "online": "Online", "onlineTime": "Online time", "onlyOneInTeam": "You are the only one in the team.", "outpost": "Outpost", "outside": "Outside", "oxumsGasStation": "Oxum's Gas Station", "pairing": "pairing", "patrolHelicopter": "Patrol Helicopter", "patrolHelicopterDestroyedSetting": "When Patrol Helicopter gets destroyed, send a notification.", "patrolHelicopterDetectedSetting": "When Patrol Helicopter is detected, send a notification.", "patrolHelicopterEntersMap": "Patrol Helicopter enters the map from {location}.", "patrolHelicopterLeftMap": "Patrol Helicopter just left the map at {location}.", "patrolHelicopterLeftSetting": "When <PERSON> Helic<PERSON>ter left the map, send a notification.", "patrolHelicopterLocatedAt": "Patrol Helicopter is located at {location}.", "patrolHelicopterNotCurrentlyOnMap": "Patrol Helicopter is not currently on the map.", "patrolHelicopterTakenDown": "Patrol Helicopter was taken down {location}.", "percentSign": "Percent Sign", "pipe": "<PERSON><PERSON>", "playerHasBeenAliveFor": "{name} has been alive for {time}.", "playerId": "Player ID", "playerJoinedTheTeam": "{name} joined the team.", "playerJustConnected": "{name} just connected.", "playerJustConnectedTo": "{name} just connected to {server}.", "playerJustConnectedTracker": "{name} se právě připojil z trackeru {tracker}.", "playerJustDied": "{name} just died at {location}.", "playerJustDisconnected": "{name} just disconnected.", "playerJustDisconnectedFrom": "{name} just disconnected from {server}.", "playerJustDisconnectedTracker": "{name} se prá<PERSON>ě odpojil od trackeru {tracker}.", "playerJustReturned": "{name} just returned ({time}).", "playerJustWentAfk": "{name} just went AFK.", "playerLeftTheTeam": "{name} left the team.", "playerNotPairedWithServer": "Leader command does not work because {name} is not paired with the server.", "players": "Players", "playersSearch": "Players Search", "plusSign": "Plus Sign", "populationPlayers": "Population: ({current}/{max}) players.", "populationQueue": "{number} players in queue.", "powerPlant": "Power Plant", "profile": "Profile", "proxLocation": "{name} is {distance}m from {caller} in direction {direction}° [{location}]", "quantity": "Quantity", "questionMark": "Question Mark", "ranch": "Ranch", "ratelimited": "RATELIMITED", "reconnectingCap": "RECONNECTING", "reconnectingToServer": "RECONNECTING TO SERVER...", "recycle": "Recycle", "recycleCap": "RECYCLE", "recycler": "Recycler", "remain": "left", "removePlayerCap": "REMOVE PLAYER", "removeSwitchCap": "REMOVE SWITCH", "removedSubscribeItem": "Item {name} have been removed from subscription.", "research": "Research", "researchTable": "Research Table", "resetSuccess": "Successfully reset Discord.", "responseContainError": "Response contain error property with value: {error}.", "responseIsEmpty": "Response is empty.", "responseIsUndefined": "Response is undefined.", "responseTimeout": "Timeout reached while waiting for response.", "resultRecycling": "Result of recycling", "roleCleared": "rustplusplus role has been cleared.", "roleSet": "rustplusplus role has been set to {name}.", "rustMonument": "Rust monument", "rustplusOperational": "RUSTPLUS OPERATIONAL.", "safe-zone-recycler": "Safe Zone Recycler", "samsite": "SAM site", "satelliteDish": "Satellite Dish", "scrap": "Scrap", "searchResult": "Search result for item: **{name}**", "second": "{second} second", "secondCommandDelay": "{second} second command delay.", "seconds": "{seconds} seconds", "secondsCommandDelay": "{seconds} seconds command delay.", "selectInGamePrefixSetting": "Select what in-game command prefix that should be used:", "selectLanguageExtendSetting": "Make sure you run **/reset discord** to successfully load the new language.", "selectLanguageSetting": "Select what language the bot uses:", "selectMenuValueChange": "Select Menu Interaction - VerifyId: {id}, Value: {value}.", "selectTrademarkSetting": "Select which trademark that should be shown in every in-game message.", "sell": "sell", "semicolon": "Semicolon", "sentTextToSpeech": "Sent the Text-To-Speech.", "server": "server", "serverId": "Server ID", "serverInfo": "Server Information", "serverInvalid": "The connection to the server seems to be invalid. Try to re-pair to the server.", "serverJustOffline": "Server just went offline.", "serverJustOnline": "Server just went online.", "serverStatus": "Server Status", "serviceUnavailable": "Service Unavailable: {error}", "setBotLanguage": "Set the bot language to: {language}.", "seven": "Seven", "sewerBranch": "Sewer Branch", "shouldBotBeMutedSetting": "Should the bot be muted in-game?", "shouldCommandsEnabledSetting": "Should in-game commands be enabled?", "shouldLeaderCommandEnabledSetting": "Should the leader command be enabled?", "shouldLeaderCommandOnlyForPairedSetting": "Should the leader command only work for people that are paired with the server?", "shouldSmartAlarmNotifyNotConnectedSetting": "Should Smart Alarms notify even if they are not setup on the connected rust server?", "shouldSmartAlarmsNotifyInGameSetting": "Should Smart Alarms notify In-Game?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Should Smart Switches and Smart Switch Groups notify In-Game when they are changed from discord?", "showingBlacklist": "Showing the blacklist.", "showingSubscriptionList": "Showing the subscription list.", "shredder": "Shredder", "sirenLight": "Siren Light", "six": "Six", "slash": "Slash", "slashCommandInteraction": "Slash Command Interaction - Guild: {guild}, Channel: {channel}, User: {user}, Command: {command}, VerifyId: {id}.", "slashCommandValueChange": "Slash Command Interaction - VerifyId: {id}, Value: {value}.", "slashCommandsSuccessRegister": "Successfully registered application commands for guild: {guildId}.", "slots": "Slots", "smallOilRig": "Small Oil Rig", "smartAlarm": "Smart Alarm", "smartAlarmEditSuccess": "Successfully edited Smart Alarm {name}.", "smartAlarmNotifyExtendSetting": "- These Alarm notifications will use the title and message given to the Smart Alarm in-game.\n- These Smart Alarms might not be available in the alarms text channel in discord.", "smartDeviceNotFound": "{device} could not be found! Either it have been destroyed or {user} have lost tool cupboard access.", "smartSwitch": "Smart Switch", "smartSwitchAutoDay": "Smart Switch will be active only during the day.", "smartSwitchAutoNight": "Smart Switch will be active only during the night.", "smartSwitchAutoOff": "Smart Switch will automatically go inactive during update cycle.", "smartSwitchAutoOffAnyOnline": "Smart Switch will automatically go inactive if any teammate is online.", "smartSwitchAutoOffProximity": "Smart Switch will automatically go inactive if teammate is in proximity.", "smartSwitchAutoOn": "Smart Switch will automatically go active during update cycle.", "smartSwitchAutoOnAnyOnline": "Smart Switch will automatically go active if any teammate is online.", "smartSwitchAutoOnProximity": "Smart Switch will automatically go active if teammate is in proximity.", "smartSwitchEditProximityLabel": "Proximity Setting (meters):", "smartSwitchEditSuccess": "Successfully edited Smart Switch {name}.", "smartSwitchNormal": "Smart Switch work as normal.", "smilyFace": "<PERSON><PERSON><PERSON>", "somethingWrongWithConnection": "Something went wrong with connection.", "southEast": "South East", "southOfGrid": "South of grid", "southWest": "South West", "sprinkler": "Sprinkler", "stackSize": "Stack Size", "stackSizeOfItem": "Stack size of {item} is {quantity}x.", "status": "Status", "statusNotConnectedToServer": "**STATUS** `NOT CONNECTED TO SERVER!`", "statusNotElectronicallyConnected": "**STATUS** `NOT ELECTRICALLY CONNECTED!`", "statusNotFound": "**STATUS**: NOT FOUND", "steamId": "SteamID", "stoneQuarry": "Stone Quarry", "storageMonitor": "Storage Monitor", "storageMonitorEditSuccess": "Successfully edited Storage Monitor {name}.", "streamerMode": "Streamer Mode", "subscribeToChangesBattlemetrics": "Subscribe to different changes on Battlemetrics.", "subscriptionList": "Subscription list", "subscriptionListEmpty": "Item subscription list is empty.", "sulfurQuarry": "Sul<PERSON>r Quarry", "switches": "Switches", "teamMember": "Team Member", "teamMemberInfo": "Team Member Information", "theDome": "The Dome", "theIdOfTheItem": "The id of the item.", "theNameOfTheItem": "The name of the item.", "theNameOfThePlayer": "The name of the player.", "three": "Three", "tilde": "<PERSON><PERSON>", "time": "Time", "timeBeforeCargoEntersEgress": "{time} before Cargo Ship at {location} enters egress stage.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} before Locked Crate at Large Oil Rig ({location}) unlocks.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} before Locked Crate at Small Oil Rig ({location}) unlocks.", "timeCap": "TIME", "timeFormatInvalid": "Time format invalid.", "timeLeftTimer": "{id}: Time left: {time}, Message: {message}", "timeSinceAlarmWasTriggered": "The alarm {alarm} was triggered {time} ago.", "timeSinceCargoLeft": "{time} since Cargo Ship left the map.", "timeSinceChinook47OnMap": "{time} since the last Chinook 47 was on the map.", "timeSinceHeavyScientistsOnLarge": "{time} since Heavy Scientists last got called to Large Oil Rig.", "timeSinceHeavyScientistsOnSmall": "{time} since Heavy Scientists last got called to Small Oil Rig.", "timeSinceLast": "{time} since last.", "timeSinceLastEvent": "{time} since last event.", "timeSinceLastSinceDestroyedLong": "{time1} since the last Patrol Helicopter was on the map, {time2} since it last got downed{location}.", "timeSinceLastSinceDestroyedShort": "{time1} since last.\n{time2} since destroyed{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} since the Patrol Helicopter was on the map.", "timeSinceTravelingVendorWasOnMap": "{time} since the Traveling Vendor was on the map.", "timeSinceWipe": "{time} since wipe.", "timeTill": "Time till {event}", "timeTillDaylight": "{time} before daylight.", "timeTillNightfall": "{time} before nightfall.", "timeTillStructureDecay": "{time} before {type} wall decay.", "timeUntilUnlocksAt": "{time} until unlocks at {location}.", "timer": "Timer: {message}.", "timerIdDoesNotExist": "Timer ID: {id} does not exist.", "timerIdInvalid": "Timer ID is invalid.", "timerRemoved": "Timer ID: {id} was removed.", "timerSet": "<PERSON><PERSON><PERSON><PERSON> nastaven na {time}.", "tokensDidNotReplenish": "<PERSON><PERSON><PERSON> did not replenish in time.", "toolCupboard": "Bedna s nářadím", "total": "Total", "tracker": "Tracker", "trackerAddPlayerDesc": "Přidat hráče do {tracker}", "trackerRemovePlayerDesc": "Odstranit hráče z {tracker}", "trademarkShownBeforeMessage": "{trademark} se zobrazí před zprávami.", "trainYard": "Nádraží", "travelingVendor": "Traveling Vendor", "travelingVendorDetectedSetting": "When the Traveling Vendor is detected, send a notification.", "travelingVendorHaltedAt": "The Traveling Vendor stopped at {location}.", "travelingVendorHaltedSetting": "When the Traveling Vendor stops moving, send a notification.", "travelingVendorLeftSetting": "When the Traveling Vendor left the map, send a notification.", "travelingVendorLocatedAt": "The Traveling Vendor is located at {location}.", "travelingVendorLeftMap": "The Traveling Vendor just left the map at {location}.", "travelingVendorNotCurrentlyOnMap": "The Traveling Vendor is not currently on the map.", "travelingVendorResumedAt": "The Traveling Vendor resumed moving at {location}.", "travelingVendorSpawnedAt": "The Traveling Vendor spawned at {location}.", "turnOffCap": "VYPNUTO", "turnOnCap": "ZAPNUTO", "turningGroupOnOff": "<PERSON>ěn<PERSON><PERSON><PERSON> skupiny {group} {status}.", "two": "Dvě", "type": "<PERSON><PERSON>", "unavailable": "Nedostupné", "underscore": "Po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underwater": "Underwater", "underwaterLab": "Podvodní laboratoř", "unhandledRejection": "Unhandled Rejection: {error}", "unknown": "Neznámý", "unknownInteraction": "Unknown Interaction...", "unmutedCap": "UNMUTED", "updateCap": "UPDATE", "upkeep": "Údržba", "upkeepForItem": "Upkeep for {item} is {cost}.", "userAddedToBlacklist": "{user} byl p<PERSON> na blacklist.", "userAlreadyInBlacklist": "{user} je již na blacklistu.", "userButtonInteraction": "Interakce tlačítka - klan: {guild}, Kanál: {channel}, Uživatel: {user}, CustomID: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Interakce tlačítka - VerifyId: {id} ÚSPĚCH", "userJustConnected": "{name} se práv<PERSON> připojil.", "userModalInteraction": "Modal Interakce - klan: {guild}, Kanál: {channel}, Uživatel: {user}, CustomID: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Modal Interakce - VerifyId: {id} ÚSPĚCH", "userNotInBlacklist": "{user} není v blacklistu.", "userNotRegistered": "{user} nen<PERSON> regis<PERSON>.", "userPartOfBlacklist": "VerifyId: {id}, {user} je součástí blacklistu.", "userPartOfBlacklistDiscord": "Zakázaný Uživatel! Klan: {guild}, Kanál: {channel}, Uživatel: {user}, Zpráva: {message}.", "userPartOfBlacklistInGame": "Zakázaný uživatel! Uživatel: {user}, Zpráva: {message}.", "userRemovedFromBlacklist": "{user} byl odebr<PERSON> z blacklistu.", "userSaid": "{user} ř<PERSON>l, {text}", "userSelectMenuInteraction": "Vyberte Interakci v menu - klan: {guild}, kanál: {channel}, Uživatel: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Vyberte interakci v menu - VerifyId: {id} ÚSPĚCH", "userTurnedOnOffSmartSwitchFromDiscord": "{user} <PERSON><PERSON><PERSON><PERSON>l Smart Switch {name} {status} přes discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} změ<PERSON>l Smart Switch Skupinu {name} {status} přes discord.", "value": "Hodnota", "vendingMachine": "Prodejní automat", "vendingMachineDetectedSetting": "<PERSON><PERSON><PERSON> je nalezen nový prodejní automat, po<PERSON><PERSON>.", "voiceCap": "ZVUK", "warningCap": "VAROVÁNÍ", "waterTreatmentPlant": "Čistička odpadních vod", "websiteCap": "WEBSITE", "websocketClosedBeforeConnection": "WebSocket byl uzavřen předtím než bylo navázáno spojení.", "westOfGrid": "Západně od gridu", "wipe": "Wipe", "wipeDetected": "Wipe byl dete<PERSON>!", "yield": "Úrodnost", "youAreAlreadyLeader": "Už jsi vůdce.", "youAreNotPairedWithServer": "Příkaz na vůdce nefunguje, protože nejsi spárován se <PERSON>em."}