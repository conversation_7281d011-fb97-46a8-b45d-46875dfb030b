{"24HoursInGameTimePassed": "Han pasado 24 horas en el juego.", "abandonedCabins": "Cabañas abandonadas", "abandonedMilitaryBase": "Base militar abandonada", "abandonedSupermarket": "Supermercado abandonado", "addPlayerCap": "AÑADIR JUGADOR", "addSwitchCap": "AÑADIR INTERRUPTOR", "afkCap": "AUSENTE", "airfield": "Aeródromo", "alarmHaveNotBeenTriggeredYet": "La alarma {alarm} no se ha activado aún.", "alias": "<PERSON><PERSON>", "aliasAlreadyExist": "Alias already exist.", "aliasIndexCouldNotBeFound": "Alias index could not be found.", "aliasWasAdded": "<PERSON><PERSON> was added.", "aliasWasRemoved": "<PERSON><PERSON> was removed.", "aliases": "Aliases", "all": "todo", "allTeammatesAreDead": "Todos tus compañeros están muertos.", "alreadySubscribedToItem": "Ya está suscrito al item {name}.", "ampersand": "Ampersand", "andMorePlayers": "... y {number} jugadores más.", "any": "Any", "apostrophe": "Apóstrofe", "arcticResearchBase": "Base de investigación polar", "asterisk": "Asterisco", "asteriskCctvDesc": "Los caracteres * (asteriscos) significan que necesitas un número diferente para cada mapa", "atLocation": "En {location}.", "atSign": "En la señal", "autoDayCap": "AUTO-DÍA", "autoNightCap": "AUTO-NOCHE", "autoOffAnyOnlineCap": "AUTO-OFF-ANY-ONLINE", "autoOffCap": "AUTO-APAGADO", "autoOffProximityCap": "AUTO-APAGADO-PROXIMIDAD", "autoOnAnyOnlineCap": "AUTO-ON-ANY-ONLINE", "autoOnCap": "AUTO-ENCENDIDO", "autoOnProximityCap": "AUTO-ENCENDIDO-PROXIMIDAD", "autoSettingCap": "CONFIGURACIÓN AUTOMÁTICA: ", "automaticallyTurnBackOnOff": " Se cambió automáticamente a {status} en {time}.", "automaticallyTurningBackOnOff": "Cambiando {device} automáticamente a {status}.", "autoturret": "Torreta automática", "badGateway": "<PERSON><PERSON><PERSON> de enlace incorrecta: {error}", "banditCamp": "Campamento de Bandoleros", "baseIsUnderAttack": "¡Tu base está siendo atacada!", "battlemetricsApiRequestFailed": "Battlemetrics API Request Failed: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics Server {server} failed to update.", "battlemetricsGlobalLoginCap": "GLOBAL LOGIN", "battlemetricsGlobalLogoutCap": "GLOBAL LOGOUT", "battlemetricsGlobalNameChangesCap": "GLOBAL NAME CHANGES", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "Battlemetrics instance is missing id and name.", "battlemetricsInstanceCouldNotBeFound": "Battlemetrics Instance for {id} could not be found.", "battlemetricsOnlinePlayers": "Battlemetrics Online Players", "battlemetricsPlayersLogin": "Battlemetrics Players Login", "battlemetricsPlayersLogout": "Battlemetrics Players Logout", "battlemetricsPlayersNameChanged": "Battlemetrics Players Name Changed", "battlemetricsServerNameChanged": "Battlemetrics Server Name Changed", "battlemetricsServerNameChangesCap": "SERVER NAME CHANGES", "battlemetricsTrackerNameChangesCap": "TRACKER NAME CHANGES", "battlemetricsTrackerPlayerNameChanged": "Battlemetrics Tracker Player Name Changed", "blacklist": "Lista negra", "boomBox": "Radiocasete", "bot": "bot", "broadcaster": "<PERSON><PERSON><PERSON>", "buttonValueChange": "Interacción con botón - Id de verificación: {id}, Valor: {value}.", "buy": "comprar", "calculated": "Calculated", "cargoAt": "En {location}.", "cargoLeavingMapAt": "El cargo está saliendo del mapa en {location}.", "cargoLocatedAt": "El cargo está en {location}.", "cargoNotCurrentlyOnMap": "El cargo no está en el mapa.", "cargoShipDetectedSetting": "<PERSON>uando se detecte el cargo, envíe una notificación.", "cargoShipDockingAtHarbor": "Cargo ship just docked at the Harbor at {location}", "cargoShipDockingAtHarborSetting": "When Cargo Ship is docked at a harbor, send a notification.", "cargoShipEgressSetting": "Cuando el cargo empiece a irse, envíe una notificación.", "cargoShipEntersEgressStage": "El cargo se debería estar yendo del mapa por {location}.", "cargoShipEntersMap": "El cargo está entrando al mapa por {location}.", "cargoShipLeftHarbor": "Cargo ship just left the Harbor at {location}", "cargoShipLeftMap": "El cargo acaba de salir del mapa por {location}.", "cargoShipLeftSetting": "Cuando el cargo deje el mapa, envíe una notificación.", "cargoShipLocated": "El cargo está en {location}.", "cargoship": "Cargo", "ceilingLight": "Lámpara de techo", "channelNameActivity": "actividad", "channelNameAlarms": "alarmas", "channelNameCommands": "comandos", "channelNameEvents": "eventos", "channelNameInformation": "información", "channelNameServers": "servid<PERSON>", "channelNameSettings": "configuración", "channelNameStorageMonitors": "monitores-de-almacenamiento", "channelNameSwitchGroups": "grupos-de-interruptores", "channelNameSwitches": "interruptores", "channelNameTeamchat": "chat-de-equipo", "channelNameTrackers": "rast<PERSON><PERSON>", "chinook47": "Chinook 47", "chinook47DetectedSetting": "Cuando el Chinook 47 entre en el mapa, envía una notificación.", "chinook47EntersMap": "El Chinook entra en el mapa desde {location} para soltar la Caja Bloqueada.", "chinook47LeftMap": "Chinook 47 dejó el mapa en {location}.", "chinook47Located": "El Chinook 47 está en {location}.", "chinook47NotOnMap": "Chinook 47 no está actualmente en el mapa.", "christmasLights": "Luz de Navidad", "circumflex": "Circunfle<PERSON>", "clanTag": "Etiqueta de Clan", "codes": "Có<PERSON><PERSON>", "colon": "<PERSON><PERSON> puntos", "comma": "Coma", "commandCap": "COMANDO", "commandDelaySetting": "¿Debería haber un retraso para comandos? ¿Cuánto tiempo?", "commandNotPossibleDiscord": "El comando no es posible a través de discord.", "commandSyntaxAdd": "<PERSON><PERSON><PERSON>", "commandSyntaxAfk": "ausente", "commandSyntaxAlive": "vivo", "commandSyntaxArmored": "<PERSON>ado", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "conexión", "commandSyntaxConnections": "conexiones", "commandSyntaxCraft": "craft", "commandSyntaxDeath": "muerte", "commandSyntaxDeaths": "muertes", "commandSyntaxDecay": "caída", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "eventos", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "idioma", "commandSyntaxLarge": "grande", "commandSyntaxLeader": "<PERSON><PERSON><PERSON>", "commandSyntaxList": "lista", "commandSyntaxMarker": "marcador", "commandSyntaxMarkers": "marcadores", "commandSyntaxMarket": "mercado", "commandSyntaxMetal": "metal", "commandSyntaxMute": "silenciar", "commandSyntaxNote": "nota", "commandSyntaxNotes": "notas", "commandSyntaxOff": "apagar", "commandSyntaxOffline": "desconectado", "commandSyntaxOn": "encender", "commandSyntaxOnline": "conectado", "commandSyntaxPlayer": "jugador", "commandSyntaxPlayers": "jugadores", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "recycle", "commandSyntaxRemove": "eliminar", "commandSyntaxResearch": "research", "commandSyntaxSearch": "buscar", "commandSyntaxSend": "enviar", "commandSyntaxSmall": "pequeña", "commandSyntaxStack": "stack", "commandSyntaxStatus": "estado", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "piedra", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "equipo", "commandSyntaxTime": "tiempo", "commandSyntaxTimer": "temporizador", "commandSyntaxTimers": "temporizadores", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendor", "commandSyntaxTwig": "paja", "commandSyntaxUnmute": "desilenciar", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "mantenimiento", "commandSyntaxUptime": "uptime", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "<PERSON>ra", "commandsAlarmDesc": "Operaciones en Alarmas Inteligentes.", "commandsAlarmEditDesc": "Editar las propiedades de una Alarma Inteligente.", "commandsAlarmEditIdDesc": "El ID de la Alarma Inteligente.", "commandsAlarmEditImageDesc": "Establezca la imagen que mejor represente la Alarma Inteligente.", "commandsAliasAddAliasDesc": "The alias to use.", "commandsAliasAddDesc": "Add an alias.", "commandsAliasAddValueDesc": "The command/sequence of characters.", "commandsAliasDesc": "Create an alias for a command/sequence of characters.", "commandsAliasRemoveDesc": "Remove an alias.", "commandsAliasRemoveIndexDesc": "The index of the alias to remove.", "commandsAliasShowDesc": "Show all registered aliases.", "commandsBlacklistAddDesc": "<PERSON><PERSON><PERSON> usuario a la lista negra.", "commandsBlacklistDesc": "Incluye a un usuario en la lista negra para que no pueda usar el bot.", "commandsBlacklistDiscordUserDesc": "El usuario de discord.", "commandsBlacklistRemoveDesc": "Elimina a un usuario de la lista negra.", "commandsBlacklistShowDesc": "Muestra los usuarios que están en la lista negra.", "commandsBlacklistSteamidDesc": "El id de steam del usuario.", "commandsCctvDesc": "Mostrar los códigos de las cámaras CCTV de un monumento", "commandsCraftDesc": "Display the cost to craft an item.", "commandsCraftQuantityDesc": "The quantity of items to craft.", "commandsCredentialsAddDesc": "Agregar Credenciales FCM.", "commandsCredentialsDesc": "Establecer/Limpiar las Credenciales FCM para la cuenta de usuario.", "commandsCredentialsRemoveDesc": "Eliminar Credenciales FCM.", "commandsCredentialsRemoveSteamIdDesc": "SteamId de las Credenciales FCM a eliminar.", "commandsCredentialsSetHosterDesc": "Establecer el anfitrión de las Credenciales FCM.", "commandsCredentialsSetHosterSteamIdDesc": "SteamId del anfitrión de las Credenciales FCM.", "commandsCredentialsShowDesc": "Mostrar las Credenciales FCM actualmente registradas.", "commandsDecayDesc": "Display the decay time of an item.", "commandsDespawnDesc": "Display the despawn time of an item.", "commandsHelpCommandList": "Lista de Comandos", "commandsHelpDesc": "Mostrar mensaje de a<PERSON>.", "commandsHelpHowToCredentials": "Cómo Registrar Credenciales", "commandsHelpHowToPairServer": "Cómo emparejar el bot con el servidor Rust", "commandsItemDesc": "Get the details of an item.", "commandsLeaderDesc": "Dar o tomar el liderazgo de/a un miembro del equipo.", "commandsLeaderMemberDesc": "Nombre del miembro del equipo.", "commandsMapAllDesc": "Obtener el mapa incluyendo tanto nombres de monumentos como marcadores.", "commandsMapCleanDesc": "Obtener el mapa limpio.", "commandsMapDesc": "Obtener la imagen de mapa del servidor conectada actualmente.", "commandsMapMarkersDesc": "Obtener el mapa incluyendo marcadores.", "commandsMapMonumentsDesc": "Obtener el mapa incluyendo nombres de monumentos.", "commandsMarketDesc": "Operaciones para máquinas expendedoras dentro del juego.", "commandsMarketListDesc": "Mostrar la lista de suscripciones.", "commandsMarketOrderDesc": "El tipo de pedido.", "commandsMarketSearchDesc": "Busca un objeto en Maquinas expendedoras.", "commandsMarketSubscribeDesc": "Suscríbete a un item en las Maquinas Expendedoras.", "commandsMarketUnsubscribeDesc": "Quitar la suscripción de un item de las Máquinas Expendedoras.", "commandsPlayersBattlemetricsIdDesc": "The Battlemetrics ID of the server (default: The connected server).", "commandsPlayersDesc": "Conseguir la información de los jugador/es basada en Battlemetrics.", "commandsPlayersNameDesc": "Search for a player on Battlemetrics based on player name.", "commandsPlayersPlayerIdDesc": "Search for a player on Battlemetrics based on player id.", "commandsPlayersPlayerIdPlayerIdDesc": "The player id of the player.", "commandsPlayersStatusDesc": "Search for players that are online/offline/any.", "commandsRecycleDesc": "Display the output of recycling an item.", "commandsRecycleQuantityDesc": "The quantity of items to recycle.", "commandsRecycleRecyclerTypeDesc": "The recycler type (recycler, shredder, safe-zone-recycler).", "commandsResearchDesc": "Display the cost to research an item.", "commandsResetAlarmsDesc": "Recrear el canal de alarmas.", "commandsResetDesc": "Recrear los canales de Discord.", "commandsResetInformationDesc": "Recrear el canal de información.", "commandsResetServersDesc": "Recrear el canal de los servidores.", "commandsResetSettingsDesc": "Recrear el canal de configuración.", "commandsResetStorageMonitorsDesc": "Recrear el canal de monitores de almacenamiento.", "commandsResetSwitchesDesc": "Recrear los canales de interruptores y grupo-de-interruptores.", "commandsResetTrackersDesc": "Recrear el canal de rastreadores.", "commandsRoleClearDesc": "Quitar el rol (permitir a cualquiera ver los canales de rustplusplus).", "commandsRoleDesc": "Establecer/quitar un rol específico para poder ver el contenido de la categoría de rustplusplus.", "commandsRoleSetDesc": "E<PERSON>cer el rol.", "commandsRoleSetRoleDesc": "El rol con el que los canales de rustplusplus serán visibles.", "commandsStackDesc": "Display the stack size of an item.", "commandsStoragemonitorDesc": "Operaciones en los Monitores de Almacenamiento.", "commandsStoragemonitorEditDesc": "Editar las propiedades del Monitor de Almacenamiento.", "commandsStoragemonitorEditIdDesc": "El ID del Monitor de Almacenamiento.", "commandsStoragemonitorEditImageDesc": "Establece la imagen que mejor representa al Monitor de Almacenamiento.", "commandsSwitchDesc": "Operaciones en Interruptores Inteligentes.", "commandsSwitchEditDesc": "Editar las propiedades de un Interruptor Inteligente.", "commandsSwitchEditIdDesc": "El ID del Interruptor Inteligente.", "commandsSwitchEditImageDesc": "Establece la imagen que mejor represente el Interruptor Inteligente.", "commandsUpkeepDesc": "Display the upkeep cost of an item.", "commandsUptimeBotDesc": "Mostrar el tiempo que bot ha estado operativo.", "commandsUptimeDesc": "Mostrar el tiempo que el bot y el servidor han estado operativos.", "commandsUptimeServerDesc": "Mostrar el tiempo que el servidor ha estado operativo.", "commandsVoiceBotJoinedVoice": "El bot ha entrado en el canal de voz", "commandsVoiceBotLeftVoice": "El bot ha salido del canal de voz", "commandsVoiceDesc": "Comandos de voz del bot", "commandsVoiceFemale": "Femenino", "commandsVoiceFemaleDescription": "E<PERSON>ce el género del actor de voz a femenino", "commandsVoiceGenderDesc": "<PERSON><PERSON>ce el género del actor de voz del bot.", "commandsVoiceJoin": "Entrando al canal de voz {name} con ID {id} del gremio {guild}", "commandsVoiceJoinDesc": "Entra al canal de voz", "commandsVoiceLeave": "Saliendo del canal de voz {name} con ID {id} del gremio {guild}", "commandsVoiceLeaveDesc": "Sale del canal de voz", "commandsVoiceMale": "<PERSON><PERSON><PERSON><PERSON>", "commandsVoiceMaleDescription": "E<PERSON>ce el género del actor de voz a masculino", "commandsVoiceNotInVoice": "No estás en un canal de voz", "connect": "Conectar", "connectCap": "CONECTAR", "connected": "Connected", "connectedCap": "CONECTADO", "connectedToServer": "CONECTADO AL SERVIDOR.", "connectingCap": "CONECTANDO", "connectingToServer": "CONECTANDO AL SERVIDOR...", "connectionEvents": "Connection Events", "connectionRefusedTo": "Conexión rechazada a: {id}.", "connectionsCap": "CONEXIONES", "couldNotAddStepTracers": "No se han podido añadir trazadores de pasos.", "couldNotAppendMapMarkers": "No se pudieron añadir marcadores de mapa, la instancia de información de rustplus no está establecida.", "couldNotAppendMapMonuments": "No se pudieron añadir monumentos del mapa, la instancia de información de rustplus no está establecida.", "couldNotAppendMapTracers": "No se pudieron añadir rastreadores del mapa, la instancia de información de rustplus no está establecida.", "couldNotConnectTo": "No se pudo conectar a: {id}.", "couldNotCreateCategory": "No se pudo crear la categoría: {name}", "couldNotCreateTextChannel": "No se pudo crear el canal de texto: {name}", "couldNotDeferInteraction": "No se pudo diferir la interacción.", "couldNotDeleteCategory": "No se pudo borrar la categoría: {categoryId}", "couldNotDeleteChannel": "No se pudo borrar el canal: {channelId}", "couldNotDeleteMessage": "No se pudo eliminar el mensaje: {message}", "couldNotFindAnyPlayers": "No se pudo encontrar a ningún jugador.", "couldNotFindCategory": "No se pudo encontrar la categoría: {category}", "couldNotFindChannel": "No se pudo encontrar el canal: {channel}", "couldNotFindCraftDetails": "Could not find craft details for {name}.", "couldNotFindDecayDetails": "Could not find decay details for {name}.", "couldNotFindDespawnDetails": "Could not find despawn details for {name}.", "couldNotFindGuild": "No se pudo encontrar el gremio: {guildId}", "couldNotFindLanguage": "No se pudo encontrar el idioma: {language}", "couldNotFindMessage": "No se pudo encontrar el mensaje {message}", "couldNotFindPlayer": "No se ha encontrado ningún jugador llamado {name}.", "couldNotFindPlayerId": "Could not find player with id {id}.", "couldNotFindRecycleDetails": "Could not find recycle details for {name}.", "couldNotFindResearchDetails": "Could not find research details for {name}.", "couldNotFindRole": "No se pudo encontrar el rol: {roleId}", "couldNotFindStackDetails": "Could not find stack details for {name}.", "couldNotFindTeammate": "No se pudo encontrar compañero: {name}.", "couldNotFindUpkeepDetails": "Could not find upkeep details for {name}.", "couldNotFindUser": "No se pudo encontrar el usuario: {userId}", "couldNotGetChannelWithId": "No se pudo obtener el canal con id: {id}.", "couldNotIdentifyMember": "No se pudo identificar al miembro del equipo: {name}.", "couldNotPerformBulkDelete": "No se pudo realizar la eliminación masiva en el canal: {channel}", "couldNotPerformMessageDelete": "No se pudo realizar la eliminación del mensaje.", "couldNotPerformMessagesFetch": "No se pudo realizar la búsqueda de mensajes en el canal: {channel}", "couldNotRegisterSlashCommands": "No se pudo registrar los comandos de barra para guild: {guildId}. ", "couldNotSetParent": "No se pudo establecer el canal principal: {channelId}", "craft": "Craft", "crate": "Caja", "createGroupCap": "CREAR GRUPO", "createTrackerCap": "CREAR RASTREADOR", "credentialsAddedSuccessfully": "¡Las Credenciales FCM fueron añadidas con éxito para SteamId: {steamId}!", "credentialsAlreadyRegistered": "¡Las Credenciales FCM para steamId: {steamId} ya están registradas!", "credentialsCannotStartLiteAlreadyHoster": "No se puede iniciar FCM Listener Lite para steamId: {steamId}. Ya hay un anfitrión.", "credentialsDoNotExist": "Credenciales FCM para steamId: {steamId} no existe.", "credentialsHosterNotSetForGuild": "Las Credenciales FCM del anfitrión no están establecidas para el gremio {id}, por favor, establece un anfitrión.", "credentialsNotRegistered": "Las Credenciales FCM para steamId: {steamId} no están registradas!", "credentialsNotRegisteredForGuild": "Las Credenciales FCM no están registradas para el gremio: {id}, no se puede iniciar FCM-listener.", "credentialsRemovedSuccessfully": "Las Credenciales FCM para steamId: {steamId} fueron eliminadas exitosamente!", "credentialsSetHosterSuccessfully": "Las Credenciales FCM para el anfitrión fueron exitosamente establecidas para steamId: {steamId}.", "currencySign": "<PERSON><PERSON><PERSON><PERSON>", "currentCommandDelay": "Retraso de comandos actual: {delay} segundos.", "currentItemHp": "The current HP of the item.", "currentPrefixPlaceholder": "Prefijo actual: {prefix}", "customCommand": "Comando Personalizado", "customTimerEditCargoShipEgressLabel": "Tiempo de salida del Cargo (segundos):", "customTimerEditCrateOilRigUnlockLabel": "Tiempo de desbloqueo de la caja (segundos):", "customTimerEditDesc": "Edición de Temporizadores Personalizados", "customTimersCap": "TEMPORIZADORES PERSONALIZADOS", "dash": "Dash", "dayOfWipe": "<PERSON><PERSON> {day}", "deathCap": "MUERTE", "decay": "Decay", "decayTimeForItem": "Decay time for {item} is {time}.", "decayingCap": "EN DETERIORO", "deleteUnreachableDevicesCap": "ELIMINAR DISPOSITIVOS INALCANZABLES", "despawnTime": "Despawn Time", "despawnTimeOfItem": "Despawn time of {item} is {time}.", "deviceIsAlreadyOnOff": "{device} is already {status}.", "deviceIsCurrentlyOnOff": "{device} está actualmente {status}.", "deviceWasTurnedOnOff": "{device} fue {status}.", "disabledCap": "DESHABILITADO", "discoFloor": "<PERSON><PERSON> de baile", "disconnectCap": "DESCONECTAR", "disconnected": "Disconnected", "disconnectedCap": "DESCONECTADO", "disconnectedFromServer": "DESCONECTADO DEL SERVIDOR.", "discordCap": "DISCORD", "discordUsers": "Usuarios de Discord", "displayInformationBattlemetricsAllOnlinePlayers": "Should all online players from Battlemetrics be displayed in the information channel?", "displayingMap": "Mostrando mapa de {mapName}.", "displayingOnlinePlayers": "Mostrando jugadores en línea.", "distanceDirectionGrid": "{distance}m en dirección {direction}° [{grid}].", "doorController": "Controlador de puerta", "dot": "Punt<PERSON>", "eastOfGrid": "Este del cuadrante", "editCap": "EDITAR", "editing": "<PERSON><PERSON><PERSON>", "editingOf": "Editando {entity}", "egressInTime": "Salida dentro de {time} en {location}.", "eight": "<PERSON><PERSON>", "elevator": "Ascensor", "empty": "Vacío", "enabledCap": "ACTIVADA", "entityId": "ID de Entidad", "equalsSign": "Signo igual", "errorCap": "ERROR", "errorExecutingCommand": "¡Hubo un error mientras se ejecutaba este comando!", "eventCap": "EVENTO", "eventInfo": "Información de evento", "exclamationMark": "Signo de exclamación", "failedToScrapeProfileName": "Error al consultar el perfil de usuario: {link}.", "failedToScrapeProfilePicture": "Error al consultar el avatar del perfil de usuario: {link}.", "fcmCredentials": "Credenciales FCM", "fcmListenerStartHost": "FCM-listener Host va a empezar en 5 segundos para guildId: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "FCM-listener Lite va a empezar en 5 segundos para guildId: {guildId}, steamId: {steamId}.", "ferryTerminal": "Terminal del Ferry", "fishingVillage": "Poblado pesquero", "five": "Cinco", "four": "Cuatro", "giantExcavatorPit": "Excavadora gigante", "greaterThanSign": "Símbolo mayor que", "groupAddSwitchDesc": "Añadir Interruptor a {group}", "groupRemoveSwitchDesc": "Eliminar interruptor de {group}", "harbor": "Puerto", "hasBeenAliveLongest": "{name} ha estado vivo más tiempo ({time}).", "hash": "Hash", "hbhfSensor": "Detector de calvos", "heart": "Corazón", "heater": "Cale<PERSON>ctor", "heavyScientistCalledSetting": "<PERSON>uando se llamen a los científicos pesados, envíe una notificación.", "heavyScientistsCalledLarge": "Los Científicos Pesados han sido llamados en la Petro Grande en {location}.", "heavyScientistsCalledSmall": "Los Científicos Pesados han sido llamados en la Petro Pequeña en {location}.", "hideTrademark": "Ocultar marca.", "hoster": "<PERSON><PERSON><PERSON><PERSON>", "hp": "HP", "hpExceedMax": "Hp {hp} is exceeding max of {max}.", "hqmQuarry": "Cantera de metal de alta calidad", "ignoreSetAvatar": "<PERSON><PERSON><PERSON><PERSON>", "ignoreSetNickname": "setNickname ignorado", "ignoreSetUsername": "setUsername ignorado", "inGameBotMessagesMuted": "Mensajes del bot del juego silenciados.", "inGameBotMessagesUnmuted": "Mensajes de bot en el juego no silenciados.", "inGameCap": "EN EL JUEGO", "inGameEventInfo": "Información de eventos en el juego", "inGameTeamNotificationsSetting": "Notificaciones de compañeros en el juego.", "inGameTime": "Tiempo en el juego: {time}.", "index": "Index", "infoCap": "INFO", "inside": "Inside", "interactionEditReplyFailed": "Error al editar la interacción: {error}", "interactionInvalidChannel": "Interacción desde un canal inválido.", "interactionReplyFailed": "Error al editar la interacción: {error}", "interactionUpdateFailed": "Error al actualizar la interacción: {error}", "invalidBattlemetricsId": "Invalid Battlemetrics ID.", "invalidGuildOrChannel": "Gremio o canal no válido.", "invalidHpInterval": "Intervalo de HP {hp} no válido.", "invalidId": "ID inválido: {id}.", "invalidStructureType": "Tipo de estructura {type} no válido.", "invalidSubcommand": "Subcomando inválido.", "invalidTimeDistance": "Distancia de tiempo no válida: {distance}, anterior: {prevTime}, nuevo: {newTime}", "isDecaying": "{device} está en deterioro!", "isNoLongerConnected": "¡{device} ya no está conectado eléctricamente!", "item": "<PERSON><PERSON>", "itemAvailableInVendingMachine": "{items} está disponible en una Máquina Expendedora en [{location}].", "itemAvailableNotifyInGameSetting": "Cuándo un item de la lista esté disponible en una Máquina Expendedora, ¿notificar en el juego?", "junkyard": "<PERSON><PERSON><PERSON><PERSON>", "justSubscribedToItem": "Acaba de suscribirse al item {name}.", "languageCode": "El código del idioma: {code}", "languageLangNotSupported": "El idioma {language} no es compatible.", "languageNotSupported": "El idioma no es compatible.", "largeBarn": "<PERSON><PERSON>", "largeFishingVillage": "Gran Poblado Pesquero", "largeOilRig": "Petro Grande", "largeWoodBox": "Caja Grande", "lastTrigger": "Última activación", "launchSite": "Estación de Lanzamiento de Misiles", "leaderAlreadyLeader": "{name} ya es el líder.", "leaderCommandIsDisabled": "El comando Leader está desactivado en la configuración.", "leaderCommandOnlyWorks": "El comando leader solo funciona si el anfitrión actual es {name}.", "leaderTransferred": "El liderazgo del equipo fue transferido a {name}.", "leavingMapAt": "Saliendo del mapa por {location}.", "lessThanSign": "<PERSON>o men<PERSON>-que", "lighthouse": "Faro", "linkCap": "ENLACE", "location": "Ubicación", "lockedCrateLargeOilRigUnlocked": "La Caja de la Petro Grande en {location} acaba de ser desbloqueada.", "lockedCrateOilRigUnlockedSetting": "Cuando una Caja Bloqueada en una Plataforma Petrolífera sea desbloqueada, envíe una notificación.", "lockedCrateSmallOilRigUnlocked": "La Caja Bloqueada en la Petro Pequeña en {location} ha sido desbloqueada.", "logDiscordCommand": "Co<PERSON><PERSON> - Servidor: {guild}, Canal: {channel}, Usuario: {user}, Mensaje: {message}.", "logDiscordMessage": "Mensaje <PERSON> - Servidor: {guild}, Canal: {channel}, Usuario: {user}, Mensaje: {message}.", "logInGameCommand": "{type} - <PERSON>mando: {command}, <PERSON>uario: {user}.", "logInGameMessage": "Mensaje: {message}, <PERSON><PERSON><PERSON>: {user}", "logSmartSwitchGroupValueChange": "Grupo de interruptores inteligentes - Valor: {value}.", "logSmartSwitchValueChange": "Interruptor inteligente - Valor: {value}.", "loggedInAs": "INICIADO SESIÓN COMO: {name}", "makeSureApplicationsCommandsEnabled": "Asegúrese de que applications.commands está marcado al crear la URL de invitación.", "map": "Mapa", "mapSalt": "La semilla/sal del mapa", "mapSeed": "Semilla del Mapa", "mapSize": "Tamaño del Mapa", "mapWipeDetectedNotifySetting": "<PERSON>uando se detecta el wipe del mapa, ¿debe notificarse a {group}?", "markerAdded": "El marcador {name} en [{location}] ha sido añadido.", "markerDoesNotExist": "El marcador {name} no existe.", "markerLocation": "El marcador {name} en [{location}] está a {distance}m de {player} hacia {direction}º.", "markerRemoved": "El marcador {name} en [{location}] ha sido eliminado.", "message": "Men<PERSON><PERSON>", "messageCap": "MESSAGE", "messageDeletedIn30": "El mensaje va a ser eliminado en 30 segundos.", "messageEditFailed": "Error al editar el mensaje: {error}", "messageReplyFailed": "Error en la respuesta del mensaje: {error}", "messageSendFailed": "Error al enviar el mensaje: {error}", "messageWasSent": "El mensaje ha sido enviado.", "militaryTunnel": "<PERSON><PERSON><PERSON>", "miningOutpost": "Puesto Minero", "missileSilo": "<PERSON><PERSON> mi<PERSON>", "missingArguments": "Faltan argumentos.", "missingPermission": "No tienes permiso para hacer esto.", "missingTimerMessage": "Falta el mensaje del temporizador.", "modalValueChange": "Interacción con modal - Id de verificación: {id}, Valor: {value}.", "more": "más", "morePlayers": "{players} ...{number} más.", "mutedCap": "SILENCIADO", "name": "Nombre", "nameChangeHistory": "Historial de cambios de nombres", "new": "New", "newVendingMachine": "Nueva Máquina expendedora ubicada en {location}.", "newsCap": "NOTICIAS", "noActiveTimers": "Temporizadores no activos.", "noCommandDelay": "No hay retardo de comandos.", "noCommunicationSmartSwitch": "No se pudo comunicar con el Interruptor Inteligente: {name}", "noData": "No hay datos.", "noDataOnLargeOilRig": "No hay datos actuales sobre la Petro Grande.", "noDataOnSmallOilRig": "No hay datos actuales sobre la Petro Pequeña.", "noDelayCap": "SIN DELAY", "noItemFound": "El item no ha sido encontrado en ninguna Máquina Expendedora...", "noItemWithIdFound": "No se ha encontrado ningún item con el id {id}.", "noItemWithNameFound": "No se ha encontrado ningún item con el nombre {name}.", "noNameIdGiven": "No se ha dado ningún 'nombre' o 'id'.", "noOneIsAfk": "Nadie está AFK.", "noOneIsOffline": "Nadie está desconectado.", "noOneIsOnline": "Nadie está conectado.", "noRegisteredConnectionEvents": "Aún no hay eventos de conexión registrados.", "noRegisteredConnectionEventsUser": "No hay eventos de conexión para {user}.", "noRegisteredDeathEvents": "No hay eventos de muerte aún.", "noRegisteredDeathEventsUser": "No hay eventos de muerte registrados para {user}.", "noRegisteredEvents": "No se han registrado eventos todavía.", "noRegisteredMarkers": "No hay marcadores registrados.", "noSavedNotes": "No hay notas guardadas.", "noToolCupboardWereFound": "No se han encontrado monitores del Armario de Herramientas.", "none": "Ninguna", "northEast": "Noreste", "northOfGrid": "Norte del cuadrante", "northWest": "Noroeste", "notAValidOrderType": "{order} no es un tipo de pedido válido.", "notActive": "Inactivo.", "notConnectedToRustServer": "Actualmente no estás conectado a un servidor de Rust.", "notExistInSubscription": "Item {name} no existe en la lista.", "notFoundCap": "NO ENCONTRADO", "notPartOfRole": "No eres parte del rol {role}, por lo tanto no puedes utilizar los comandos del bot.", "notShowingCap": "NO VISIBLE", "noteCap": "NOTA", "noteIdDoesNotExist": "ID de la Nota: {id} no existe.", "noteIdInvalid": "El ID de la nota no es válido.", "noteIdWasRemoved": "El ID de la nota: {id} ha sido eliminado.", "noteSaved": "Nota guardada.", "offCap": "APAGADO", "offline": "Offline", "offlineTime": "Offline time", "oilRig": "Petro", "old": "Old", "onCap": "ENCENDIDO", "one": "Uno", "online": "Online", "onlineTime": "Online time", "onlyOneInTeam": "<PERSON>res la única persona en el equipo.", "outpost": "<PERSON><PERSON><PERSON>", "outside": "Outside", "oxumsGasStation": "Gasolinera Oxum", "pairing": "vinculando", "patrolHelicopter": "Helicóptero de Combate", "patrolHelicopterDestroyedSetting": "Cuando el Helicóptero de Combate sea destruido, envía una notificación.", "patrolHelicopterDetectedSetting": "Cuando el Helicóptero de Combate sea detectado, envía una notificación.", "patrolHelicopterEntersMap": "El Helicóptero de Combate entra al mapa por {location}.", "patrolHelicopterLeftMap": "El Helicóptero de Combate acaba de salir por {location}.", "patrolHelicopterLeftSetting": "Cuando el Helicóptero de Combate deje el mapa, envía una notificación.", "patrolHelicopterLocatedAt": "El Helicóptero de Combate está en {location}.", "patrolHelicopterNotCurrentlyOnMap": "El Helicóptero de Combate no está en el mapa.", "patrolHelicopterTakenDown": "El Helicóptero de Combate ha sido destruido en {location}.", "percentSign": "Signo de porcentaje", "pipe": "Tubería", "playerHasBeenAliveFor": "{name} ha estado vivo por {time}.", "playerId": "Player ID", "playerJoinedTheTeam": "{name} ha entrado al equipo.", "playerJustConnected": "{name} acaba de conectarse.", "playerJustConnectedTo": "{name} acaba de conectarse a {server}.", "playerJustConnectedTracker": "El jugador {name} del rastreador {tracker} se acaba de conectar.", "playerJustDied": "{name} acaba de morir en {location}.", "playerJustDisconnected": "{name} acaba de desconectarse.", "playerJustDisconnectedFrom": "{name} acaba de desconectar de {server}.", "playerJustDisconnectedTracker": "El jugador {name} del rastreador {tracker} se acaba de desconectar.", "playerJustReturned": "{name} acaba de regresar ({time}).", "playerJustWentAfk": "{name} acaba de irse AFK.", "playerLeftTheTeam": "{name} ha abandonado el grupo.", "playerNotPairedWithServer": "El comando líder no funciona porque {name} no está emparejado con el servidor.", "players": "<PERSON><PERSON><PERSON>", "playersSearch": "Players Search", "plusSign": "Sign<PERSON> m<PERSON>", "populationPlayers": "Pop: ({current}/{max}) jugadores.", "populationQueue": "{number} jugadores en cola.", "powerPlant": "Central Nuclear", "profile": "Profile", "proxLocation": "{name} está a {distance}m de {caller} en dirección {direction}° [{location}]", "quantity": "Cantidad", "questionMark": "Signo de Interrogación", "ranch": "Rancho", "ratelimited": "LIMITADO", "reconnectingCap": "RECONECTANDO", "reconnectingToServer": "RECONECTANDO AL SERVIDOR...", "recycle": "Recycle", "recycleCap": "RECICLADORA", "recycler": "Recycler", "remain": "izquierda", "removePlayerCap": "ELIMINAR JUGADOR", "removeSwitchCap": "ELIMINAR INTERRUPTOR", "removedSubscribeItem": "<PERSON><PERSON><PERSON><PERSON> {name} han sido eliminados de la suscripción.", "research": "Research", "researchTable": "Research Table", "resetSuccess": "Se ha restablecido Discord con éxito.", "responseContainError": "La respuesta contiene la propiedad con error con valor: {error}.", "responseIsEmpty": "La respuesta está vacía.", "responseIsUndefined": "La respuesta no está definida.", "responseTimeout": "Se alcanzó el tiempo de espera mientras se esperaba una respuesta.", "resultRecycling": "Resultado del reciclaje", "roleCleared": "El rol para rustplusplus se ha quitado.", "roleSet": "El rol para rustplusplus se ha establecido a {name}.", "rustMonument": "Monumento de Rust", "rustplusOperational": "RUSTPLUS OPERATIVO.", "safe-zone-recycler": "Safe Zone Recycler", "samsite": "Torreta SAM", "satelliteDish": "Antena parabólica", "scrap": "Scrap", "searchResult": "Resultado de la búsqueda para el item: **{name}**", "second": "{second} segundos", "secondCommandDelay": "{second} segundos de retardo de comandos.", "seconds": "{seconds} segundos", "secondsCommandDelay": "{seconds} segundos de retardo de comandos.", "selectInGamePrefixSetting": "Seleccione qué prefijo de comandos dentro del juego se debe usar:", "selectLanguageExtendSetting": "Asegúrate de ejecutar **/reset discord** para cargar correctamente el nuevo idioma.", "selectLanguageSetting": "Seleccione qué idioma utiliza el bot:", "selectMenuValueChange": "Interacción con menú de selección - Id de verificación: {id}, Valor: {value}.", "selectTrademarkSetting": "Seleccione qué marca debe mostrarse en cada mensaje del juego.", "sell": "vender", "semicolon": "Punto y coma", "sentTextToSpeech": "Enviado el texto a voz.", "server": "servidor", "serverId": "Server ID", "serverInfo": "Información del Servidor", "serverInvalid": "La conexión con el servidor parece no ser válida. Intente volver a conectar con el servidor.", "serverJustOffline": "Servidor aca<PERSON> de desconectarse.", "serverJustOnline": "Servidor aca<PERSON> de conectarse.", "serverStatus": "Server Status", "serviceUnavailable": "Servicio no disponible: {error}", "setBotLanguage": "Establece el idioma del bot a: {language}.", "seven": "Siete", "sewerBranch": "<PERSON>", "shouldBotBeMutedSetting": "¿Debe silenciarse el bot dentro del juego?", "shouldCommandsEnabledSetting": "¿Deben estar habilitados los comandos dentro del juego?", "shouldLeaderCommandEnabledSetting": "¿Debe activar el comando de líder?", "shouldLeaderCommandOnlyForPairedSetting": "¿Debería el comando de líder funcionar sólo para las personas que están emparejadas con el servidor?", "shouldSmartAlarmNotifyNotConnectedSetting": "¿Debería notificar las Alarmas Inteligentes incluso si no están configuradas en el servidor de rust conectado?", "shouldSmartAlarmsNotifyInGameSetting": "¿Deben notificar las Alarmas Inteligentes en el juego?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Should Smart Switches and Smart Switch Groups notify In-Game when they are changed from discord?", "showingBlacklist": "Mostrando la lista negra.", "showingSubscriptionList": "Mostrando la lista.", "shredder": "Shredder", "sirenLight": "Luz de sirena", "six": "<PERSON><PERSON>", "slash": "Barr<PERSON>", "slashCommandInteraction": "Interacción con comando - Servidor: {guild}, Canal: {channel}, Usuario: {user}, Comando: {command}, Id de verificación: {id}.", "slashCommandValueChange": "Interacción con comando - Id de verificación: {id}, Valor: {value}.", "slashCommandsSuccessRegister": "Registrado con éxito los comandos de aplicación para el gremio: {guildId}.", "slots": "<PERSON><PERSON><PERSON><PERSON>", "smallOilRig": "Petro <PERSON>", "smartAlarm": "Alarma inteligente", "smartAlarmEditSuccess": "Editada con éxito la Alarma Inteligente {name}.", "smartAlarmNotifyExtendSetting": "- Estas notificaciones de Alarma usarán el título y el mensaje que se da a la Alarma Inteligente en el juego.\n- Estas Alarmas Inteligentes podrían no estar disponibles en el canal de texto de alarmas en discord.", "smartDeviceNotFound": "{device} no pudo ser encontrado! O ha sido destruido o {user} ha perdido el acceso al armario de herramientas.", "smartSwitch": "Interruptor Inteligente", "smartSwitchAutoDay": "El Interruptor Inteligente sólo estará activo durante el día.", "smartSwitchAutoNight": "El Interruptor Inteligente estará activo sólo durante la noche.", "smartSwitchAutoOff": "El Interruptor Inteligente se apagará durante el ciclo de actualización.", "smartSwitchAutoOffAnyOnline": "Smart Switch will automatically go inactive if any teammate is online.", "smartSwitchAutoOffProximity": "El Interruptor Inteligente se apagará si un miembro del equipo está cerca.", "smartSwitchAutoOn": "El Interruptor Inteligente se encenderá durante el ciclo de actualización.", "smartSwitchAutoOnAnyOnline": "Smart Switch will automatically go active if any teammate is online.", "smartSwitchAutoOnProximity": "El Interruptor Inteligente se encenderá si un miembro del equipo está cerca.", "smartSwitchEditProximityLabel": "Configuración de proximidad (metros):", "smartSwitchEditSuccess": "Se ha editado con éxito el Interruptor Inteligente {name}.", "smartSwitchNormal": "El Interruptor Inteligente funciona de manera normal.", "smilyFace": "<PERSON>", "somethingWrongWithConnection": "Algo ha ido mal con la conexión.", "southEast": "Sureste", "southOfGrid": "Sur del cuadrante", "southWest": "Suroeste", "sprinkler": "<PERSON><PERSON><PERSON>", "stackSize": "Stack Size", "stackSizeOfItem": "Stack size of {item} is {quantity}x.", "status": "Estado", "statusNotConnectedToServer": "**ESTADO** `NO CONECTADO AL SERVIDOR!`", "statusNotElectronicallyConnected": "**ESTADO** `NO CONECTADO ELÉCTRICAMENTE!`", "statusNotFound": "**ESTADO**: NO ENCONTRADO", "steamId": "SteamID", "stoneQuarry": "Cantera de Piedra", "storageMonitor": "Monitor de Almacenamiento", "storageMonitorEditSuccess": "Editado con éxito el Monitor de Almacenamiento {name}.", "streamerMode": "Streamer Mode", "subscribeToChangesBattlemetrics": "Subscribe to different changes on Battlemetrics.", "subscriptionList": "Lista de suscripciones", "subscriptionListEmpty": "La lista de suscripción está vacía.", "sulfurQuarry": "Cantera de Azufre", "switches": "Interruptores", "teamMember": "Miembro del Equipo", "teamMemberInfo": "Información del miembro del grupo", "theDome": "La Cúpula", "theIdOfTheItem": "The id of the item.", "theNameOfTheItem": "The name of the item.", "theNameOfThePlayer": "The name of the player.", "three": "Árbol", "tilde": "<PERSON><PERSON>", "time": "Tiempo", "timeBeforeCargoEntersEgress": "{time} hasta que el Cargo en {location} empiece a irse.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} antes de que la Caja Bloqueada de la Petro Grande ({location}) se desbloquee.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} antes de que la Caja Bloqueada en la Petro Pequeña ({location}) se desbloquee.", "timeCap": "TIEMPO", "timeFormatInvalid": "Formato de hora inválido.", "timeLeftTimer": "{id}: <PERSON><PERSON><PERSON> restante: {time}, <PERSON><PERSON><PERSON>: {message}", "timeSinceAlarmWasTriggered": "La alarma {alarm} fue activada hace {time}.", "timeSinceCargoLeft": "{time} desde que el Cargo salió del mapa.", "timeSinceChinook47OnMap": "{time} desde que el último Chinook estaba en el mapa.", "timeSinceHeavyScientistsOnLarge": "{time} desde que los Científicos Pesados fueron llamados por última vez en la Petro Grande.", "timeSinceHeavyScientistsOnSmall": "{time} desde que los Científicos Pesados fueron llamados por última vez en la Petro Pequeña.", "timeSinceLast": "{time} desde el último.", "timeSinceLastEvent": "{time} desde el ultimo evento.", "timeSinceLastSinceDestroyedLong": "{time1} since the last Patrol Helicopter was on the map, {time2} since it last got downed{location}.", "timeSinceLastSinceDestroyedShort": "{time1} since last.\n{time2} since destroyed{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} desde que el Helicóptero de Combate estaba en el mapa.", "timeSinceTravelingVendorWasOnMap": "{time} since the Traveling Vendor was on the map.", "timeSinceWipe": "{time} desde el <PERSON>.", "timeTill": "Tiempo para {event}", "timeTillDaylight": "{time} para el día.", "timeTillNightfall": "{time} para la noche.", "timeTillStructureDecay": "{time} antes de la caída de la pared {type}.", "timeUntilUnlocksAt": "{time} para que se desbloquee en {location}.", "timer": "Temporizador: {message}.", "timerIdDoesNotExist": "ID de temporizador: {id} no existe.", "timerIdInvalid": "ID de temporizador no es válido.", "timerRemoved": "ID de temporizador: {id} ha sido eliminado.", "timerSet": "Temporizador establecido para {time}.", "tokensDidNotReplenish": "Los Tokens no se repusieron a tiempo.", "toolCupboard": "Armario de herramientas", "total": "Total", "tracker": "Tracker", "trackerAddPlayerDesc": "<PERSON><PERSON><PERSON> juga<PERSON> a {tracker}", "trackerRemovePlayerDesc": "Eliminar jugador de {tracker}", "trademarkShownBeforeMessage": "{trademark} se mostrará antes de los mensajes.", "trainYard": "<PERSON><PERSON> ferroviario", "travelingVendor": "Traveling Vendor", "travelingVendorDetectedSetting": "When the Traveling Vendor is detected, send a notification.", "travelingVendorHaltedAt": "The Traveling Vendor stopped at {location}.", "travelingVendorHaltedSetting": "When the Traveling Vendor stops moving, send a notification.", "travelingVendorLeftSetting": "When the Traveling Vendor left the map, send a notification.", "travelingVendorLocatedAt": "The Traveling Vendor is located at {location}.", "travelingVendorLeftMap": "The Traveling Vendor just left the map at {location}.", "travelingVendorNotCurrentlyOnMap": "The Traveling Vendor is not currently on the map.", "travelingVendorResumedAt": "The Traveling Vendor resumed moving at {location}.", "travelingVendorSpawnedAt": "The Traveling Vendor spawned at {location}.", "turnOffCap": "APAGAR", "turnOnCap": "ENCENDER", "turningGroupOnOff": "Cambiando el grupo de interruptores {group} a {status}.", "two": "<PERSON><PERSON>", "type": "Tipo", "unavailable": "No disponible", "underscore": "<PERSON><PERSON><PERSON> bajo", "underwater": "Underwater", "underwaterLab": "Laboratorio submarino", "unhandledRejection": "Rechazo no controlado: {error}", "unknown": "Desconocido", "unknownInteraction": "Interacción desconocida...", "unmutedCap": "DESMUTEADO", "updateCap": "UPDATE", "upkeep": "Mantenimiento", "upkeepForItem": "Upkeep for {item} is {cost}.", "userAddedToBlacklist": "{user} fue añadido a la lista negra.", "userAlreadyInBlacklist": "{user} ya está en la lista negra.", "userButtonInteraction": "Interacción con botón - Servidor: {guild}, Canal: {channel}, Usuario: {user}, CustomId: {customid}, Id de verificación: {id}.", "userButtonInteractionSuccess": "Interacción con botón - Id de verificación: {id} CORRECTO", "userJustConnected": "{name} acaba de conectarse.", "userModalInteraction": "Interacción con modal - Servidor: {guild}, Canal: {channel}, Usuario: {user}, CustomId: {customid}, Id de verificación: {id}.", "userModalInteractionSuccess": "Interacción con modal - Id de verificación: {id} CORRECTO", "userNotInBlacklist": "{user} no está en la lista negra.", "userNotRegistered": "{user} no está registrado.", "userPartOfBlacklist": "Id de verificación: {id}, {user} es parte de la lista negra.", "userPartOfBlacklistDiscord": "¡Usuario en la lista negra! - Servidor: {guild}, Canal: {channel}, Usuario: {user}, Mensaje: {message}.", "userPartOfBlacklistInGame": "¡Usuario en la lista negra! Usuario: {user}, Mensaje: {message}.", "userRemovedFromBlacklist": "{user} fue eliminado de la lista negra.", "userSaid": "{user} dijo: {text}", "userSelectMenuInteraction": "Interacción con menú de selección - Servidor: {guild}, Canal: {channel}, Usuario: {user}, CustomId: {customid}, Id de verificación: {id}.", "userSelectMenuInteractionSuccess": "Interacción con menú de selección - Id de verificación: {id} CORRECTO", "userTurnedOnOffSmartSwitchFromDiscord": "{user} turned Smart Switch {name} {status} from discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} turned Smart Switch Group {name} {status} from discord.", "value": "Value", "vendingMachine": "Máquina Expendedora", "vendingMachineDetectedSetting": "<PERSON>uando se detecte una nueva máquina expendedora, envíe una notificación.", "voiceCap": "VOZ", "warningCap": "ADVERTENCIA", "waterTreatmentPlant": "Planta potabilizadora", "websiteCap": "PÁGINA WEB", "websocketClosedBeforeConnection": "El WebSocket se cerró antes de que se estableciera la conexión.", "westOfGrid": "Oeste del cuadrante", "wipe": "Wipe", "wipeDetected": "¡Wipe detectado!", "yield": "Yield", "youAreAlreadyLeader": "Ya eres líder.", "youAreNotPairedWithServer": "El comando líder no funciona porque no está emparejado con el servidor."}