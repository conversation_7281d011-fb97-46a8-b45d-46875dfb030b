{"24HoursInGameTimePassed": "Skutecznie minęły 24 godziny w grze.", "abandonedCabins": "Opuszczone domki", "abandonedMilitaryBase": "Opuszczona Baza Wojskowa", "abandonedSupermarket": "Opuszczony supermarket", "addPlayerCap": "DODAJ GRACZA", "addSwitchCap": "DODAJ PRZEŁĄCZNIK", "afkCap": "<PERSON><PERSON>z <PERSON>", "airfield": "<PERSON><PERSON><PERSON>", "alarmHaveNotBeenTriggeredYet": "Alarm {alarm} nie został jeszcze aktywowany.", "alias": "Pseudonim", "aliasAlreadyExist": "Pseudonim już jest w użyciu.", "aliasIndexCouldNotBeFound": "Pseudonim nie został znaleziony.", "aliasWasAdded": "Dodano pseudo<PERSON>z<PERSON>.", "aliasWasRemoved": "Usuni<PERSON><PERSON>.", "aliases": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allTeammatesAreDead": "Wszyscy twoi koledzy z drużyny nie żyją.", "alreadySubscribedToItem": "Subskrybowano już przedmiot {name}.", "ampersand": "Ampersand", "andMorePlayers": "... i {number} wi<PERSON><PERSON>j graczy.", "any": "Dowolny", "apostrophe": "Apostrophe", "arcticResearchBase": "Arktyczna baza badawcza", "asterisk": "Asterisk", "asteriskCctvDesc": "* <PERSON><PERSON><PERSON>, że potrzebujesz innego kodu numerycznego dla każdej mapy", "atLocation": "At {location}.", "atSign": "Na znaku", "autoDayCap": "AUTOMATYCZNIE DZIEŃ", "autoNightCap": "AUTO-NOC", "autoOffAnyOnlineCap": "WYŁĄCZ-WSZYSTKIE-ONLINE", "autoOffCap": "AUTOMATYCZNE-WYŁĄCZENIE", "autoOffProximityCap": "AUTOMATYCZNE-WYŁĄCZENIE-DYSTANSOWE", "autoOnAnyOnlineCap": "WŁĄCZ-WSZYSTKIE-ONLINE", "autoOnCap": "AUTOMATYCZNE-WŁĄCZENIE", "autoOnProximityCap": "AUOTMATYCZNE-WŁĄCZENIE-DYSTANSOWE", "autoSettingCap": "USTAWIENIA AUTOMATYCZNE: ", "automaticallyTurnBackOnOff": " Automatycznie przywrócono {status} w {time}.", "automaticallyTurningBackOnOff": "Automatycznie przywrócono {device} w {status}.", "autoturret": "Działko Automatyczne", "badGateway": "Błędna brama: {error}", "banditCamp": "Obóz Bandytów", "baseIsUnderAttack": "<PERSON>ja baza jest atakowana!", "battlemetricsApiRequestFailed": "Zapytyanie API do Battlemetrics nieudane: {api_call}.", "battlemetricsCap": "Battlemetrics", "battlemetricsFailedToUpdate": "Aktualizacja serwera Battlemetrics {server} zakończona niepowodzeniem.", "battlemetricsGlobalLoginCap": "LOGOWANIE GLOBALNE", "battlemetricsGlobalLogoutCap": "WYLOGOWANIE GLOBALNE", "battlemetricsGlobalNameChangesCap": "GLOBALNE ZMIANY NAZWY", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "Instacja Battlemetrics posiada brakujące id oraz nazwę.", "battlemetricsInstanceCouldNotBeFound": "Instancja Battlemetrics dla id {id} nie mogła zostać znaleziona.", "battlemetricsOnlinePlayers": "Gracze online Battlemetrics", "battlemetricsPlayersLogin": "Logowanie Battlemetrics", "battlemetricsPlayersLogout": "<PERSON><PERSON><PERSON><PERSON><PERSON> Battlemetrics", "battlemetricsPlayersNameChanged": "Battlemetrics gracz zmienił nazwę", "battlemetricsServerNameChanged": "Battlemetrics serwer zmienił nazwę", "battlemetricsServerNameChangesCap": "ZMIANA NAZWY SERWERA", "battlemetricsTrackerNameChangesCap": "ZMIANA NAZWY TRACKERA", "battlemetricsTrackerPlayerNameChanged": "Battlemetrics Tracker <PERSON><PERSON><PERSON> na<PERSON> gracza", "blacklist": "Czarna lista", "boomBox": "Boom Box", "bot": "bot", "broadcaster": "Broadcaster", "buttonValueChange": "Interakcja przyciskiem - VerifyId: {id}, Value: {value}.", "buy": "kup", "calculated": "Obliczone", "cargoAt": "W {location}.", "cargoLeavingMapAt": "Statek towarowy opuszcza mapę na {location}.", "cargoLocatedAt": "Statek towarowy znajduje się w {location}.", "cargoNotCurrentlyOnMap": "Statek towarowy nie jest obecnie na mapie.", "cargoShipDetectedSetting": "W przypadku wykrycia statku towarowego, wyślij powiadomienie.", "cargoShipDockingAtHarbor": "Cargo ship just docked at the Harbor at {location}", "cargoShipDockingAtHarborSetting": "When Cargo Ship is docked at a harbor, send a notification.", "cargoShipEgressSetting": "Gdy statek towarowy wchodzi na etap wyjścia, wyślij powiadomienie.", "cargoShipEntersEgressStage": "Cargo Ship should be in the egress stage at {location}.", "cargoShipEntersMap": "Cargo Ship enters the map from {location}.", "cargoShipLeftHarbor": "Statek towarowy właśnie opuścił port w {location}", "cargoShipLeftMap": "Cargo Ship just left the map at {location}.", "cargoShipLeftSetting": "When Cargo Ship left the map, send a notification.", "cargoShipLocated": "Cargo Ship is located at {location}.", "cargoship": "Cargoship", "ceilingLight": "Światło sufitowe", "channelNameActivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "channelNameAlarms": "alarmy", "channelNameCommands": "polecenia", "channelNameEvents": "Wyd<PERSON>zen<PERSON>", "channelNameInformation": "Informacje", "channelNameServers": "serwery", "channelNameSettings": "ustawienia", "channelNameStorageMonitors": "storageMonitors", "channelNameSwitchGroups": "Grupy przełączników", "channelNameSwitches": "przełączniki", "channelNameTeamchat": "c<PERSON>t d<PERSON>ż<PERSON>y", "channelNameTrackers": "trackers", "chinook47": "Chinook 47", "chinook47DetectedSetting": "Kiedy Chinok 47 wejdzie na mapę, wyślij powiadomienie.", "chinook47EntersMap": "Chinook 47 wchodzi na mapę z {location} aby <PERSON><PERSON><PERSON><PERSON> zablokowaną skrzynkę.", "chinook47LeftMap": "Chinook 47 opuścił mapę na {location}.", "chinook47Located": "Chinook 47 is located at {location}.", "chinook47NotOnMap": "Chinook 47 nie znajduje się obecnie na mapie.", "christmasLights": "Christmas Light", "circumflex": "Circumflex", "clanTag": "TAG Klanu", "codes": "<PERSON><PERSON>", "colon": "Colon", "comma": "Comma", "commandCap": "KOMENDY", "commandDelaySetting": "Powinien być opóźnienie polecenia? Jak długo?", "commandNotPossibleDiscord": "Polecenie nie jest możliwe przez DISCORD", "commandSyntaxAdd": "<PERSON><PERSON><PERSON>", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "alive", "commandSyntaxArmored": "opancerzony", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "połączenie", "commandSyntaxConnections": "połączenia", "commandSyntaxCraft": "stwórz", "commandSyntaxDeath": "death", "commandSyntaxDeaths": "deaths", "commandSyntaxDecay": "decay", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "wyd<PERSON><PERSON>ia", "commandSyntaxHeli": "heli", "commandSyntaxLanguage": "ję<PERSON>k", "commandSyntaxLarge": "large", "commandSyntaxLeader": "leader", "commandSyntaxList": "list", "commandSyntaxMarker": "marker", "commandSyntaxMarkers": "markers", "commandSyntaxMarket": "market", "commandSyntaxMetal": "metal", "commandSyntaxMute": "wycisz", "commandSyntaxNote": "note", "commandSyntaxNotes": "notes", "commandSyntaxOff": "wyłączony", "commandSyntaxOffline": "w trybie offline", "commandSyntaxOn": "włączony", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "gracz", "commandSyntaxPlayers": "<PERSON><PERSON><PERSON>", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "recycle", "commandSyntaxRemove": "usuń", "commandSyntaxResearch": "<PERSON><PERSON>e", "commandSyntaxSearch": "wyszukaj", "commandSyntaxSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandSyntaxSmall": "mały", "commandSyntaxStack": "stack", "commandSyntaxStatus": "status", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "kamień", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxTime": "czas", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendor", "commandSyntaxTwig": "<PERSON><PERSON><PERSON>", "commandSyntaxUnmute": "wyłącz wyciszenie", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "Utrzymanie", "commandSyntaxUptime": "utrzymanie", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "drewno", "commandsAlarmDesc": "Operacje na inteligentnych alarmach.", "commandsAlarmEditDesc": "Edit the properties of a Smart Alarm.", "commandsAlarmEditIdDesc": "The ID of the Smart Alarm.", "commandsAlarmEditImageDesc": "Set the image that best represent the Smart Alarm.", "commandsAliasAddAliasDesc": "Pseudonim do użycia.", "commandsAliasAddDesc": "<PERSON><PERSON><PERSON>.", "commandsAliasAddValueDesc": "Komenda/sekwencja znaków.", "commandsAliasDesc": "Stwórz pseudonim dla komendy/sekwencji znaków.", "commandsAliasRemoveDesc": "<PERSON><PERSON><PERSON> pseudonim.", "commandsAliasRemoveIndexDesc": "Numer pseudonimu do usunięcia.", "commandsAliasShowDesc": "Pokaż wszystkie zarejestrowane aliasy.", "commandsBlacklistAddDesc": "Dodaje użytkownika na czarną listę.", "commandsBlacklistDesc": "Zablokuj używanie bota przez użytkownika.", "commandsBlacklistDiscordUserDesc": "Użytkownik Discorda.", "commandsBlacklistRemoveDesc": "Remove user from the blacklist.", "commandsBlacklistShowDesc": "Show blacklisted users.", "commandsBlacklistSteamidDesc": "The steamid of the user.", "commandsCctvDesc": "Wyświetl kody CCTV dla monumentu", "commandsCraftDesc": "Display the cost to craft an item.", "commandsCraftQuantityDesc": "The quantity of items to craft.", "commandsCredentialsAddDesc": "Add FCM Credentials.", "commandsCredentialsDesc": "Set/Clear the FCM Credentials for the user account.", "commandsCredentialsRemoveDesc": "Remove FCM Credentials.", "commandsCredentialsRemoveSteamIdDesc": "SteamId of the FCM Credentials to remove.", "commandsCredentialsSetHosterDesc": "Set the hoster of FCM Credentials.", "commandsCredentialsSetHosterSteamIdDesc": "SteamId of the FCM Credentials hoster.", "commandsCredentialsShowDesc": "Show the currently registered FCM Credentials.", "commandsDecayDesc": "Display the decay time of an item.", "commandsDespawnDesc": "Display the despawn time of an item.", "commandsHelpCommandList": "Lista Poleceń", "commandsHelpDesc": "Display help message.", "commandsHelpHowToCredentials": "How-to Register Credentials", "commandsHelpHowToPairServer": "How-to <PERSON><PERSON> with <PERSON><PERSON>", "commandsItemDesc": "Get the details of an item.", "commandsLeaderDesc": "Give or take the leadership from/to a team member.", "commandsLeaderMemberDesc": "The name of the team member.", "commandsMapAllDesc": "Get the map including both monument names and markers.", "commandsMapCleanDesc": "Get the clean map.", "commandsMapDesc": "Get the currently connected server map image.", "commandsMapMarkersDesc": "Get the map including markers.", "commandsMapMonumentsDesc": "Get the map including monument names.", "commandsMarketDesc": "Operations for In-Game Vending Machines.", "commandsMarketListDesc": "Display the subscription list.", "commandsMarketOrderDesc": "The order type.", "commandsMarketSearchDesc": "Search for an item in Vending Machines.", "commandsMarketSubscribeDesc": "Subscribe to an item in Vending Machines.", "commandsMarketUnsubscribeDesc": "Unsubscribe from an item in Vending Machines.", "commandsPlayersBattlemetricsIdDesc": "The Battlemetrics ID of the server (default: The connected server).", "commandsPlayersDesc": "Get player/players information based on Battlemetrics.", "commandsPlayersNameDesc": "Search for a player on Battlemetrics based on player name.", "commandsPlayersPlayerIdDesc": "Search for a player on Battlemetrics based on player id.", "commandsPlayersPlayerIdPlayerIdDesc": "The player id of the player.", "commandsPlayersStatusDesc": "Search for players that are online/offline/any.", "commandsRecycleDesc": "Display the output of recycling an item.", "commandsRecycleQuantityDesc": "The quantity of items to recycle.", "commandsRecycleRecyclerTypeDesc": "The recycler type (recycler, shredder, safe-zone-recycler).", "commandsResearchDesc": "Display the cost to research an item.", "commandsResetAlarmsDesc": "Reset alarms channel.", "commandsResetDesc": "Reset Discord channels.", "commandsResetInformationDesc": "Reset information channel.", "commandsResetServersDesc": "Reset servers channel.", "commandsResetSettingsDesc": "Reset settings channel.", "commandsResetStorageMonitorsDesc": "Reset storage monitors channel.", "commandsResetSwitchesDesc": "Reset switches and switchGroups channels.", "commandsResetTrackersDesc": "Reset trackers channel.", "commandsRoleClearDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rolę (aby umożliwić wszystkim widzenie kanałów rustplusplu).", "commandsRoleDesc": "Set/Clear a specific role that will be able to see the rustplusplus category content.", "commandsRoleSetDesc": "Set the role.", "commandsRoleSetRoleDesc": "The role rustplusplus channels will be visible to.", "commandsStackDesc": "Display the stack size of an item.", "commandsStoragemonitorDesc": "Operations on Storage Monitors.", "commandsStoragemonitorEditDesc": "Edit the properties of a Storage Monitor.", "commandsStoragemonitorEditIdDesc": "The ID of the Storage Monitor.", "commandsStoragemonitorEditImageDesc": "Set the image that best represent the Storage Monitor.", "commandsSwitchDesc": "Operations on Smart Switches.", "commandsSwitchEditDesc": "Edit the properties of a Smart Switch.", "commandsSwitchEditIdDesc": "The ID of the Smart Switch.", "commandsSwitchEditImageDesc": "Set the image that best represent the Smart Switch.", "commandsUpkeepDesc": "Display the upkeep cost of an item.", "commandsUptimeBotDesc": "Wyś<PERSON><PERSON><PERSON> czas pracy bota.", "commandsUptimeDesc": "Display uptime of the bot and server.", "commandsUptimeServerDesc": "Display uptime of server.", "commandsVoiceBotJoinedVoice": "Bot przyłączył się do kanału głosowego.", "commandsVoiceBotLeftVoice": "The <PERSON><PERSON> has left the Voicechannel", "commandsVoiceDesc": "Bot Voice Commands", "commandsVoiceFemale": "Female", "commandsVoiceFemaleDescription": "Sets the voiceactor gender to Female", "commandsVoiceGenderDesc": "Sets the <PERSON><PERSON>'s voiceactor gender.", "commandsVoiceJoin": "Joining voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceJoinDesc": "Joins the Voicechannel", "commandsVoiceLeave": "Leaving voice channel {name} with the ID {id} in guild {guild}", "commandsVoiceLeaveDesc": "Leaves the Voicechannel", "commandsVoiceMale": "Male", "commandsVoiceMaleDescription": "Sets the voiceactor gender to Male", "commandsVoiceNotInVoice": "You are not in a voicechannel", "connect": "Connect", "connectCap": "CONNECT", "connected": "Connected", "connectedCap": "CONNECTED", "connectedToServer": "CONNECTED TO SERVER.", "connectingCap": "CONNECTING", "connectingToServer": "CONNECTING TO SERVER...", "connectionEvents": "Connection Events", "connectionRefusedTo": "Connection refused to: {id}.", "connectionsCap": "CONNECTIONS", "couldNotAddStepTracers": "Could not add step tracers.", "couldNotAppendMapMarkers": "Could not append map markers, rustplus info instance is not set.", "couldNotAppendMapMonuments": "Could not append map monuments, rustplus info instance is not set.", "couldNotAppendMapTracers": "Could not append map tracers, rustplus info instance is not set.", "couldNotConnectTo": "Could not connect to: {id}.", "couldNotCreateCategory": "Could not create category: {name}", "couldNotCreateTextChannel": "Could not create text channel: {name}", "couldNotDeferInteraction": "Could not defer interaction.", "couldNotDeleteCategory": "Could not delete category: {categoryId}", "couldNotDeleteChannel": "Could not delete channel: {channelId}", "couldNotDeleteMessage": "Could not delete message: {message}", "couldNotFindAnyPlayers": "Could not find any players.", "couldNotFindCategory": "Could not find category: {category}", "couldNotFindChannel": "Could not find channel: {channel}", "couldNotFindCraftDetails": "Could not find craft details for {name}.", "couldNotFindDecayDetails": "Could not find decay details for {name}.", "couldNotFindDespawnDetails": "Could not find despawn details for {name}.", "couldNotFindGuild": "Could not find guild: {guildId}", "couldNotFindLanguage": "Could not find language: {language}", "couldNotFindMessage": "Could not find message {message}", "couldNotFindPlayer": "Could not find a player {name}.", "couldNotFindPlayerId": "Could not find player with id {id}.", "couldNotFindRecycleDetails": "Could not find recycle details for {name}.", "couldNotFindResearchDetails": "Could not find research details for {name}.", "couldNotFindRole": "Could not find role: {roleId}", "couldNotFindStackDetails": "<PERSON>e można znaleźć szczegółów stosu dla {name}.", "couldNotFindTeammate": "Could not find teammate: {name}.", "couldNotFindUpkeepDetails": "Could not find upkeep details for {name}.", "couldNotFindUser": "Could not find user: {userId}", "couldNotGetChannelWithId": "Could not get channel with id: {id}.", "couldNotIdentifyMember": "Could not identify team member: {name}.", "couldNotPerformBulkDelete": "Could not perform bulkDelete on channel: {channel}", "couldNotPerformMessageDelete": "Could not perform message delete.", "couldNotPerformMessagesFetch": "Could not perform messages fetch on channel: {channel}", "couldNotRegisterSlashCommands": "Could not register Slash Commands for guild: {guildId}. ", "couldNotSetParent": "Could not set parent for channel: {channelId}", "craft": "Stwórz", "crate": "Crate", "createGroupCap": "CREATE GROUP", "createTrackerCap": "CREATE TRACKER", "credentialsAddedSuccessfully": "FCM Credentials were added successfully for steamId: {steamId}!", "credentialsAlreadyRegistered": "FCM Credentials for steamId: {steamId} are already registered!", "credentialsCannotStartLiteAlreadyHoster": "Cannot start FCM Listener Lite for steamId: {steamId}. Already hoster.", "credentialsDoNotExist": "FCM Credentials for steamId: {steamId} does not exist.", "credentialsHosterNotSetForGuild": "FCM Credentials hoster is not set for guild {id}, please set a hoster.", "credentialsNotRegistered": "FCM Credentials for steamId: {steamId} is not registered!", "credentialsNotRegisteredForGuild": "FCM Credentials are not registered for guild: {id}, cannot start FCM-listener.", "credentialsRemovedSuccessfully": "FCM Credentials for steamId: {steamId} was removed successfully!", "credentialsSetHosterSuccessfully": "FCM Credentials hoster was successfully set to steamId: {steamId}.", "currencySign": "Currency Sign", "currentCommandDelay": "Current Command Delay: {delay} seconds.", "currentItemHp": "The current HP of the item.", "currentPrefixPlaceholder": "Current Prefix: {prefix}", "customCommand": "Custom Command", "customTimerEditCargoShipEgressLabel": "Cargoship egress time (seconds):", "customTimerEditCrateOilRigUnlockLabel": "OilRig Locked Crate unlock time (seconds):", "customTimerEditDesc": "Editing of Custom Timers", "customTimersCap": "CUSTOM TIMERS", "dash": "Dash", "dayOfWipe": "Day {day}", "deathCap": "DEATH", "decay": "Decay", "decayTimeForItem": "Decay time for {item} is {time}.", "decayingCap": "DECAYING", "deleteUnreachableDevicesCap": "DELETE UNREACHABLE DEVICES", "despawnTime": "Despawn Time", "despawnTimeOfItem": "Despawn time of {item} is {time}.", "deviceIsAlreadyOnOff": "{device} is already {status}.", "deviceIsCurrentlyOnOff": "{device} is currently {status}.", "deviceWasTurnedOnOff": "{device} was turned {status}.", "disabledCap": "DISABLED", "discoFloor": "Discofloor", "disconnectCap": "DISCONNECT", "disconnected": "Disconnected", "disconnectedCap": "DISCONNECTED", "disconnectedFromServer": "DISCONNECTED FROM SERVER.", "discordCap": "DISCORD", "discordUsers": "Discord Users", "displayInformationBattlemetricsAllOnlinePlayers": "Should all online players from Battlemetrics be displayed in the information channel?", "displayingMap": "Displaying {mapName} map.", "displayingOnlinePlayers": "Displaying online players.", "distanceDirectionGrid": "{distance}m in direction {direction}° [{grid}].", "doorController": "Door Controller", "dot": "Dot", "eastOfGrid": "East of grid", "editCap": "EDIT", "editing": "Editing", "editingOf": "Editing of {entity}", "egressInTime": "Egress in {time} at {location}.", "eight": "Eight", "elevator": "Elevator", "empty": "Empty", "enabledCap": "ENABLED", "entityId": "Entity ID", "equalsSign": "Equals Sign", "errorCap": "ERROR", "errorExecutingCommand": "There was an error while executing this command!", "eventCap": "WYDARZENIE", "eventInfo": "Informacje o wydarzeniu", "exclamationMark": "Exclamation Mark", "failedToScrapeProfileName": "Failed to scrape profile name: {link}.", "failedToScrapeProfilePicture": "Failed to scrape profile picture: {link}.", "fcmCredentials": "Dane logowania FCM", "fcmListenerStartHost": "FCM-listener Host will start in 5 seconds for guildId: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "FCM-listener Li<PERSON> will start in 5 seconds for guildId: {guildId}, steamId: {steamId}.", "ferryTerminal": "Ferry Terminal", "fishingVillage": "Wioska rybacka", "five": "<PERSON><PERSON><PERSON>", "four": "Cztery", "giantExcavatorPit": "Giant Excavator Pit", "greaterThanSign": "Greater-than Sign", "groupAddSwitchDesc": "Dodaj przełącznik do {group}", "groupRemoveSwitchDesc": "Re<PERSON>ve <PERSON> from {group}", "harbor": "Port", "hasBeenAliveLongest": "{name} has been alive the longest ({time}).", "hash": "Hash", "hbhfSensor": "HBHF Sensor", "heart": "Heart", "heater": "Heater", "heavyScientistCalledSetting": "When Heavy Scientists are called to Oil Rig, send a notification.", "heavyScientistsCalledLarge": "Heavy Scientists got called to the Large Oil Rig at {location}.", "heavyScientistsCalledSmall": "Ciężcy naukowcy zostali wezwani na małej Platformie wiertniczej {location}.", "hideTrademark": "Ukryj znak handlowy.", "hoster": "Hoster", "hp": "HP", "hpExceedMax": "Hp {hp} is exceeding max of {max}.", "hqmQuarry": "HQM Quarry", "ignoreSetAvatar": "Ignored set<PERSON><PERSON>ar", "ignoreSetNickname": "Ignored setNickname", "ignoreSetUsername": "Ignored setUsername", "inGameBotMessagesMuted": "In-Game bot messages muted.", "inGameBotMessagesUnmuted": "In-Game bot messages unmuted.", "inGameCap": "IN-GAME", "inGameEventInfo": "In-game event information", "inGameTeamNotificationsSetting": "In-Game teammate notifications.", "inGameTime": "In-Game time: {time}.", "index": "Index", "infoCap": "INFO", "inside": "Inside", "interactionEditReplyFailed": "Interaction edit reply failed: {error}", "interactionInvalidChannel": "Interaction from an invalid channel.", "interactionReplyFailed": "Interaction reply failed: {error}", "interactionUpdateFailed": "Interaction update failed: {error}", "invalidBattlemetricsId": "Invalid Battlemetrics ID.", "invalidGuildOrChannel": "Invalid guild or channel.", "invalidHpInterval": "Invalid HP interval {hp}.", "invalidId": "Invalid ID: {id}.", "invalidStructureType": "Invalid Structure type {type}.", "invalidSubcommand": "Invalid subcommand.", "invalidTimeDistance": "Invalid time distance: {distance}, prev: {prevTime}, new: {newTime}", "isDecaying": "{device} is decaying!", "isNoLongerConnected": "{device} is no longer electrically connected!", "item": "<PERSON><PERSON>", "itemAvailableInVendingMachine": "{items} just became available in a Vending Machine at [{location}].", "itemAvailableNotifyInGameSetting": "When an item from the subscription list becomes available in a Vending Machine, notify In-Game?", "junkyard": "Junkyard", "justSubscribedToItem": "Just subscribed to item {name}.", "languageCode": "Language code: {code}", "languageLangNotSupported": "The language {language} is not supported.", "languageNotSupported": "The language is not supported.", "largeBarn": "Large Barn", "largeFishingVillage": "Large Fishing Village", "largeOilRig": "Large Oil Rig", "largeWoodBox": "Large Wood Box", "lastTrigger": "Last Trigger", "launchSite": "Launch Site", "leaderAlreadyLeader": "{name} is already team leader.", "leaderCommandIsDisabled": "Leader command is disabled in settings.", "leaderCommandOnlyWorks": "Leader command only works if the current leader is {name}.", "leaderTransferred": "Team leadership was transferred to {name}.", "leavingMapAt": "Leaving at {location}.", "lessThanSign": "Less-than Sign", "lighthouse": "Lighthouse", "linkCap": "LINK", "location": "Location", "lockedCrateLargeOilRigUnlocked": "Locked Crate at Large Oil Rig at {location} has been unlocked.", "lockedCrateOilRigUnlockedSetting": "When a Locked Crate at Oil Rig is unlocked, send a notification.", "lockedCrateSmallOilRigUnlocked": "Locked Crate at Small Oil Rig at {location} has been unlocked.", "logDiscordCommand": "Discord Command - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logDiscordMessage": "Discord Message - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logInGameCommand": "{type} - Command: {command}, User: {user}.", "logInGameMessage": "Message: {message}, User: {user}", "logSmartSwitchGroupValueChange": "Smart Switch Group - Value: {value}.", "logSmartSwitchValueChange": "Smart Switch - Value: {value}.", "loggedInAs": "LOGGED IN AS: {name}", "makeSureApplicationsCommandsEnabled": "Make sure applications.commands is checked when creating the invite URL.", "map": "Map", "mapSalt": "Map Salt", "mapSeed": "Map Seed", "mapSize": "Map Size", "mapWipeDetectedNotifySetting": "When Map Wipe is detected, should {group} be notified?", "markerAdded": "Marker {name} at [{location}] was added.", "markerDoesNotExist": "Marker {name} does not exist.", "markerLocation": "Marker {name} at [{location}] is {distance}m from {player} in direction {direction}°.", "markerRemoved": "Marker {name} at [{location}] was removed.", "message": "Message", "messageCap": "MESSAGE", "messageDeletedIn30": "This message will be deleted in 30 seconds.", "messageEditFailed": "Message edit failed: {error}", "messageReplyFailed": "Message reply failed: {error}", "messageSendFailed": "Message send failed: {error}", "messageWasSent": "Message was sent.", "militaryTunnel": "Military Tunnel", "miningOutpost": "Mining Outpost", "missileSilo": "Missile Silo", "missingArguments": "Missing arguments.", "missingPermission": "You don't have permission to do this.", "missingTimerMessage": "Missing timer message.", "modalValueChange": "Modal Interaction - VerifyId: {id}, Value: {value}.", "more": "more", "morePlayers": "{players} ...{number} more.", "mutedCap": "MUTED", "name": "Name", "nameChangeHistory": "Name Change History", "new": "New", "newVendingMachine": "New Vending Machine located at {location}.", "newsCap": "NEWS", "noActiveTimers": "No active timers.", "noCommandDelay": "No command delay.", "noCommunicationSmartSwitch": "Could not communicate with Smart Switch: {name}", "noData": "No data.", "noDataOnLargeOilRig": "No current data on Large Oil Rig.", "noDataOnSmallOilRig": "No current data on Small Oil Rig.", "noDelayCap": "NO DELAY", "noItemFound": "Item could not be found in any Vending Machines...", "noItemWithIdFound": "No item with id {id} could be found.", "noItemWithNameFound": "No item with name {name} could be found.", "noNameIdGiven": "No 'name' or 'id' was given.", "noOneIsAfk": "No one is AFK.", "noOneIsOffline": "No one is offline.", "noOneIsOnline": "No one is online.", "noRegisteredConnectionEvents": "No registered connection events yet.", "noRegisteredConnectionEventsUser": "No registered connection events for {user}.", "noRegisteredDeathEvents": "No registered death events yet.", "noRegisteredDeathEventsUser": "No registered death events for {user}.", "noRegisteredEvents": "No registered events yet.", "noRegisteredMarkers": "No registered markers.", "noSavedNotes": "There are no saved notes.", "noToolCupboardWereFound": "No Tool Cupboard monitors were found.", "none": "None", "northEast": "North East", "northOfGrid": "North of grid", "northWest": "North West", "notAValidOrderType": "{order} is not a valid order type.", "notActive": "Not active.", "notConnectedToRustServer": "Not currently connected to a rust server.", "notExistInSubscription": "Item {name} does not exist in subscription list.", "notFoundCap": "NOT FOUND", "notPartOfRole": "You are not part of the {role} role, therefore you can't run bot commands.", "notShowingCap": "NOT SHOWING", "noteCap": "NOTE", "noteIdDoesNotExist": "Note ID: {id} does not exist.", "noteIdInvalid": "Note ID is invalid.", "noteIdWasRemoved": "Note ID: {id} was removed.", "noteSaved": "Note saved.", "offCap": "OFF", "offline": "Offline", "offlineTime": "Offline time", "oilRig": "Oil Rig", "old": "Old", "onCap": "ON", "one": "One", "online": "Online", "onlineTime": "Online time", "onlyOneInTeam": "You are the only one in the team.", "outpost": "Outpost", "outside": "Outside", "oxumsGasStation": "Oxum's Gas Station", "pairing": "pairing", "patrolHelicopter": "Patrol Helicopter", "patrolHelicopterDestroyedSetting": "When Patrol Helicopter gets destroyed, send a notification.", "patrolHelicopterDetectedSetting": "When Patrol Helicopter is detected, send a notification.", "patrolHelicopterEntersMap": "Patrol Helicopter enters the map from {location}.", "patrolHelicopterLeftMap": "Patrol Helicopter just left the map at {location}.", "patrolHelicopterLeftSetting": "When <PERSON> Helic<PERSON>ter left the map, send a notification.", "patrolHelicopterLocatedAt": "Patrol Helicopter is located at {location}.", "patrolHelicopterNotCurrentlyOnMap": "Patrol Helicopter is not currently on the map.", "patrolHelicopterTakenDown": "Patrol Helicopter was taken down {location}.", "percentSign": "Percent Sign", "pipe": "<PERSON><PERSON>", "playerHasBeenAliveFor": "{name} has been alive for {time}.", "playerId": "Player ID", "playerJoinedTheTeam": "{name} joined the team.", "playerJustConnected": "{name} just connected.", "playerJustConnectedTo": "{name} just connected to {server}.", "playerJustConnectedTracker": "{name} just connected from tracker {tracker}.", "playerJustDied": "{name} just died at {location}.", "playerJustDisconnected": "{name} just disconnected.", "playerJustDisconnectedFrom": "{name} just disconnected from {server}.", "playerJustDisconnectedTracker": "{name} just disconnected from tracker {tracker}.", "playerJustReturned": "{name} just returned ({time}).", "playerJustWentAfk": "{name} just went AFK.", "playerLeftTheTeam": "{name} left the team.", "playerNotPairedWithServer": "Leader command does not work because {name} is not paired with the server.", "players": "Players", "playersSearch": "Players Search", "plusSign": "Plus Sign", "populationPlayers": "Population: ({current}/{max}) players.", "populationQueue": "{number} players in queue.", "powerPlant": "Power Plant", "profile": "Profile", "proxLocation": "{name} is {distance}m from {caller} in direction {direction}° [{location}]", "quantity": "Quantity", "questionMark": "Question Mark", "ranch": "Ranch", "ratelimited": "RATELIMITED", "reconnectingCap": "RECONNECTING", "reconnectingToServer": "RECONNECTING TO SERVER...", "recycle": "Recycle", "recycleCap": "RECYCLE", "recycler": "Recycler", "remain": "left", "removePlayerCap": "REMOVE PLAYER", "removeSwitchCap": "REMOVE SWITCH", "removedSubscribeItem": "Item {name} have been removed from subscription.", "research": "Research", "researchTable": "Stół do badań", "resetSuccess": "Successfully reset Discord.", "responseContainError": "Response contain error property with value: {error}.", "responseIsEmpty": "Response is empty.", "responseIsUndefined": "Response is undefined.", "responseTimeout": "Timeout reached while waiting for response.", "resultRecycling": "Result of recycling", "roleCleared": "rustplusplus role has been cleared.", "roleSet": "rustplusplus role has been set to {name}.", "rustMonument": "Monument Rust", "rustplusOperational": "RUSTPLUS OPERATIONAL.", "safe-zone-recycler": "Safe Zone Recycler", "samsite": "SAM site", "satelliteDish": "Satellite Dish", "scrap": "Scrap", "searchResult": "Search result for item: **{name}**", "second": "{second} second", "secondCommandDelay": "{second} second command delay.", "seconds": "{seconds} seconds", "secondsCommandDelay": "{seconds} seconds command delay.", "selectInGamePrefixSetting": "Select what in-game command prefix that should be used:", "selectLanguageExtendSetting": "Make sure you run **/reset discord** to successfully load the new language.", "selectLanguageSetting": "Select what language the bot uses:", "selectMenuValueChange": "Select Menu Interaction - VerifyId: {id}, Value: {value}.", "selectTrademarkSetting": "Select which trademark that should be shown in every in-game message.", "sell": "sell", "semicolon": "Semicolon", "sentTextToSpeech": "Sent the Text-To-Speech.", "server": "server", "serverId": "Server ID", "serverInfo": "Server Information", "serverInvalid": "The connection to the server seems to be invalid. Try to re-pair to the server.", "serverJustOffline": "Server just went offline.", "serverJustOnline": "Server just went online.", "serverStatus": "Server Status", "serviceUnavailable": "Service Unavailable: {error}", "setBotLanguage": "Set the bot language to: {language}.", "seven": "Seven", "sewerBranch": "Sewer Branch", "shouldBotBeMutedSetting": "Should the bot be muted in-game?", "shouldCommandsEnabledSetting": "Should in-game commands be enabled?", "shouldLeaderCommandEnabledSetting": "Should the leader command be enabled?", "shouldLeaderCommandOnlyForPairedSetting": "Should the leader command only work for people that are paired with the server?", "shouldSmartAlarmNotifyNotConnectedSetting": "Should Smart Alarms notify even if they are not setup on the connected rust server?", "shouldSmartAlarmsNotifyInGameSetting": "Should Smart Alarms notify In-Game?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Should Smart Switches and Smart Switch Groups notify In-Game when they are changed from discord?", "showingBlacklist": "Showing the blacklist.", "showingSubscriptionList": "Showing the subscription list.", "shredder": "Shredder", "sirenLight": "Siren Light", "six": "Six", "slash": "Slash", "slashCommandInteraction": "Slash Command Interaction - Guild: {guild}, Channel: {channel}, User: {user}, Command: {command}, VerifyId: {id}.", "slashCommandValueChange": "Slash Command Interaction - VerifyId: {id}, Value: {value}.", "slashCommandsSuccessRegister": "Successfully registered application commands for guild: {guildId}.", "slots": "Slots", "smallOilRig": "Small Oil Rig", "smartAlarm": "Smart Alarm", "smartAlarmEditSuccess": "Successfully edited Smart Alarm {name}.", "smartAlarmNotifyExtendSetting": "- These Alarm notifications will use the title and message given to the Smart Alarm in-game.\n- These Smart Alarms might not be available in the alarms text channel in discord.", "smartDeviceNotFound": "{device} could not be found! Either it have been destroyed or {user} have lost tool cupboard access.", "smartSwitch": "Smart Switch", "smartSwitchAutoDay": "Smart Switch will be active only during the day.", "smartSwitchAutoNight": "Smart Switch will be active only during the night.", "smartSwitchAutoOff": "Smart Switch will automatically go inactive during update cycle.", "smartSwitchAutoOffAnyOnline": "Smart Switch will automatically go inactive if any teammate is online.", "smartSwitchAutoOffProximity": "Smart Switch will automatically go inactive if teammate is in proximity.", "smartSwitchAutoOn": "Smart Switch will automatically go active during update cycle.", "smartSwitchAutoOnAnyOnline": "Smart Switch will automatically go active if any teammate is online.", "smartSwitchAutoOnProximity": "Smart Switch will automatically go active if teammate is in proximity.", "smartSwitchEditProximityLabel": "Proximity Setting (meters):", "smartSwitchEditSuccess": "Successfully edited Smart Switch {name}.", "smartSwitchNormal": "Smart Switch work as normal.", "smilyFace": "<PERSON><PERSON><PERSON>", "somethingWrongWithConnection": "Something went wrong with connection.", "southEast": "South East", "southOfGrid": "South of grid", "southWest": "South West", "sprinkler": "Sprinkler", "stackSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> stosu", "stackSizeOfItem": "Stack size of {item} is {quantity}x.", "status": "Status", "statusNotConnectedToServer": "**STATUS** `NOT CONNECTED TO SERVER!`", "statusNotElectronicallyConnected": "**STATUS** `NOT ELECTRICALLY CONNECTED!`", "statusNotFound": "**STATUS**: NOT FOUND", "steamId": "SteamID", "stoneQuarry": "Stone Quarry", "storageMonitor": "Storage Monitor", "storageMonitorEditSuccess": "Successfully edited Storage Monitor {name}.", "streamerMode": "Streamer Mode", "subscribeToChangesBattlemetrics": "Subscribe to different changes on Battlemetrics.", "subscriptionList": "Subscription list", "subscriptionListEmpty": "Item subscription list is empty.", "sulfurQuarry": "Sul<PERSON>r Quarry", "switches": "Switches", "teamMember": "Team Member", "teamMemberInfo": "Team Member Information", "theDome": "The Dome", "theIdOfTheItem": "The id of the item.", "theNameOfTheItem": "The name of the item.", "theNameOfThePlayer": "The name of the player.", "three": "Three", "tilde": "<PERSON><PERSON>", "time": "Time", "timeBeforeCargoEntersEgress": "{time} before Cargo Ship at {location} enters egress stage.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} before Locked Crate at Large Oil Rig ({location}) unlocks.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} before Locked Crate at Small Oil Rig ({location}) unlocks.", "timeCap": "TIME", "timeFormatInvalid": "Time format invalid.", "timeLeftTimer": "{id}: Time left: {time}, Message: {message}", "timeSinceAlarmWasTriggered": "The alarm {alarm} was triggered {time} ago.", "timeSinceCargoLeft": "{time} since Cargo Ship left the map.", "timeSinceChinook47OnMap": "{time} since the last Chinook 47 was on the map.", "timeSinceHeavyScientistsOnLarge": "{time} since Heavy Scientists last got called to Large Oil Rig.", "timeSinceHeavyScientistsOnSmall": "{time} since Heavy Scientists last got called to Small Oil Rig.", "timeSinceLast": "{time} since last.", "timeSinceLastEvent": "{time} since last event.", "timeSinceLastSinceDestroyedLong": "{time1} since the last Patrol Helicopter was on the map, {time2} since it last got downed{location}.", "timeSinceLastSinceDestroyedShort": "{time1} since last.\n{time2} since destroyed{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} since the Patrol Helicopter was on the map.", "timeSinceTravelingVendorWasOnMap": "{time} since the Traveling Vendor was on the map.", "timeSinceWipe": "{time} since wipe.", "timeTill": "Time till {event}", "timeTillDaylight": "{time} before daylight.", "timeTillNightfall": "{time} before nightfall.", "timeTillStructureDecay": "{time} before {type} wall decay.", "timeUntilUnlocksAt": "{time} until unlocks at {location}.", "timer": "Timer: {message}.", "timerIdDoesNotExist": "Timer ID: {id} does not exist.", "timerIdInvalid": "Timer ID is invalid.", "timerRemoved": "Timer ID: {id} was removed.", "timerSet": "Licznik ustawiony dla {time}.", "tokensDidNotReplenish": "<PERSON><PERSON><PERSON> did not replenish in time.", "toolCupboard": "Szafa z narzędziami (TC)", "total": "Total", "tracker": "Tracker", "trackerAddPlayerDesc": "<PERSON><PERSON><PERSON> grac<PERSON> do {tracker}", "trackerRemovePlayerDesc": "Usuń Gracza z {tracker}", "trademarkShownBeforeMessage": "{trademark} will be shown before messages.", "trainYard": "Zajezdnia kolejowa", "travelingVendor": "Traveling Vendor", "travelingVendorDetectedSetting": "When the Traveling Vendor is detected, send a notification.", "travelingVendorHaltedAt": "The Traveling Vendor stopped at {location}.", "travelingVendorHaltedSetting": "When the Traveling Vendor stops moving, send a notification.", "travelingVendorLeftSetting": "When the Traveling Vendor left the map, send a notification.", "travelingVendorLocatedAt": "The Traveling Vendor is located at {location}.", "travelingVendorLeftMap": "The Traveling Vendor just left the map at {location}.", "travelingVendorNotCurrentlyOnMap": "The Traveling Vendor is not currently on the map.", "travelingVendorResumedAt": "The Traveling Vendor resumed moving at {location}.", "travelingVendorSpawnedAt": "The Traveling Vendor spawned at {location}.", "turnOffCap": "WYŁĄCZ", "turnOnCap": "WŁĄCZ", "turningGroupOnOff": "Przełączanie grupy {group} {status}.", "two": "Dwa", "type": "<PERSON><PERSON><PERSON>", "unavailable": "Unavailable", "underscore": "Underscore", "underwater": "Underwater", "underwaterLab": "Podwodne Labolatorium", "unhandledRejection": "Unhandled Rejection: {error}", "unknown": "Unknown", "unknownInteraction": "Unknown Interaction...", "unmutedCap": "UNMUTED", "updateCap": "UPDATE", "upkeep": "Upkeep", "upkeepForItem": "Upkeep for {item} is {cost}.", "userAddedToBlacklist": "{user} was added to blacklist.", "userAlreadyInBlacklist": "{user} already in blacklist.", "userButtonInteraction": "Button Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Button Interaction - VerifyId: {id} SUCCESS", "userJustConnected": "{name} just connected.", "userModalInteraction": "Modal Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Modal Interaction - VerifyId: {id} SUCCESS", "userNotInBlacklist": "{user} not in blacklist.", "userNotRegistered": "{user} is not registered.", "userPartOfBlacklist": "VerifyId: {id}, {user} is part of the blacklist.", "userPartOfBlacklistDiscord": "Blacklisted User! Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "userPartOfBlacklistInGame": "Blacklisted User! User: {user}, Message: {message}.", "userRemovedFromBlacklist": "{user} został usunięty z czarnej listy.", "userSaid": "{user} said, {text}", "userSelectMenuInteraction": "Select Menu Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Select Menu Interaction - VerifyId: {id} SUCCESS", "userTurnedOnOffSmartSwitchFromDiscord": "{user} wyłączył Smart Przełącznik {name} {status} z Discord-a.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} wyłączył Smart Przełącznik {name} {status} z Discord-a.", "value": "<PERSON><PERSON><PERSON><PERSON>", "vendingMachine": "Vending Machine", "vendingMachineDetectedSetting": "When a new Vending Machine is detected, send a notification.", "voiceCap": "GŁOS", "warningCap": "WARNING", "waterTreatmentPlant": "Water Treatment Plant", "websiteCap": "WEBSITE", "websocketClosedBeforeConnection": "WebSocket was closed before the connection was established.", "westOfGrid": "West of grid", "wipe": "Wipe", "wipeDetected": "Wipe detected!", "yield": "Plon", "youAreAlreadyLeader": "You are already leader.", "youAreNotPairedWithServer": "Leader command does not work because you're not paired with the server."}