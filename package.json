{"name": "rustplusplus", "version": "1.22.0", "description": "A NodeJS Discord Bot that uses the rustplus.js library to utilize the power of the Rust+ Companion App with additional Quality-of-Life features.", "main": "index.ts", "scripts": {"start": "ts-node .", "preinstall": "npx npm-force-resolutions", "test": "tsc --noEmit -p ."}, "repository": {"type": "git", "url": "https://github.com/alexemanuelol/rustplusplus.git"}, "author": "Alexemanuelol", "license": "SEE LICENSE IN LICENSE", "bugs": {"url": "https://github.com/alexemanuelol/rustplusplus/issues"}, "homepage": "https://github.com/alexemanuelol/rustplusplus#readme", "dependencies": {"@discordjs/rest": "^1.6.0", "@discordjs/voice": "^0.16.0", "@formatjs/intl": "^2.6.9", "@liamcottle/push-receiver": "^0.0.4", "@liamcottle/rustplus.js": "git+https://github.com/alexemanuelol/rustplus.js.git#089cfd3db1b04709911948bce669273139c6a124", "axios": "^1.3.4", "colors": "^1.4.0", "discord-api-types": "^0.37.37", "discord.js": "^14.8.0", "ffmpeg-static": "^5.1.0", "gm": "^1.25.0", "jimp": "^0.22.7", "libsodium-wrappers": "^0.7.11", "lodash": "^4.17.21", "translate": "^1.4.1", "ts-node": "^10.9.1", "typescript": "^4.8.2", "winston": "^3.8.2"}, "resolutions": {"jpeg-js": "0.4.4", "protobufjs": "7.2.4"}}