{"24HoursInGameTimePassed": "24 heures de temps de jeu se sont écoulées.", "abandonedCabins": "Cabines Abandonnées", "abandonedMilitaryBase": "Base Militaire Abandonnée", "abandonedSupermarket": "<PERSON>mar<PERSON><PERSON>", "addPlayerCap": "<PERSON><PERSON><PERSON>", "addSwitchCap": "Ajouter SWITCH", "afkCap": "Absent", "airfield": "Aérodrome", "alarmHaveNotBeenTriggeredYet": "L'alarme {alarm} n'a pas encore été déclenchée.", "alias": "<PERSON><PERSON><PERSON>", "aliasAlreadyExist": "Ce surnom/pseudo existe déjà.", "aliasIndexCouldNotBeFound": "Le surnom/pseudo n'est pas trouvable dans l'index.", "aliasWasAdded": "Surnom/pseudo a<PERSON>.", "aliasWasRemoved": "Surnom/pseudo enlevé.", "aliases": "Surnoms/Pseudos", "all": "tous", "allTeammatesAreDead": "Tous tes coéquipiers sont morts.", "alreadySubscribedToItem": "<PERSON><PERSON><PERSON><PERSON> abonné à l'item {name}.", "ampersand": "&", "andMorePlayers": "... et {number} autres joueurs.", "any": "N'importe lequel", "apostrophe": "'", "arcticResearchBase": "Base de recherche Arctique", "asterisk": "*", "asteriskCctvDesc": "* signifie que vous avez besoin d'un code numérique différent pour chaque carte", "atLocation": "en {location}.", "atSign": "Au panneau", "autoDayCap": "AUTO-JOUR", "autoNightCap": "AUTO-NUIT", "autoOffAnyOnlineCap": "Désactivation automatique en ligne", "autoOffCap": "AUTO-OFF", "autoOffProximityCap": "AUTO-OFF-A-PROXIMITÉ", "autoOnAnyOnlineCap": "AUTO-ON-EN-LIGNE", "autoOnCap": "AUTO-ON", "autoOnProximityCap": "AUTO-ON-A-PROXIMITE", "autoSettingCap": "AUTO PARAMETRE: ", "automaticallyTurnBackOnOff": " Renvoi automatique de {status} dans {time}.", "automaticallyTurningBackOnOff": "Rétablir automatiquement {device} retour {status}.", "autoturret": "Tourelle automatique", "badGateway": "<PERSON><PERSON><PERSON><PERSON> passerelle: {error}", "banditCamp": "Camp de bandits", "baseIsUnderAttack": "Votre base est attaquée !", "battlemetricsApiRequestFailed": "La requête de l'API battlemetrics a échoué : {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics server {server} Échec de mise à jour.", "battlemetricsGlobalLoginCap": "CONNEXION GLOBALE", "battlemetricsGlobalLogoutCap": "DÉCONNEXION GLOBALE", "battlemetricsGlobalNameChangesCap": "CHANGEMENTS DE NOM GLOBAUX", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "L'instance Battlemetrics n'a pas d'identifiant et de nom.", "battlemetricsInstanceCouldNotBeFound": "L'instance Battlemetrics pour {id} est introuvable.", "battlemetricsOnlinePlayers": "Joueurs en ligne Battlemetrics", "battlemetricsPlayersLogin": "Connexion des joueurs Battlemetrics", "battlemetricsPlayersLogout": "Déconnexion des joueurs de Battlemetrics", "battlemetricsPlayersNameChanged": "Nom des joueurs Battlemetrics modifié", "battlemetricsServerNameChanged": "Nom du serveur Battlemetrics modifié", "battlemetricsServerNameChangesCap": "CHANGEMENTS DE NOM DU SERVEUR", "battlemetricsTrackerNameChangesCap": "CHANGEMENTS DE NOM DU TRACKER", "battlemetricsTrackerPlayerNameChanged": "Nom du joueur Battlemetrics Tracker modifié", "blacklist": "Blacklist", "boomBox": "Boom Box", "bot": "bot", "broadcaster": "Diffuseur", "buttonValueChange": "Bouton d'Interaction - Vérification d'Id: {id}, Valeur: {value}.", "buy": "achat", "calculated": "Calculé", "cargoAt": "Cargo en {location}.", "cargoLeavingMapAt": "Cargo quitte la map en {location}.", "cargoLocatedAt": "Cargo est localisé en {location}.", "cargoNotCurrentlyOnMap": "Le cargo n'est actuellement pas sur la map.", "cargoShipDetectedSetting": "<PERSON><PERSON><PERSON> le Cargo est détecté, envoyez une notification.", "cargoShipDockingAtHarbor": "Le cargo vient d'accoster au port en {location}", "cargoShipDockingAtHarborSetting": "Lorsque le cargo est amarré dans un port, envoyez une notification.", "cargoShipEgressSetting": "<PERSON><PERSON><PERSON> le Cargo entre en phase de sortie, envoyez une notification.", "cargoShipEntersEgressStage": "Le cargo semble être en phase de sortie en {location}.", "cargoShipEntersMap": "Cargo Ship entre dans la map à partir de {location}.", "cargoShipLeftHarbor": "Un cargo vient de quitter le port en {location}", "cargoShipLeftMap": "Le cargo vient de quitter la map en {location}.", "cargoShipLeftSetting": "Lorsque le cargo a quitté la map, envoyer une notification.", "cargoShipLocated": "Le cargo a été localisé en {location}.", "cargoship": "Cargo", "ceilingLight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "channelNameActivity": "activité", "channelNameAlarms": "alarmes", "channelNameCommands": "commandes", "channelNameEvents": "évènements", "channelNameInformation": "information", "channelNameServers": "serveurs", "channelNameSettings": "paramètres", "channelNameStorageMonitors": "moniteurs de stockage", "channelNameSwitchGroups": "switchGroups", "channelNameSwitches": "switches", "channelNameTeamchat": "teamchat", "channelNameTrackers": "trackeurs", "chinook47": "Chinook 47", "chinook47DetectedSetting": "<PERSON><PERSON><PERSON> le Chinook entre dans la map, envoyer une notification.", "chinook47EntersMap": "Le Chinook entre dans la map en {location} pour déposer une caisse.", "chinook47LeftMap": "Le Chinook est sorti de la map en {location}.", "chinook47Located": "Le Chinook à été localisé en {location}.", "chinook47NotOnMap": "Le Chinook n'est pas sur la map.", "christmasLights": "<PERSON><PERSON><PERSON>", "circumflex": "Circonflexe", "clanTag": "Tag De Clan", "codes": "Codes", "colon": "Monnaie", "comma": ",", "commandCap": "COMMANDE", "commandDelaySetting": "Doit-il y avoir un délai de commande ? Combien de temps?", "commandNotPossibleDiscord": "La commande n'est pas possible via discord.", "commandSyntaxAdd": "Ajouter", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "en vie", "commandSyntaxArmored": "armored", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "connexion", "commandSyntaxConnections": "connexions", "commandSyntaxCraft": "craft", "commandSyntaxDeath": "mort", "commandSyntaxDeaths": "morts", "commandSyntaxDecay": "decay", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "events", "commandSyntaxHeli": "helicop<PERSON>", "commandSyntaxLanguage": "language", "commandSyntaxLarge": "large", "commandSyntaxLeader": "leader", "commandSyntaxList": "liste", "commandSyntaxMarker": "marqueur", "commandSyntaxMarkers": "marqueurs", "commandSyntaxMarket": "marché", "commandSyntaxMetal": "metal", "commandSyntaxMute": "muet", "commandSyntaxNote": "note", "commandSyntaxNotes": "notes", "commandSyntaxOff": "d<PERSON><PERSON><PERSON>", "commandSyntaxOffline": "hors ligne", "commandSyntaxOn": "activé", "commandSyntaxOnline": "en ligne", "commandSyntaxPlayer": "joueur", "commandSyntaxPlayers": "joueurs", "commandSyntaxPop": "pop", "commandSyntaxProx": "prox", "commandSyntaxRecycle": "recycle", "commandSyntaxRemove": "supprimer", "commandSyntaxResearch": "research", "commandSyntaxSearch": "recherche", "commandSyntaxSend": "envoyer", "commandSyntaxSmall": "petit", "commandSyntaxStack": "stack", "commandSyntaxStatus": "status", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "stone", "commandSyntaxSubscribe": "sub", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "team", "commandSyntaxTime": "temps", "commandSyntaxTimer": "minuteur", "commandSyntaxTimers": "minuteurs", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendeur", "commandSyntaxTwig": "twig", "commandSyntaxUnmute": "r<PERSON><PERSON><PERSON> le son", "commandSyntaxUnsubscribe": "unsub", "commandSyntaxUpkeep": "Coût d'entretien", "commandSyntaxUptime": "disponibilité", "commandSyntaxWipe": "effacer", "commandSyntaxWood": "wood", "commandsAlarmDesc": "Opérations sur les alarmes intelligentes.", "commandsAlarmEditDesc": "Modifier les propriétés de l'alarme intelligente.", "commandsAlarmEditIdDesc": "L'ID de l'alarme intelligente.", "commandsAlarmEditImageDesc": "Définissez l'image qui représente le mieux l'alarme intelligente.", "commandsAliasAddAliasDesc": "Pseudo/Surnom à utiliser.", "commandsAliasAddDesc": "Ajouter un pseudo/surnom.", "commandsAliasAddValueDesc": "La commande/séquence de caractères.", "commandsAliasDesc": "<PERSON><PERSON><PERSON> un pseudo/surnom pour une commande/séquence de caractères.", "commandsAliasRemoveDesc": "Supprimez un pseudo/surnom.", "commandsAliasRemoveIndexDesc": "L'index du pseudo/surnom à été supprimer.", "commandsAliasShowDesc": "Afficher tous les pseudos/surnoms enregistrés.", "commandsBlacklistAddDesc": "Ajouter un utilisateur à la blacklist.", "commandsBlacklistDesc": "Blacklist un utilisateur pour l'empêcher d'utiliser le bot.", "commandsBlacklistDiscordUserDesc": "L’utilisateur Discord.", "commandsBlacklistRemoveDesc": "Supprimer l'utilisateur de la blacklist.", "commandsBlacklistShowDesc": "Afficher les utilisateurs sur blacklist.", "commandsBlacklistSteamidDesc": "L'Id Steam de l'utilisateur.", "commandsCctvDesc": "Afficher les codes de vidéosurveillance d'un monument", "commandsCraftDesc": "Affichez le coût de fabrication d'un item.", "commandsCraftQuantityDesc": "La quantité d'un item à fabriquer.", "commandsCredentialsAddDesc": "Ajouter des identifiants FCM.", "commandsCredentialsDesc": "Définir/Effacer les informations d'identification FCM pour le compte utilisateur.", "commandsCredentialsRemoveDesc": "Supprimer les identifiants FCM.", "commandsCredentialsRemoveSteamIdDesc": "ID Steam des identifiants FCM à supprimer.", "commandsCredentialsSetHosterDesc": "Définir l'hébergeur des informations d'identification FCM.", "commandsCredentialsSetHosterSteamIdDesc": "Définir l'hébergeur des informations d'identification FCM.", "commandsCredentialsShowDesc": "Afficher les identifiants FCM actuellement enregistrés.", "commandsDecayDesc": "Afficher le temps de decay d'un item.", "commandsDespawnDesc": "Afficher l'heure du despawn d'un item.", "commandsHelpCommandList": "Liste des commandes", "commandsHelpDesc": "Affiche ce message d'aide.", "commandsHelpHowToCredentials": "Comment enregistrer les informations d'identification", "commandsHelpHowToPairServer": "Comment associer un bot avec le serveur Rust", "commandsItemDesc": "Obt<PERSON>z les détails d'un item.", "commandsLeaderDesc": "<PERSON><PERSON> ou prendre le rôle de chef d'un membre de l'équipe.", "commandsLeaderMemberDesc": "Nom de l'équipe.", "commandsMapAllDesc": "Obtenez la carte en incluant les noms des monuments et les marqueurs.", "commandsMapCleanDesc": "Obtenez la carte propre.", "commandsMapDesc": "Obtenez l'image de la carte du serveur actuellement connecté.", "commandsMapMarkersDesc": "Obtenez la carte avec les marqueurs.", "commandsMapMonumentsDesc": "Obtenez la carte avec les noms des monuments.", "commandsMarketDesc": "Opérations pour les vending Machines en jeu.", "commandsMarketListDesc": "Afficher la liste des abonnements.", "commandsMarketOrderDesc": "Le type de commande.", "commandsMarketSearchDesc": "Rechercher un item dans les vending Machines.", "commandsMarketSubscribeDesc": "S'abonner à un item dans les vending Machines.", "commandsMarketUnsubscribeDesc": "Se désabonner d'un item dans les vending Machines.", "commandsPlayersBattlemetricsIdDesc": "L'ID Battlemetrics du serveur (par défaut : Le serveur connecté).", "commandsPlayersDesc": "Obtenez des informations sur le(s) joueur(s) basés sur Battlemetric.", "commandsPlayersNameDesc": "Recherchez un joueur sur Battlemetrics en fonction du nom du joueur.", "commandsPlayersPlayerIdDesc": "Recherchez un joueur sur Battlemetrics en fonction de son ID.", "commandsPlayersPlayerIdPlayerIdDesc": "L'identifiant du joueur.", "commandsPlayersStatusDesc": "Recherchez des joueurs en ligne/hors ligne/n'importe lequel.", "commandsRecycleDesc": "Afficher le résultat du recyclage d’un item.", "commandsRecycleQuantityDesc": "La quantité de l'item à recycler.", "commandsRecycleRecyclerTypeDesc": "Le type de recycleur (recycleur, broyeur, recycleur en zone sûre).", "commandsResearchDesc": "Affichez le coût de recherche d'un item.", "commandsResetAlarmsDesc": "Réinitialiser le canal des alarmes.", "commandsResetDesc": "Réinitialisez-les channels Discord.", "commandsResetInformationDesc": "Réinitialiser le channel d'information.", "commandsResetServersDesc": "Réinitialiser les channels des serveurs.", "commandsResetSettingsDesc": "Réinitialiser le canal des paramètres.", "commandsResetStorageMonitorsDesc": "Réinitialisez le canal des moniteurs de stockage.", "commandsResetSwitchesDesc": "Réinitialiser les commutateurs et les canaux switchGroups.", "commandsResetTrackersDesc": "Réinitialiser le channel des trackers.", "commandsRoleClearDesc": "Efface<PERSON> le rôle(pour permettre à tous de voir les channels rustplusplus).", "commandsRoleDesc": "Définissez/effacez un rôle spécifique qui pourra voir le contenu de la catégorie rustplusplus.", "commandsRoleSetDesc": "Définir le rôle.", "commandsRoleSetRoleDesc": "Le rôle des chaînes rustplusplus sera visible.", "commandsStackDesc": "Afficher la taille de la pile d'un item.", "commandsStoragemonitorDesc": "Opérations sur les moniteurs de stockage.", "commandsStoragemonitorEditDesc": "Modifier les propriétés d'un moniteur de stockage.", "commandsStoragemonitorEditIdDesc": "L’ID du moniteur de stockage.", "commandsStoragemonitorEditImageDesc": "Définissez l’image qui représente le mieux le moniteur de stockage.", "commandsSwitchDesc": "Opérations sur les commutateurs intelligents.", "commandsSwitchEditDesc": "Modifier les propriétés d'un Smart Switch.", "commandsSwitchEditIdDesc": "L’ID du Smart Switch.", "commandsSwitchEditImageDesc": "Définissez l’image qui représente le mieux le Smart Switch.", "commandsUpkeepDesc": "A<PERSON><PERSON><PERSON> le coût de l'upkeep d'un item.", "commandsUptimeBotDesc": "Afficher la disponibilité du bot.", "commandsUptimeDesc": "Afficher la disponibilité du bot et du serveur.", "commandsUptimeServerDesc": "Afficher la disponibilité du serveur.", "commandsVoiceBotJoinedVoice": "Le Bot a rejoint le channel vocal", "commandsVoiceBotLeftVoice": "Le Bot a quitté le channel vocal", "commandsVoiceDesc": "Commandes vocales du robot", "commandsVoiceFemale": "<PERSON>mme", "commandsVoiceFemaleDescription": "Définit le sexe du doubleur sur Femme", "commandsVoiceGenderDesc": "Définit le sexe du doubleur du Bot.", "commandsVoiceJoin": "Rejoindre le canal vocal {name} avec l'ID {id} dans la guilde {guild}", "commandsVoiceJoinDesc": "Rejoint le canal vocal", "commandsVoiceLeave": "Quitte le canal vocal {name} avec l'ID {id} dans la guilde {guild}", "commandsVoiceLeaveDesc": "<PERSON><PERSON><PERSON> le canal vocal", "commandsVoiceMale": "<PERSON><PERSON>", "commandsVoiceMaleDescription": "Définit le sexe du doubleur sur Homme", "commandsVoiceNotInVoice": "Vous n'êtes pas dans un channel vocal", "connect": "Connecte", "connectCap": "CONNECTE", "connected": "Connecté", "connectedCap": "CONNECTE", "connectedToServer": "CONNEXION AU SERVEUR.", "connectingCap": "CONNEXION", "connectingToServer": "RECONNEXION AU SERVEUR...", "connectionEvents": "Événements de connexion", "connectionRefusedTo": "Connexion refusée : {id}.", "connectionsCap": "CONNEXION", "couldNotAddStepTracers": "Impossible d'ajouter des traceurs d'étapes.", "couldNotAppendMapMarkers": "Impossible d'ajouter des marqueurs de carte, l'instance d'informations rust+ n'est pas définie.", "couldNotAppendMapMonuments": "Impossible d'ajouter des monuments sur la carte, l'instance d'informations rust+ n'est pas définie.", "couldNotAppendMapTracers": "Impossible d'ajouter des traceurs de carte, l'instance d'informations rust+ n'est pas définie.", "couldNotConnectTo": "Impossible de se connecter à : {id}.", "couldNotCreateCategory": "Impossible de créer la catégorie: {name}", "couldNotCreateTextChannel": "Impossible de créer le salon textuel : {name}", "couldNotDeferInteraction": "Impossible de reporter l'interaction.", "couldNotDeleteCategory": "Impossible de supprimer la catégorie: {categoryId}", "couldNotDeleteChannel": "Impossible de supprimer le channel: {channelId}", "couldNotDeleteMessage": "Impossible de supprimer le message : {message}", "couldNotFindAnyPlayers": "Impossible de trouver des joueurs.", "couldNotFindCategory": "Impossible de trouver la catégorie: {category}", "couldNotFindChannel": "Impossible de trouver le salon : {channel}", "couldNotFindCraftDetails": "Impossible de trouver les détails de l'artisanat pour {name}.", "couldNotFindDecayDetails": "Impossible de trouver les détails du decay pour {name}.", "couldNotFindDespawnDetails": "Impossible de trouver les détails du despawn pour {name}.", "couldNotFindGuild": "Impossible de trouver la guilde : {guildId}", "couldNotFindLanguage": "Impossible de trouver la langue : {language}", "couldNotFindMessage": "Impossible de trouver le message {message}", "couldNotFindPlayer": "Impossible de trouver le joueur {name}.", "couldNotFindPlayerId": "Impossible de trouver le joueur avec son Id {id}.", "couldNotFindRecycleDetails": "Impossible de trouver les détails de recyclage pour {name}.", "couldNotFindResearchDetails": "Impossible de trouver les détails de la recherche pour {name}.", "couldNotFindRole": "Impossible de trouver le rôle : {roleId}", "couldNotFindStackDetails": "Impossible de trouver les détails de la pile pour {name}.", "couldNotFindTeammate": "Impossible de trouver le coéquipier : {name}.", "couldNotFindUpkeepDetails": "Impossible de trouver les détails de l'upkeep pour {name}.", "couldNotFindUser": "Impossible de trouver l'utilisateur : {userId}", "couldNotGetChannelWithId": "Impossible d'obtenir le salon avec l'id : {id}.", "couldNotIdentifyMember": "Impossible d'identifier le membre de l'équipe : {name}.", "couldNotPerformBulkDelete": "Impossible d'effectuer une suppression en bloc sur le canal : {channel}", "couldNotPerformMessageDelete": "Impossible d'effectuer la suppression du message.", "couldNotPerformMessagesFetch": "Impossible d'effectuer la récupération des messages sur le canal : {channel}", "couldNotRegisterSlashCommands": "Impossible d'enregistrer les commandes Slash pour la guilde : {guildId}.", "couldNotSetParent": "Impossible de définir le parent pour le canal : {channelId}", "craft": "Craft", "crate": "Caisse", "createGroupCap": "CRÉER UN GROUPE", "createTrackerCap": "CRÉER UN TRACKER", "credentialsAddedSuccessfully": "Les identifiants FCM ont été ajoutés avec succès pour le steamid: {steamId}!", "credentialsAlreadyRegistered": "Identifiants FCM pour steamId : {steamId} sont déjà enregistrés !", "credentialsCannotStartLiteAlreadyHoster": "Impossible de démarrer FCM Listener Lite pour les ID steam : {steamId}. <PERSON><PERSON><PERSON><PERSON>.", "credentialsDoNotExist": "Informations d'identification FCM pour les ID steam: {steamId} n'existe pas.", "credentialsHosterNotSetForGuild": "L'hébergeur des informations d'identification FCM n'est pas défini pour la guilde {id}, veuillez définir un hébergeur.", "credentialsNotRegistered": "Identifiants FCM pour steamId : {steamId} n'est pas enregistré !", "credentialsNotRegisteredForGuild": "Les informations d'identification FCM ne sont pas enregistrées pour la guilde : {id}, impossible de démarrer l'écouteur FCM.", "credentialsRemovedSuccessfully": "Les informations d'identification FCM pour steamId : {steamId} ont été supprimées avec succès !", "credentialsSetHosterSuccessfully": "L'hébergeur des informations d'identification FCM a été défini avec succès sur steamId : {steamId}.", "currencySign": "Symbôle de devise", "currentCommandDelay": "<PERSON><PERSON><PERSON> de commande actuel : {delay} secondes.", "currentItemHp": "Les HP actuels de l'item.", "currentPrefixPlaceholder": "Préfixe actuel : {prefix}", "customCommand": "Commande personnalisée", "customTimerEditCargoShipEgressLabel": "Temps de sortie du cargo (seconds) :", "customTimerEditCrateOilRigUnlockLabel": "Temps de unlock de la crate OilRig (seconds):", "customTimerEditDesc": "Modification des minuteries personnalisées", "customTimersCap": "MINUTERIES PERSONNALISÉES", "dash": "<PERSON><PERSON><PERSON>", "dayOfWipe": "Jour {day}", "deathCap": "Mort", "decay": "Decay", "decayTimeForItem": "Temps de decay pour {item} est {time}.", "decayingCap": "DECAYING", "deleteUnreachableDevicesCap": "SUPPRIMER LES APPAREILS INACCESSIBLES", "despawnTime": "Temps du Despawn", "despawnTimeOfItem": "Heure du Despawn de {item} est {time}.", "deviceIsAlreadyOnOff": "{device} est déjà {status}.", "deviceIsCurrentlyOnOff": "{device} est actuellement {status}.", "deviceWasTurnedOnOff": "{device} a été transformé en {status}.", "disabledCap": "DÉSACTIVÉ", "discoFloor": "Discofloor", "disconnectCap": "DECONNECTER", "disconnected": "Deconnecte", "disconnectedCap": "DECONNECTER", "disconnectedFromServer": "DÉCONNECTER DU SERVEUR.", "discordCap": "DISCORD", "discordUsers": "Utilisateur discord", "displayInformationBattlemetricsAllOnlinePlayers": "Tous les joueurs en ligne de Battlemetrics devraient-ils être affichés dans le canal d'information ?", "displayingMap": "Affichage de la carte {mapName}.", "displayingOnlinePlayers": "Affichage des joueurs en ligne.", "distanceDirectionGrid": "{distance}m dans la direction {direction}° [{grid}].", "doorController": "Contrôleur de porte", "dot": "Point", "eastOfGrid": "Est de la grille", "editCap": "MODIFIER", "editing": "Modification", "editingOf": "Modification De {entity}", "egressInTime": "Sortie dans {time} en {location}.", "eight": "8", "elevator": "Elevateur", "empty": "Empty", "enabledCap": "ACTIVÉ", "entityId": "ID d'entité", "equalsSign": "=", "errorCap": "ERREUR", "errorExecutingCommand": "Une erreur s'est produite lors de l'exécution de cette commande !", "eventCap": "EVENEMENT", "eventInfo": "Information sur l'evenement", "exclamationMark": "Point d'exclamation", "failedToScrapeProfileName": "Échec de la récupération du nom du profil : {link}.", "failedToScrapeProfilePicture": "Échec de la récupération de la photo de profil : {link}.", "fcmCredentials": "Informations d'authentification", "fcmListenerStartHost": "L'hôte d'écoute FCM démarrera dans 5 secondes pour guild Id : {guildId}, steamId : {steamId}.", "fcmListenerStartLite": "FCM-listener <PERSON><PERSON> dans 5 secondes pour guild Id : {guildId}, steamId : {steamId}.", "ferryTerminal": "Terminal Maritime", "fishingVillage": "Village de pêcheurs", "five": "5", "four": "4", "giantExcavatorPit": "Grand trou d’excavation", "greaterThanSign": "Signe supérieur à", "groupAddSwitchDesc": "Ajouter un commutateur à {group}", "groupRemoveSwitchDesc": "Supprimer le commutateur de {group}", "harbor": "Port", "hasBeenAliveLongest": "{name} est en vie depuis le plus longtemps ({time}).", "hash": "#", "hbhfSensor": "Capteur <PERSON><PERSON><PERSON>", "heart": "Coeur", "heater": "<PERSON><PERSON><PERSON>", "heavyScientistCalledSetting": "Lorsque des scientifiques lourds sont appelés sur une grande Oil Rig, envoyez une notification.", "heavyScientistsCalledLarge": "Des scientifiques lourds ont été appelés sur la grande Oil Rig en {location}.", "heavyScientistsCalledSmall": "Des scientifiques lourds ont été appelés sur la Petite Oil en {location}.", "hideTrademark": "Masquer la marque.", "hoster": "<PERSON><PERSON><PERSON><PERSON>", "hp": "HP", "hpExceedMax": "Hp {hp} dépasse le maximum de {max}.", "hqmQuarry": "Carrière HQM", "ignoreSetAvatar": "Définir un avatar ignoré", "ignoreSetNickname": "Définir un pseudo ignoré", "ignoreSetUsername": "Définir un pseudo ignoré", "inGameBotMessagesMuted": "Les messages du bot en jeu sont désactivés.", "inGameBotMessagesUnmuted": "Les messages du bot en jeu ne sont pas mis en sourdine.", "inGameCap": "EN JEU", "inGameEventInfo": "Informations sur les événements en jeu", "inGameTeamNotificationsSetting": "Notifications des coéquipiers en jeu.", "inGameTime": "Temps de jeu : {time}.", "index": "Index", "infoCap": "INFO", "inside": "À l'intérieur", "interactionEditReplyFailed": "Échec de la réponse de modification de l'interaction : {error}", "interactionInvalidChannel": "Interaction depuis un canal non valide.", "interactionReplyFailed": "La réponse de l'interaction a échoué : {error}", "interactionUpdateFailed": "Échec de la mise à jour de l'interaction : {error}", "invalidBattlemetricsId": "ID Battlemetrics invalide.", "invalidGuildOrChannel": "Guilde ou canal invalide.", "invalidHpInterval": "Intervalle HP {hp} non valide.", "invalidId": "ID invalide: {id}.", "invalidStructureType": "Type de structure invalide {type}.", "invalidSubcommand": "Sous-commande invalide.", "invalidTimeDistance": "Distance temporelle non valide : {distance}, précédent : {prevTime}, nouveau : {newTime}", "isDecaying": "{device} est en dégradation!", "isNoLongerConnected": "{device} n'est plus connecté électriquement !", "item": "<PERSON><PERSON>", "itemAvailableInVendingMachine": "{items} Vient d'être disponible dans une vending Machine en [{location}].", "itemAvailableNotifyInGameSetting": "Lorsqu'un item de la liste d'abonnement devient disponible dans une vending Machine, le prévenir en jeu ?", "junkyard": "<PERSON><PERSON><PERSON><PERSON>", "justSubscribedToItem": "Vous venez de vous abonner à l'item {name}.", "languageCode": "Code de langue: {code}", "languageLangNotSupported": "La langue {language} n'est pas prise en charge.", "languageNotSupported": "La langue n'est pas prise en charge.", "largeBarn": "Grande grange", "largeFishingVillage": "Grand Village de Pêcheurs", "largeOilRig": "Grande Oil Rig", "largeWoodBox": "Grande Coffre en Bois", "lastTrigger": "<PERSON><PERSON>", "launchSite": "Site de lancement", "leaderAlreadyLeader": "{name} est déjà chef d'équipe.", "leaderCommandIsDisabled": "La commande Leader est désactivée dans les paramètres.", "leaderCommandOnlyWorks": "La commande Leader ne fonctionne que si le leader actuel est {name}.", "leaderTransferred": "La direction de l'équipe a été transférée à {name}.", "leavingMapAt": "<PERSON>é<PERSON><PERSON> à {location}.", "lessThanSign": ">", "lighthouse": "Ph<PERSON>", "linkCap": "LIEN", "location": "Position", "lockedCrateLargeOilRigUnlocked": "La caisse verrouillée de la grande plate-forme pétrolière a été débloquée en {location}.", "lockedCrateOilRigUnlockedSetting": "Lorsqu'une caisse verrouillée sur une plate-forme pétrolière est déverrouillée, envoyez une notification.", "lockedCrateSmallOilRigUnlocked": "La caisse verrouillée de la petite Oil Rig a été débloqué en {location}.", "logDiscordCommand": "Discord Command - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logDiscordMessage": "Discord Message - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logInGameCommand": "{type} - Command: {command}, User: {user}.", "logInGameMessage": "Message: {message}, Utilisateur: {user}", "logSmartSwitchGroupValueChange": "Groupe de commutateurs intelligents - Valeur : {value}.", "logSmartSwitchValueChange": "Commutateur intelligent - Valeur : {value}.", "loggedInAs": "CONNECTÉ EN TANT QUE: {name}", "makeSureApplicationsCommandsEnabled": "Assurez-vous que les commandes des applications sont cochées lors de la création de l'URL d'invitation.", "map": "Map", "mapSalt": "Map Salt", "mapSeed": "Map Seed", "mapSize": "Map Size", "mapWipeDetectedNotifySetting": "Lorsque Map Wipe est détecté, {group} doit-il être averti ?", "markerAdded": "Le marqueur {name} en [{location}] a été ajouté.", "markerDoesNotExist": "Le marqueur {name} n'existe pas.", "markerLocation": "Le marqueur {name} en [{location}] est à {distance} m de {player} dans la direction {direction}°.", "markerRemoved": "Le marqueur {name} en [{location}] a été supprimé.", "message": "Message", "messageCap": "MESSAGE", "messageDeletedIn30": "Ce message sera supprimé dans 30 secondes.", "messageEditFailed": "L'édition du message a échoué: {error}", "messageReplyFailed": "La réponse au message a échoué : {error}", "messageSendFailed": "L'envoi du message a échoué: {error}", "messageWasSent": "Le message a été envoyé !", "militaryTunnel": "Tunnel militaire", "miningOutpost": "Exploitation Minier", "missileSilo": "Silo à missile", "missingArguments": "Arguments manquants.", "missingPermission": "Vous n'avez pas la permission de faire cela.", "missingTimerMessage": "Message de minuterie manquant.", "modalValueChange": "Interaction modale - VerifyId : {id}, Valeur : {value}.", "more": "plus", "morePlayers": "{players} ...{number} plus.", "mutedCap": "<PERSON><PERSON>", "name": "Nom", "nameChangeHistory": "Historique des changements de nom", "new": "Nouveau", "newVendingMachine": "Une vending Machine a été localisé en {location}.", "newsCap": "Nouvelles", "noActiveTimers": "Au<PERSON>ne minuterie active.", "noCommandDelay": "<PERSON><PERSON> de d<PERSON> de commande.", "noCommunicationSmartSwitch": "Impossible de communiquer avec Smart Switch : {name}", "noData": "Pas de don<PERSON>.", "noDataOnLargeOilRig": "Aucune donnée pour la grande Oil Rig.", "noDataOnSmallOilRig": "Aucune donnée pour la petite Oil Rig.", "noDelayCap": "PAS DE DÉLAIS", "noItemFound": "L'item n'a pu être trouvé dans aucun vending Machine...", "noItemWithIdFound": "Aucun élément portant l'identifiant {id} n'a pu être trouvé.", "noItemWithNameFound": "Aucun élément portant le nom {name} n'a pu être trouvé.", "noNameIdGiven": "Aucun élément portant le nom {name} n'a pu être trouvé.", "noOneIsAfk": "Personne n'est AFK.", "noOneIsOffline": "Personne n'est hors ligne.", "noOneIsOnline": "Personne n'est en ligne.", "noRegisteredConnectionEvents": "Aucun événement de connexion enregistré pour l'instant.", "noRegisteredConnectionEventsUser": "Aucun événement de connexion enregistré pour {user}.", "noRegisteredDeathEvents": "Aucun événement de mort enregistré pour l'instant.", "noRegisteredDeathEventsUser": "Aucun événement de mort enregistré pour {user}.", "noRegisteredEvents": "Aucun événement enregistré pour l'instant.", "noRegisteredMarkers": "Aucun marqueur enregistré.", "noSavedNotes": "Aucune note enregistrée.", "noToolCupboardWereFound": "Aucun moniteur d'armoire à outils n’a été trouvé.", "none": "Aucun", "northEast": "Nord Est", "northOfGrid": "Nord de la grille", "northWest": "Nord Ouest", "notAValidOrderType": "{order} n'est pas un type de commande valide.", "notActive": "Pas actif.", "notConnectedToRustServer": "Pas actuellement connecté à un serveur Rust.", "notExistInSubscription": "L'élément {name} n'existe pas dans la liste d'abonnement.", "notFoundCap": "Introuvable", "notPartOfRole": "Vous ne faites pas partie du rôle {role}, vous ne pouvez donc pas exécuter de commandes de bot.", "notShowingCap": "NE PAS MONTRER", "noteCap": "NOTE", "noteIdDoesNotExist": "ID de note: {id} n'existe pas.", "noteIdInvalid": "L'ID de la note n'est pas valide.", "noteIdWasRemoved": "ID de note : {id} a été supprimé.", "noteSaved": "Note enregistrée.", "offCap": "OFF", "offline": "<PERSON><PERSON> ligne", "offlineTime": "<PERSON><PERSON> hors-ligne", "oilRig": "Oil Rig", "old": "Ancien", "onCap": "ON", "one": "1", "online": "En ligne", "onlineTime": "Temps en ligne", "onlyOneInTeam": "Tu es le seul dans l'équipe.", "outpost": "Avant-poste", "outside": "<PERSON><PERSON><PERSON>", "oxumsGasStation": "Station-service D'Oxum", "pairing": "appariement", "patrolHelicopter": "Patrol", "patrolHelicopterDestroyedSetting": "Lorsque Patrol est détruit, envoyez une notification.", "patrolHelicopterDetectedSetting": "Lorsque Patrol est détecté, envoyez une notification.", "patrolHelicopterEntersMap": "Patrol est entre sur la map depuis {location}.", "patrolHelicopterLeftMap": "Patrol vient de quitter la map à {location}.", "patrolHelicopterLeftSetting": "Lorsque Patrol quitte la map, envoyez une notification.", "patrolHelicopterLocatedAt": "Patrol est situé en {location}.", "patrolHelicopterNotCurrentlyOnMap": "Patrol n'est pas actuellement sur la map.", "patrolHelicopterTakenDown": "Patrol à été abattu en {location}.", "percentSign": "%", "pipe": "<PERSON><PERSON><PERSON>", "playerHasBeenAliveFor": "{name} est en vie depuis {time}.", "playerId": "ID du joueur", "playerJoinedTheTeam": "{name} a rejoint l'équipe.", "playerJustConnected": "{name} vient de se connecter.", "playerJustConnectedTo": "{name} vient de se connecter à {server}.", "playerJustConnectedTracker": "{name} vient de se connecter du tracker {tracker}.", "playerJustDied": "{name} vient de mourrir en {location}.", "playerJustDisconnected": "{name} vient de se déconnecter.", "playerJustDisconnectedFrom": "{name} vient de se déconnecter du serveur {server}.", "playerJustDisconnectedTracker": "{name} vient de se déconnecter du tracker {tracker}.", "playerJustReturned": "{name} est de nouveau actif ({time}).", "playerJustWentAfk": "{name} est actuellement inactif.", "playerLeftTheTeam": "{name} À quitter la team.", "playerNotPairedWithServer": "La commande Leader ne fonctionne pas car {name} n'est pas associé au serveur.", "players": "<PERSON><PERSON><PERSON>", "playersSearch": "Recherche de joueurs", "plusSign": "Signe +", "populationPlayers": "Population : ({current}/{max}) joueurs.", "populationQueue": "{number} joueurs dans la file d'attente.", "powerPlant": "Centrale électrique", "profile": "Profil", "proxLocation": "{name} se trouve à {distance} m de {caller} dans la direction {direction}° [{location}]", "quantity": "Quantité", "questionMark": "Point d'interrogation", "ranch": "Ranch", "ratelimited": "TAUX LIMITÉ", "reconnectingCap": "RECONNEXION", "reconnectingToServer": "RECONNEXION AU SERVEUR...", "recycle": "Recycle", "recycleCap": "RECYCLER", "recycler": "Recycleur", "remain": "restant", "removePlayerCap": "RETIRER LE JOUEUR", "removeSwitchCap": "RETIRER L'INTERRUPTEUR", "removedSubscribeItem": "L'item {name} a été supprimé de l'abonnement.", "research": "Recherche", "researchTable": "Table de recherche", "resetSuccess": "Réinitialisation r<PERSON><PERSON><PERSON> de <PERSON>.", "responseContainError": "La réponse contient une propriété d'erreur avec la valeur : {error}.", "responseIsEmpty": "La réponse est vide.", "responseIsUndefined": "La réponse n'est pas définie.", "responseTimeout": "<PERSON><PERSON><PERSON> d'attente atteint en attendant la réponse.", "resultRecycling": "Résultat du recyclage", "roleCleared": "le rôle rustplusplus à été autorisé.", "roleSet": "le rôle rustplusplus à été défini sur {name}.", "rustMonument": "Rust Monument", "rustplusOperational": "Rustplusplus OPÉRATIONNEL.", "safe-zone-recycler": "Recycler zone sûre", "samsite": "Site SAM", "satelliteDish": "Antenne parabolique", "scrap": "Scrap", "searchResult": "Résultat de recherche pour l'L'item: **{name}**", "second": "{second} second", "secondCommandDelay": "{second} d<PERSON><PERSON> de la deuxième commande.", "seconds": "{seconds} seconds", "secondsCommandDelay": "{seconds} secondes de délai de commande.", "selectInGamePrefixSetting": "Sélectionnez le préfixe de commande dans le jeu à utiliser:", "selectLanguageExtendSetting": "Assurez-vous d'exécuter **/redémarrer discord** pour charger avec succès la nouvelle langue.", "selectLanguageSetting": "Sélectionnez la langue utilisée par le bot:", "selectMenuValueChange": "Sélectionnez Interaction avec le menu - Verification Id : {id}, Valeur : {value}.", "selectTrademarkSetting": "Sélectionnez la marque qui doit être affichée dans chaque message du jeu.", "sell": "vendre", "semicolon": "Point-virgule", "sentTextToSpeech": "Envoy<PERSON> la synthèse vocale.", "server": "serveur", "serverId": "ID du serveur", "serverInfo": "Informations sur le serveur", "serverInvalid": "La connexion au serveur semble invalide. Essayez de vous reconnecter au serveur.", "serverJustOffline": "Le serveur vient juste se mettre hors ligne.", "serverJustOnline": "Le serveur vient d'être mis en ligne.", "serverStatus": "Statut du serveur", "serviceUnavailable": "Service indisponible: {error}", "setBotLanguage": "Définissez la langue du bot sur: {language}.", "seven": "7", "sewerBranch": "Égouts", "shouldBotBeMutedSetting": "Le bot doit-il être désactivé dans le jeu ?", "shouldCommandsEnabledSetting": "Les commandes du jeu doivent-elles être activées ?", "shouldLeaderCommandEnabledSetting": "La commande leader doit-elle être activée ?", "shouldLeaderCommandOnlyForPairedSetting": "La commande leader de<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fonctionner uniquement pour les personnes associées au serveur ?", "shouldSmartAlarmNotifyNotConnectedSetting": "Les alarmes intelligentes devraient-elles avertir même si elles ne sont pas configurées sur le serveur Rust connecté ?", "shouldSmartAlarmsNotifyInGameSetting": "Les alarmes intelligentes doivent-elles avertir en jeu ?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Les commutateurs intelligents et les groupes de commutateurs intelligents devraient-ils avertir dans le jeu lorsqu'ils sont modifiés depuis Discord ?", "showingBlacklist": "<PERSON>ff<PERSON><PERSON> la blacklist.", "showingSubscriptionList": "Afficher la liste des abonnements.", "shredder": "Destructeur", "sirenLight": "<PERSON><PERSON><PERSON>", "six": "6", "slash": "Slash", "slashCommandInteraction": "Interaction avec les commandes Slash - Guilde : {guild}, Canal : {channel}, Utilisateur : {user}, Commande : {command}, VerifyId : {id}.", "slashCommandValueChange": "Interaction de commande Slash - VerifyId : {id}, Valeur : {value}.", "slashCommandsSuccessRegister": "Commandes d'application enregistrées avec succès pour la guilde: {guildId}.", "slots": "Slots", "smallOilRig": "Petite Oil Rig", "smartAlarm": "<PERSON><PERSON><PERSON> intelligente", "smartAlarmEditSuccess": "Alarme intelligente modifiée avec succès {name}.", "smartAlarmNotifyExtendSetting": "Ces notifications d'alarme utiliseront le titre et le message donnés à l'alarme intelligente dans le jeu.\\n- Ces alarmes intelligentes peuvent ne pas être disponibles dans le canal de texte des alarmes dans Discord.", "smartDeviceNotFound": "{device} est introuvable ! Soit il a été détruit, soit {user} a perdu l'accès à l'armoire à outils.", "smartSwitch": "Commutateur intelligent", "smartSwitchAutoDay": "Sera actif uniquement pendant la journée.", "smartSwitchAutoNight": "Sera actif que pendant la nuit.", "smartSwitchAutoOff": "Sera automatiquement inactif pendant la mise à jour.", "smartSwitchAutoOffAnyOnline": "Sera automatiquement inactif si un coéquipier est en ligne.", "smartSwitchAutoOffProximity": "Sera automatiquement inactif si un coéquipier est à proximité.", "smartSwitchAutoOn": "Sera automatiquement actif pendant la mise à jour.", "smartSwitchAutoOnAnyOnline": "Sera automatiquement actif pendant la mise à jour.", "smartSwitchAutoOnProximity": "Sera automatiquement actif si un coéquipier est à proximité.", "smartSwitchEditProximityLabel": "Paramètres de proximité (meters):", "smartSwitchEditSuccess": "Modification réussie sur l'interrupteur intelligent {name}.", "smartSwitchNormal": "L'interrupteur intelligent fonctionne normalement.", "smilyFace": "<PERSON><PERSON>", "somethingWrongWithConnection": "Quelque chose ne va pas avec la connexion.", "southEast": "Sud Est", "southOfGrid": "Sud de la grille", "southWest": "Sud ouest", "sprinkler": "<PERSON><PERSON><PERSON><PERSON>", "stackSize": "<PERSON><PERSON> de <PERSON>", "stackSizeOfItem": "Taille maximum {item} est {quantity}x.", "status": "Status", "statusNotConnectedToServer": "**STATUT** `NON CONNECTE AU SERVEUR!`", "statusNotElectronicallyConnected": "**STATUT** `NON CONNECTÉ ÉLECTRIQUEMENT!`", "statusNotFound": "**STATUT**: NON TROUVER", "steamId": "SteamID", "stoneQuarry": "Carrière de pierre", "storageMonitor": "Storage Monitor", "storageMonitorEditSuccess": "Moniteur de stockage modifié avec succès {name}.", "streamerMode": "Mode Streameur", "subscribeToChangesBattlemetrics": "Abonnez-vous aux différents changements sur Battlemetrics.", "subscriptionList": "Liste d'abonnement", "subscriptionListEmpty": "La liste d'abonnement aux item est vide.", "sulfurQuarry": "Carrière de Sulfure", "switches": "Commutateurs", "teamMember": "Membre de la team", "teamMemberInfo": "Information sur les membres d'une team", "theDome": "Le Dôme", "theIdOfTheItem": "Id de l'item.", "theNameOfTheItem": "Nom de l'item.", "theNameOfThePlayer": "Nom du joueur.", "three": "3", "tilde": "<PERSON><PERSON>", "time": "Temps", "timeBeforeCargoEntersEgress": "{time} Avant que le cargo à {location} n'entre dans la phase de sortie.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} avant que la caisse verrouillée de la grande Oil Rig ne se débloque en ({location}).", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} Avant que la caisse verrouillée à la petite Oil Rig ne se débloque en ({location}).", "timeCap": "TEMPS", "timeFormatInvalid": "Format de temps invalide.", "timeLeftTimer": "{id}: Temps restant: {time}, Message: {message}", "timeSinceAlarmWasTriggered": "L'alarme {alarm} a été déclenchée {time} il y a.", "timeSinceCargoLeft": "{time} depuis que le cargo à quitter la map.", "timeSinceChinook47OnMap": "{time} depu<PERSON> le dernier Chinook 47 sur la map.", "timeSinceHeavyScientistsOnLarge": "{time} Depuis l'appel des scientifiques lourds sur la grande Oil Rig.", "timeSinceHeavyScientistsOnSmall": "{time} Depuis l'appel des scientifiques lourds sur la petite Oil Rig.", "timeSinceLast": "{time} depuis le dernier.", "timeSinceLastEvent": "{time} depuis le dernier événement.", "timeSinceLastSinceDestroyedLong": "{time1} <PERSON><PERSON><PERSON> le dernier patrol sur la map, {time2} la dernière fois qu'il est tombé {location}.", "timeSinceLastSinceDestroyedShort": "{time1} depuis le dernier.\\n{time2} depuis la destruction{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} Avant que le patrol soit sur la map.", "timeSinceTravelingVendorWasOnMap": "{time} depuis que le vendeur ambulant était sur la carte.", "timeSinceWipe": "{time} avant le wipe.", "timeTill": "Temps pour {event}", "timeTillDaylight": "{time} avant le jour.", "timeTillNightfall": "{time} avant la tombée de la nuit.", "timeTillStructureDecay": "{time} avant {type} mur qui perds de la dura.", "timeUntilUnlocksAt": "{time} avant d'être débloquer en {location}.", "timer": "Temps: {message}.", "timerIdDoesNotExist": "Temps ID: {id} n'existe pas.", "timerIdInvalid": "Temps ID est invalide.", "timerRemoved": "Temps ID: {id} à été retiré.", "timerSet": "Temps réglé pour {time}.", "tokensDidNotReplenish": "Tokens n'a pas été réapprovisionné à temps.", "toolCupboard": "TC", "total": "Total", "tracker": "Tracker", "trackerAddPlayerDesc": "Ajouter un joueur au {tracker}", "trackerRemovePlayerDesc": "<PERSON><PERSON><PERSON> un joueur depuie le {tracker}", "trademarkShownBeforeMessage": "{trademark} sera affiché avant les messages.", "trainYard": "Gare de Triage", "travelingVendor": "Vendeur ambulant", "travelingVendorDetectedSetting": "Lors<PERSON> le vendeur ambulant est détecté, envoyez une notification.", "travelingVendorHaltedAt": "Le vendeur ambulant s'est arrêté en {location}.", "travelingVendorHaltedSetting": "<PERSON><PERSON><PERSON> le vendeur ambulant cesse de bouger, envoyez une notification.", "travelingVendorLeftSetting": "<PERSON><PERSON><PERSON> le vendeur ambulant quitte la carte, envoyez une notification.", "travelingVendorLocatedAt": "Le vendeur ambulant est situé en {location}.", "travelingVendorLeftMap": "Le vendeur ambulant vient de laisser la carte en {location}.", "travelingVendorNotCurrentlyOnMap": "Le vendeur ambulant n'est actuellement pas sur la carte.", "travelingVendorResumedAt": "Le vendeur ambulant a repris ses déplacements en {location}.", "travelingVendorSpawnedAt": "Le vendeur ambulant est apparu en {location}.", "turnOffCap": "ETEINDRE", "turnOnCap": "ALLUMER", "turningGroupOnOff": "Changement de groupe {group} {status}.", "two": "2", "type": "Type", "unavailable": "Indisponible", "underscore": "<PERSON><PERSON><PERSON>", "underwater": "Sous-marin", "underwaterLab": "Laboratoire sous-marin", "unhandledRejection": "Rejet non géré: {error}", "unknown": "Inconnu", "unknownInteraction": "Interaction inconnue...", "unmutedCap": "DEMUTE", "updateCap": "Mise à jour", "upkeep": "Upkeep", "upkeepForItem": "Upkeep pour {item} est {cost}.", "userAddedToBlacklist": "{user} à été ajouté à la blackliste.", "userAlreadyInBlacklist": "{user} est déjà dans la blackliste.", "userButtonInteraction": "Bouton Interaction - Guilde: {guild}, Channel: {channel}, Utilisateur: {user}, Idperso: {customid}, Vérification d'Id: {id}.", "userButtonInteractionSuccess": "Bouton d'intéraction - Vérification d'ID: {id} SUCCESS", "userJustConnected": "{name} vient de se connecter.", "userModalInteraction": "L'intéraction modal - Guilde: {guild}, Channel: {channel}, Utilisateur: {user}, Idperso: {customid}, Vérification d'Id: {id}.", "userModalInteractionSuccess": "L'intéraction modal - Vérification d'ID {id} SUCCESS", "userNotInBlacklist": "{user} n'est pas dans la blackliste.", "userNotRegistered": "{user} n'est pas enregistré.", "userPartOfBlacklist": "<PERSON><PERSON><PERSON><PERSON>d: {id}, {user} fait partit de la liste noire.", "userPartOfBlacklistDiscord": "Utilisateur blacklisté ! Guilde: {guild}, Channel: {channel}, Utilisateur: {user}, Message: {message}.", "userPartOfBlacklistInGame": "Utilisateur blacklisté ! Utilisateur: {user}, Message: {message}.", "userRemovedFromBlacklist": "{user} à été retirer de la blackliste.", "userSaid": "{user} dit, {text}", "userSelectMenuInteraction": "Sélection du menu d'interaction- Guilde: {guild}, Channel: {channel}, Utilisateur: {user}, Idperso: {customid}, Vérification d'Id : {id}.", "userSelectMenuInteractionSuccess": "Selection du menu d'intéraction- Vérification d'ID: {id} Succès", "userTurnedOnOffSmartSwitchFromDiscord": "{user} <PERSON><PERSON> les interrupteurs intelligents sur Off {name} {status} via discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} <PERSON><PERSON> les interrupteurs intelligents sur Off pour sa team {name} {status} via discord.", "value": "<PERSON><PERSON>", "vendingMachine": "Vending Machine", "vendingMachineDetectedSetting": "Lorsqu'une nouvelle vending Machines est détecté, envoyer une notification.", "voiceCap": "VOIX", "warningCap": "ATTENTION", "waterTreatmentPlant": "Station d'épuration", "websiteCap": "Site internet", "websocketClosedBeforeConnection": "Le protocole web socket a été fermée avant que la connexion n'ait pu s'établir.", "westOfGrid": "Ouest de la grille", "wipe": "Wipe", "wipeDetected": "Wipe detecté!", "yield": "Rendement", "youAreAlreadyLeader": "Vous êtes déjà le chef du groupe.", "youAreNotPairedWithServer": "La commande Leader ne fonctionne pas car n'est pas associé au serveur."}