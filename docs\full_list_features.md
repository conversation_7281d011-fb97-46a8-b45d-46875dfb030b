# Full list of Features

## Discord Slash Commands
- **/alarm** - Change image of paired Smart Alarms.
- **/alias** - Create an alias for a command/sequence of characters.
- **/blacklist** - Blacklist a user from using the bot.
- **/cctv** - Get cctv camera codes for monuments.
- **/craft** - Display the cost to craft an item.
- **/credentials** - Setup Credentials.
- **/decay** - Display the decay time of an item.
- **/help** - Get help message.
- **/item** - Get the details of an item.
- **/leader** - Transfer leadership.
- **/map** - Display the In-Game Map.
- **/market** - Search for or subscribe to items in vending machines.
- **/players** - Get Battlemetrics data on all connected players.
- **/recycle** - Display the output of recycling an item.
- **/research** - Display the cost to research an item.
- **/reset** - Reset Discord Channels.
- **/role** - Setup a specific role to use rustplusplus.
- **/storagemonitor** - Change image of paired Storage Monitors.
- **/switch** - Change image of paired Storage Monitors.
- **/upkeep** - Get the upkeep cost of an item.
- **/uptime** - Get the current uptime for rustplusplus.
- **/voice** - Let rustplusplus join voicechat.

## In-Game Commands
- **afk** - Display afk teammates.
- **alive** - Display who has been alive longest.
- **cargo** - Display information regarding Cargoship.
- **chinook** - Display information regarding Chinook 47.
- **connection/connections** - Display latest team connections.
- **craft** - Display the cost to craft an item.
- **death/deaths** - Display latest deaths.
- **decay** - Display the decay time of an item.
- **events** - Get recent events.
- **heli** - Get information regarding Patrol Helicopter.
- **large** - Get information regarding Large Oil Rig.
- **leader** - Transfer leadership.
- **marker/markers** - Set markers to navigate to.
- **market** - Search for or subscribe to items in vending machines.
- **mute** - Mute rustplusplus In-Game.
- **note/notes** - Add notes.
- **offline** - Display offline teammates.
- **online** - Display online teammates.
- **player/players** - Get Battlemetrics information about players.
- **pop** - Get population of the server.
- **prox** - Display teammates that are nearby.
- **recycle** - Display the output of recycling an item.
- **research** - Display the cost to research an item.
- **send** - Send a message through rustplusplus to a person on Discord.
- **small** - Get information regarding Small Oil Rig.
- **steamid** - Get teammate steamid.
- **team** - Get team information (names of all teammates).
- **time** - Get In-Game time.
- **timer/timers** - Setup timers.
- **tr** - Translate from English to another language.
- **trf** - Translate from one language to another.
- **tts** - Text-To-Speech (Need to have teamchat open in Discord).
- **unmute** - Unmute rustplusplus In-Game.
- **upkeep** - Check upkeep of Storage Monitor Tool Cupboards.
- **uptime** - Display the uptime of rustplusplus and currently connected server.
- **vendor** - Get information regarding the Traveling Vendor
- **wipe** - Display time since wipe.

## Smart Devices
> Pair Smart Devices such as `Smart Switches`, `Smart Alarms`, `Storage Monitors` and control them from Discord or In-Game teamchat.

- See [Smart Switches](smart_devices.md#smart-switches).
- See [Smart Switch Groups](smart_devices.md#smart-switch-groups).
- See [Smart Alarms](smart_devices.md#smart-alarms).
- See [Storage Monitors](smart_devices.md#storage-monitors).


## Rust Server Information
- See number of players, max capacity and queue size of the Rust Server.
- See the In-Game time and time till day/night.
- See how long ago wipe was.
- See Map Size.
- See Map Seed.
- See Map Salt.
- See Map Name.
- F1 console connect information.

## In-Game Event Notifications
> Receive notifications for In-Game Events such as:
- **Cargo Ship** - When it spawns, despawns, how long before it enters egress stage. How long time since it was last out. step-trace.
- **Patrol Helicopter** - When it spawns, despawns or gets taken down. How long time since it was last out and how long since it was taken down. step-trace.
- **Oil Rig** - When Oil Rig calls in Heavy Scientists and how long till the Locked Crate unlocks.
- **Chinook 47** - When it enters map and when it leaves.
- **Vending Machines** - Whenever a new Vending Machine appears on the map.

## Teammate Information
> Get information about teammates such as Online/Offline/AFK/Alive/Dead/Location/Paired/Leader.

## Other
- Connect through different Rust Servers seemingly through the `servers` Text-Channel in Discord.
- Easily access rustplusplus settings via the Discord Text-Channel `settings`.
- Run In-Game commands either from In-Game teamchat or from Discord Text-Channel `commands`.
- Communicate with teammates from In-Game to Discord and vice versa.
- Get activity information in the `activity` Text-Channel on Discord. Information such as Smart Devices not reachable, Teammate connect/disconnect/leave/join/death, Smart Alarms notify when triggered, Server went offline/online, Map Wipe Detection, Storage Monitor Decay Notification, Tracker notifications etc...
- Create Battlemetrics Trackers to track players or groups.
- Get Facepunch news in Discord.