{"24HoursInGameTimePassed": "Sono trascorse 24 ore di gioco con successo.", "abandonedCabins": "Cabine abbandonate", "abandonedMilitaryBase": "Base Militare Abbandonata", "abandonedSupermarket": "Supermercato Abbandonato", "addPlayerCap": "AGGIUNGI GIOCATORE", "addSwitchCap": "AGGIUNGI INTERRUTTORE", "afkCap": "AFK", "airfield": "Campo d'Aviazione", "alarmHaveNotBeenTriggeredYet": "La sveglia {alarm} non è ancora suonata.", "alias": "Sopra<PERSON><PERSON>", "aliasAlreadyExist": "Il soprannome esiste già.", "aliasIndexCouldNotBeFound": "Impossibile trovare l'indice del soprannome.", "aliasWasAdded": "Soprannome aggiunto.", "aliasWasRemoved": "Soprannome rimosso.", "aliases": "<PERSON><PERSON><PERSON><PERSON>", "all": "tutti", "allTeammatesAreDead": "Tutti i tuoi compagni di squadra sono morti.", "alreadySubscribedToItem": "<PERSON><PERSON><PERSON> iscritto all'oggetto {name}.", "ampersand": "E commerciale", "andMorePlayers": "... e {number} al<PERSON> gio<PERSON>ori.", "any": "Q<PERSON>un<PERSON>", "apostrophe": "Apostrofo", "arcticResearchBase": "Base di Ricerca Artica", "asterisk": "Asterisco", "asteriskCctvDesc": "* significa che necessiti un codice numerico che è diverso per ogni mappa", "atLocation": "In {location}.", "atSign": "Chiocciola", "autoDayCap": "AUTO-GIORNO", "autoNightCap": "AUTO-NOTTE", "autoOffAnyOnlineCap": "AUTO-SPEGNI-QUALUNQUE-ONLINE", "autoOffCap": "AUTO-SPEGNI", "autoOffProximityCap": "AUTO-SPEGNI-PROSSIMITÀ", "autoOnAnyOnlineCap": "AUTO-SU-QUALUNQUE-ONLINE", "autoOnCap": "AUTO-ACCENDI", "autoOnProximityCap": "AUTO-ACCENDI-PROSSIMITÀ", "autoSettingCap": "IMPOSTAZIONI AUTOMATICHE: ", "automaticallyTurnBackOnOff": " Riattivato automaticamente {status} in {time}.", "automaticallyTurningBackOnOff": "Riaccendendo automaticamente {device} {status}.", "autoturret": "Torretta automatica", "badGateway": "Cattiva gateway: {error}", "banditCamp": "Campo dei Banditi", "baseIsUnderAttack": "La tua base è sotto attacco!", "battlemetricsApiRequestFailed": "Chiamata API Battlemetrics Fallita: {api_call}.", "battlemetricsCap": "BATTLEMETRICS", "battlemetricsFailedToUpdate": "Battlemetrics Server {server} failed to update.", "battlemetricsGlobalLoginCap": "LOGIN GLOBALE", "battlemetricsGlobalLogoutCap": "LOGOUT GLOBALE", "battlemetricsGlobalNameChangesCap": "MODIFICHE DI NOME GLOBALI", "battlemetricsId": "BattlemetricsID", "battlemetricsIdAndNameMissing": "L'istanza di Battlemetrics non contiene id e nome.", "battlemetricsInstanceCouldNotBeFound": "Impossibile trovare l'Istanza Battlemetrics per {id}.", "battlemetricsOnlinePlayers": "Giocatori Online di Battlemetrics", "battlemetricsPlayersLogin": "Login Giocatori di Battlemetrics", "battlemetricsPlayersLogout": "Logout Giocatori di Battlemetrics", "battlemetricsPlayersNameChanged": "Nome Giocatori di Battlemetrics Cambiato", "battlemetricsServerNameChanged": "Nome Server Battlemetrics Cam<PERSON>to", "battlemetricsServerNameChangesCap": "MODIFICHE NOME SERVER", "battlemetricsTrackerNameChangesCap": "MODIFICHE NOME TRACKER", "battlemetricsTrackerPlayerNameChanged": "Nome Giocatore di Battlemetrics Tracker <PERSON>", "blacklist": "Blacklist", "boomBox": "Boom Box", "bot": "bot", "broadcaster": "Trasmettitore", "buttonValueChange": "Interazione Bottone: IdVerifica: {id}, Valore: {value}.", "buy": "compra", "calculated": "Calcolato", "cargoAt": "A {location}.", "cargoLeavingMapAt": "La Nave Cargo sta lasciando la mappa a {location}.", "cargoLocatedAt": "La Nave Cargo si trova a {location}.", "cargoNotCurrentlyOnMap": "Nave Cargo non è attualmente sulla mappa.", "cargoShipDetectedSetting": "Quando la Nave Cargo viene rilevata, invia una notifica.", "cargoShipDockingAtHarbor": "Cargo ship just docked at the Harbor at {location}", "cargoShipDockingAtHarborSetting": "When Cargo Ship is docked at a harbor, send a notification.", "cargoShipEgressSetting": "Quando la Nave Cargo inizia la fase di uscita, invia una notifica.", "cargoShipEntersEgressStage": "La Nave Cargo dovrebbe essere in fase di uscita a {location}.", "cargoShipEntersMap": "Nave <PERSON> entra nella mappa da {location}.", "cargoShipLeftHarbor": "Cargo ship just left the Harbor at {location}", "cargoShipLeftMap": "La Nave Cargo ha appena lasciato la mappa a {location}.", "cargoShipLeftSetting": "Quando la Nave Cargo lascia la mappa, invia una notifica.", "cargoShipLocated": "Nave Cargo si trova a {location}.", "cargoship": "<PERSON>ve <PERSON>", "ceilingLight": "Luce a soffitto", "channelNameActivity": "attività", "channelNameAlarms": "sveglie", "channelNameCommands": "comandi", "channelNameEvents": "eventi", "channelNameInformation": "informazioni", "channelNameServers": "server", "channelNameSettings": "impostazioni", "channelNameStorageMonitors": "monitor-archiviazione", "channelNameSwitchGroups": "gruppi-interruttori", "channelNameSwitches": "interruttori", "channelNameTeamchat": "chat-team", "channelNameTrackers": "tracker", "chinook47": "Chinook 47", "chinook47DetectedSetting": "Quando un Chinook 47 entra nella mappa, invia una notifica.", "chinook47EntersMap": "Il Chinook 47 entra nella mappa da {location} per scaricare la cassa bloccata.", "chinook47LeftMap": "Il Chinook 47 ha lasciato la mappa a {location}.", "chinook47Located": "Il Chinook 47 si trova a {location}.", "chinook47NotOnMap": "Il Chinook 47 non è attualmente sulla mappa.", "christmasLights": "Luci di Natale", "circumflex": "<PERSON>ircum<PERSON><PERSON>", "clanTag": "Tag del Clan", "codes": "Codici", "colon": "<PERSON> punti", "comma": "Virgola", "commandCap": "COMANDO", "commandDelaySetting": "Ci deve essere un ritardo nel comando? Per quanto tempo?", "commandNotPossibleDiscord": "Il comando non è possibile tramite Discord.", "commandSyntaxAdd": "aggiu<PERSON>i", "commandSyntaxAfk": "afk", "commandSyntaxAlive": "vivo", "commandSyntaxArmored": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxCargo": "cargo", "commandSyntaxChinook": "chinook", "commandSyntaxConnection": "connessione", "commandSyntaxConnections": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxCraft": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxDeath": "morte", "commandSyntaxDeaths": "morti", "commandSyntaxDecay": "decadimento", "commandSyntaxDespawn": "despawn", "commandSyntaxEvents": "eventi", "commandSyntaxHeli": "eli", "commandSyntaxLanguage": "lingua", "commandSyntaxLarge": "grande", "commandSyntaxLeader": "capo", "commandSyntaxList": "elenco", "commandSyntaxMarker": "marcatore", "commandSyntaxMarkers": "marca<PERSON><PERSON>", "commandSyntaxMarket": "mercato", "commandSyntaxMetal": "metallo", "commandSyntaxMute": "muta", "commandSyntaxNote": "nota", "commandSyntaxNotes": "note", "commandSyntaxOff": "spento", "commandSyntaxOffline": "offline", "commandSyntaxOn": "acceso", "commandSyntaxOnline": "online", "commandSyntaxPlayer": "giocatore", "commandSyntaxPlayers": "gio<PERSON><PERSON>", "commandSyntaxPop": "pop", "commandSyntaxProx": "vicino", "commandSyntaxRecycle": "riciclo", "commandSyntaxRemove": "<PERSON><PERSON><PERSON>", "commandSyntaxResearch": "ricerca", "commandSyntaxSearch": "cerca", "commandSyntaxSend": "invia", "commandSyntaxSmall": "piccola", "commandSyntaxStack": "stack", "commandSyntaxStatus": "stato", "commandSyntaxSteamid": "steamid", "commandSyntaxStone": "pietra", "commandSyntaxSubscribe": "<PERSON><PERSON><PERSON><PERSON>", "commandSyntaxTTS": "tts", "commandSyntaxTeam": "squadra", "commandSyntaxTime": "ora", "commandSyntaxTimer": "timer", "commandSyntaxTimers": "timers", "commandSyntaxTranslateFromTo": "trf", "commandSyntaxTranslateTo": "tr", "commandSyntaxTravelingVendor": "vendor", "commandSyntaxTwig": "ramo", "commandSyntaxUnmute": "smuta", "commandSyntaxUnsubscribe": "di<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandSyntaxUpkeep": "upkeep", "commandSyntaxUptime": "uptime", "commandSyntaxWipe": "wipe", "commandSyntaxWood": "legno", "commandsAlarmDesc": "Operazioni sulle Sveglie Intelligenti.", "commandsAlarmEditDesc": "Modifica le proprietà di una Sveglia Intelligente.", "commandsAlarmEditIdDesc": "ID della Sveglia Intelligente.", "commandsAlarmEditImageDesc": "Imposta l'immagine che rappresenta meglio la  Sveglia Intelligente.", "commandsAliasAddAliasDesc": "Il soprannome da usare.", "commandsAliasAddDesc": "Aggiungi un soprannome.", "commandsAliasAddValueDesc": "Il comando/sequenza di caratteri.", "commandsAliasDesc": "Crea un soprannome per un comando/sequenza di caratteri.", "commandsAliasRemoveDesc": "Rim<PERSON>vi un soprannome.", "commandsAliasRemoveIndexDesc": "L'indice del soprannome da rimuovere.", "commandsAliasShowDesc": "Mostra tutti i soprannomi registrati.", "commandsBlacklistAddDesc": "Aggiungi utente alla blacklist.", "commandsBlacklistDesc": "<PERSON><PERSON> nella blacklist del bot un utente.", "commandsBlacklistDiscordUserDesc": "L'utente discord.", "commandsBlacklistRemoveDesc": "R<PERSON><PERSON>vi utente dalla blacklist.", "commandsBlacklistShowDesc": "<PERSON><PERSON> u<PERSON> in blacklist.", "commandsBlacklistSteamidDesc": "Lo steamid dell'utente.", "commandsCctvDesc": "Mostra i codici CCTV per un monumento", "commandsCraftDesc": "Mostra il costo per costruire un oggetto.", "commandsCraftQuantityDesc": "La quantità di oggetti da costruire.", "commandsCredentialsAddDesc": "Aggiungi le credenziali FCM.", "commandsCredentialsDesc": "Imposta/Cancella le credenziali FCM per l'account utente.", "commandsCredentialsRemoveDesc": "Rimuovi le credenziali FCM.", "commandsCredentialsRemoveSteamIdDesc": "SteamId delle credenziali FCM da rimuovere.", "commandsCredentialsSetHosterDesc": "Imposta l'host delle credenziali FCM.", "commandsCredentialsSetHosterSteamIdDesc": "SteamId dell'hoster delle credenziali FCM.", "commandsCredentialsShowDesc": "Mostra le credenziali FCM registrate attualmente.", "commandsDecayDesc": "Display the decay time of an item.", "commandsDespawnDesc": "Display the despawn time of an item.", "commandsHelpCommandList": "Elenco dei comandi", "commandsHelpDesc": "Mostra il messaggio di aiuto.", "commandsHelpHowToCredentials": "Come Registrare le Credenziali", "commandsHelpHowToPairServer": "Come Associare il Bot con il Server Rust", "commandsItemDesc": "Get the details of an item.", "commandsLeaderDesc": "Assegna o toglie la leadership a un membro del team.", "commandsLeaderMemberDesc": "Il nome del membro del team.", "commandsMapAllDesc": "Otti<PERSON> la mappa con sia i nomi dei monumenti che i marcatori.", "commandsMapCleanDesc": "<PERSON><PERSON><PERSON> la mappa pulita.", "commandsMapDesc": "O<PERSON><PERSON> l'immagine della mappa del server attualmente connesso.", "commandsMapMarkersDesc": "<PERSON><PERSON><PERSON> la mappa con i marcatori.", "commandsMapMonumentsDesc": "O<PERSON><PERSON> la mappa con i nomi dei monumenti.", "commandsMarketDesc": "Operazioni per i Distributori Automatici in-game.", "commandsMarketListDesc": "Mostra l'elenco delle iscrizioni.", "commandsMarketOrderDesc": "Il tipo di ordine.", "commandsMarketSearchDesc": "Cerca un oggetto nei Distributori Automatici.", "commandsMarketSubscribeDesc": "Iscrivi un oggetto nei Distributori Automatici.", "commandsMarketUnsubscribeDesc": "Annulla l'iscrizione di un oggetto nei Distributori Automatici.", "commandsPlayersBattlemetricsIdDesc": "L'ID Battlemetrics del server (predefinito: Il server connesso).", "commandsPlayersDesc": "Ottieni le informazioni sui giocatori basate su Battlemetrics.", "commandsPlayersNameDesc": "Cerca un giocatore su Battlemetrics in base al nome del giocatore.", "commandsPlayersPlayerIdDesc": "Cerca un giocatore su Battlemetrics in base all'id del giocatore.", "commandsPlayersPlayerIdPlayerIdDesc": "L'id giocatore del giocatore.", "commandsPlayersStatusDesc": "Cerca giocatori che sono online/offline/qualsiasi.", "commandsRecycleDesc": "Mostra il risultato del riciclaggio di un oggetto.", "commandsRecycleQuantityDesc": "La quantità di articoli da riciclare.", "commandsRecycleRecyclerTypeDesc": "The recycler type (recycler, shredder, safe-zone-recycler).", "commandsResearchDesc": "Mostra il costo per cercare un oggetto.", "commandsResetAlarmsDesc": "Resetta il canale delle sveglie.", "commandsResetDesc": "Resetta i canali di Discord.", "commandsResetInformationDesc": "Resetta il canale delle informazioni.", "commandsResetServersDesc": "Resetta il canale dei server.", "commandsResetSettingsDesc": "Resetta il canale delle impostazioni.", "commandsResetStorageMonitorsDesc": "Resetta il canale dei monitor di archiviazione.", "commandsResetSwitchesDesc": "Resetta i canali degli interruttori e dei gruppi di interruttori.", "commandsResetTrackersDesc": "Resetta il canale dei tracker.", "commandsRoleClearDesc": "Cancella il ruolo (per permettere a tutti di vedere i canali di rustplusplus).", "commandsRoleDesc": "Imposta/Cancella un ruolo specifico che potrà vedere il contenuto della categoria rustplusplus.", "commandsRoleSetDesc": "Imposta il ruolo.", "commandsRoleSetRoleDesc": "Il ruolo a cui saranno visibili i canali rustplusplus.", "commandsStackDesc": "Display the stack size of an item.", "commandsStoragemonitorDesc": "Operazioni sui monitor di archiviazione.", "commandsStoragemonitorEditDesc": "Modifica le proprietà di un monitor di archiviazione.", "commandsStoragemonitorEditIdDesc": "L'ID del monitor di archiviazione.", "commandsStoragemonitorEditImageDesc": "Imposta l'immagine che rappresenta al meglio il monitor di archiviazione.", "commandsSwitchDesc": "Operazioni sugli interruttori intelligenti.", "commandsSwitchEditDesc": "Modifica le proprietà di un interruttore intelligente.", "commandsSwitchEditIdDesc": "L'ID dell'interruttore intelligente.", "commandsSwitchEditImageDesc": "Imposta l'immagine che rappresenta al meglio l'interruttore intelligente.", "commandsUpkeepDesc": "Display the upkeep cost of an item.", "commandsUptimeBotDesc": "Mostra l'uptime del bot.", "commandsUptimeDesc": "Mostra l'uptime del bot e del server.", "commandsUptimeServerDesc": "Mostra l'uptime del server.", "commandsVoiceBotJoinedVoice": "Il Bot è entrato nel Voicechannel", "commandsVoiceBotLeftVoice": "Il Bot ha lasciato il Voicechannel", "commandsVoiceDesc": "Comandi Vocali del Bot", "commandsVoiceFemale": "<PERSON><PERSON><PERSON>", "commandsVoiceFemaleDescription": "Imposta il genere dell'attore vocale a Femmina", "commandsVoiceGenderDesc": "Imposta il genere dell'attore vocale.", "commandsVoiceJoin": "Entrando nel canale vocale {name} con l'ID {id} nella gilda {guild}", "commandsVoiceJoinDesc": "Unisciti al Canalevocale", "commandsVoiceLeave": "<PERSON><PERSON><PERSON> il canale vocale {name} con l'ID {id} nella gilda {guild}", "commandsVoiceLeaveDesc": "Lascia il Canalevocale", "commandsVoiceMale": "<PERSON><PERSON><PERSON>", "commandsVoiceMaleDescription": "Imposta il genere dell'attore vocale a Maschio", "commandsVoiceNotInVoice": "Non sei in un canale vocale", "connect": "<PERSON><PERSON><PERSON>", "connectCap": "CONNETTI", "connected": "<PERSON><PERSON><PERSON>", "connectedCap": "CONNESSO", "connectedToServer": "CONNESSO AL SERVER.", "connectingCap": "CONNESSIONE", "connectingToServer": "CONNESSIONE AL SERVER...", "connectionEvents": "Eventi di Connessione", "connectionRefusedTo": "Connessione rifiutata a: {id}.", "connectionsCap": "CONNESSIONI", "couldNotAddStepTracers": "Impossibile aggiungere tracciatori passo.", "couldNotAppendMapMarkers": "Impossibile aggiungere marcatori alla mappa, le informazioni di rustplus non sono impostate.", "couldNotAppendMapMonuments": "Impossibile aggiungere i monumenti alla mappa, le informazioni di rustplus non sono impostate.", "couldNotAppendMapTracers": "Could not append map tracers, rustplus info instance is not set.", "couldNotConnectTo": "Impossibile connettersi a: {id}.", "couldNotCreateCategory": "Impossibile creare la categoria: {name}", "couldNotCreateTextChannel": "Impossibile creare il canale di testo: {name}", "couldNotDeferInteraction": "Impossibile rinviare l'interazione.", "couldNotDeleteCategory": "Could not delete category: {categoryId}", "couldNotDeleteChannel": "Could not delete channel: {channelId}", "couldNotDeleteMessage": "Impossibile eliminare il messaggio: {message}", "couldNotFindAnyPlayers": "Impossibile trovare alcun giocatore.", "couldNotFindCategory": "Impossibile trovare la categoria: {category}", "couldNotFindChannel": "Impossibile trovare il canale: {channel}", "couldNotFindCraftDetails": "Could not find craft details for {name}.", "couldNotFindDecayDetails": "Could not find decay details for {name}.", "couldNotFindDespawnDetails": "Could not find despawn details for {name}.", "couldNotFindGuild": "Impossibile trovare il server: {guildId}", "couldNotFindLanguage": "Impossibile trovare la lingua: {language}", "couldNotFindMessage": "Impossibile trovare il messaggio {message}", "couldNotFindPlayer": "Impossibile trovare il giocatore {name}.", "couldNotFindPlayerId": "Could not find player with id {id}.", "couldNotFindRecycleDetails": "Could not find recycle details for {name}.", "couldNotFindResearchDetails": "Could not find research details for {name}.", "couldNotFindRole": "Impossibile trovare il ruolo: {roleId}", "couldNotFindStackDetails": "Could not find stack details for {name}.", "couldNotFindTeammate": "Impossibile trovare il compagno: {name}.", "couldNotFindUpkeepDetails": "Could not find upkeep details for {name}.", "couldNotFindUser": "Impossibile trovare l'utente: {userId}", "couldNotGetChannelWithId": "Impossibile ottenere il canale con id: {id}.", "couldNotIdentifyMember": "Impossibile identificare il membro del team: {name}.", "couldNotPerformBulkDelete": "Impossibile eseguire la cancellazione di massa nel canale: {channel}", "couldNotPerformMessageDelete": "Impossibile eseguire la cancellazione del messaggio.", "couldNotPerformMessagesFetch": "Impossibile recuperare i messaggi nel canale: {channel}", "couldNotRegisterSlashCommands": "Impossibile registrare i Comandi Slash per il server: {guildId}. ", "couldNotSetParent": "Impossibile impostare il parent per il canale: {channelId}", "craft": "Craft", "crate": "Cass<PERSON>", "createGroupCap": "CREA GRUPPO", "createTrackerCap": "CREA TRACKER", "credentialsAddedSuccessfully": "Le credenziali FCM sono state aggiunte correttamente per steamId: {steamId}!", "credentialsAlreadyRegistered": "Le credenziali FCM per steamId: {steamId} sono già registrate!", "credentialsCannotStartLiteAlreadyHoster": "Impossibile avviare FCM Listener Lite per steamId: {steamId}. Già hoster.", "credentialsDoNotExist": "Le credenziali FCM per steamId: {steamId} non esistono.", "credentialsHosterNotSetForGuild": "L'hoster delle credenziali FCM non è impostato per il server {id}, si prega di impostare un hoster.", "credentialsNotRegistered": "Le credenziali FCM per steamId: {steamId} non sono registrate!", "credentialsNotRegisteredForGuild": "Le credenziali FCM non sono registrate per il server: {id}, impossibile avviare FCM-listener.", "credentialsRemovedSuccessfully": "Le credenziali FCM per steamId: {steamId} sono state rimosse correttamente!", "credentialsSetHosterSuccessfully": "L'hoster delle credenziali FCM è stato impostato correttamente su steamId: {steamId}.", "currencySign": "Simbolo di Valuta", "currentCommandDelay": "<PERSON><PERSON> corrente del comando: {delay} secondi.", "currentItemHp": "The current HP of the item.", "currentPrefixPlaceholder": "Prefisso Corrente: {prefix}", "customCommand": "<PERSON><PERSON><PERSON>", "customTimerEditCargoShipEgressLabel": "Tempo di uscita della Nave Cargo (secondi):", "customTimerEditCrateOilRigUnlockLabel": "Tempo di sblocco della Cassa Bloccata della Piattaforma Petrolifera (secondi):", "customTimerEditDesc": "Modifica dei timer personalizzati", "customTimersCap": "TIMER PERSONALIZZATI", "dash": "<PERSON><PERSON><PERSON>", "dayOfWipe": "<PERSON><PERSON><PERSON> {day}", "deathCap": "MORTI", "decay": "Decay", "decayTimeForItem": "Decay time for {item} is {time}.", "decayingCap": "DECADIMENTO", "deleteUnreachableDevicesCap": "DELETE UNREACHABLE DEVICES", "despawnTime": "Despawn Time", "despawnTimeOfItem": "Despawn time of {item} is {time}.", "deviceIsAlreadyOnOff": "{device} is already {status}.", "deviceIsCurrentlyOnOff": "{device} è attualmente {status}.", "deviceWasTurnedOnOff": "{device} è stato messo {status}.", "disabledCap": "DISABILITATO", "discoFloor": "Discofloor", "disconnectCap": "DISCONNETTI", "disconnected": "Disconnected", "disconnectedCap": "DISCONNESSO", "disconnectedFromServer": "DISCONNESSO DAL SERVER.", "discordCap": "DISCORD", "discordUsers": "Discord Users", "displayInformationBattlemetricsAllOnlinePlayers": "Should all online players from Battlemetrics be displayed in the information channel?", "displayingMap": "Visualizzazione mappa {mapName}.", "displayingOnlinePlayers": "Visualizzazione dei giocatori online.", "distanceDirectionGrid": "{distance}m in direzione {direction}° [{grid}].", "doorController": "Controller <PERSON><PERSON>", "dot": "Punt<PERSON>", "eastOfGrid": "Est della riga", "editCap": "MODIFICA", "editing": "Editing", "editingOf": "Editing of {entity}", "egressInTime": "Egress in {time} at {location}.", "eight": "<PERSON>", "elevator": "Ascensore", "empty": "<PERSON><PERSON><PERSON>", "enabledCap": "ABILITATO", "entityId": "Entity ID", "equalsSign": "Segno di Uguale", "errorCap": "ERRORE", "errorExecutingCommand": "Si è verificato un errore nell'esecuzione di questo comando!", "eventCap": "EVENTO", "eventInfo": "Informazioni sull'evento", "exclamationMark": "Punto Esclamativo", "failedToScrapeProfileName": "Impossibile scaricare il nome del profilo: {link}.", "failedToScrapeProfilePicture": "Impossibile scaricare l'immagine del profilo: {link}.", "fcmCredentials": "Credenziali FCM", "fcmListenerStartHost": "FCM-listener Host partirà tra 5 secondi per guildId: {guildId}, steamId: {steamId}.", "fcmListenerStartLite": "FCM-listener Lite partirà tra 5 secondi per guildId: {guildId}, steamId: {steamId}.", "ferryTerminal": "Ferry Terminal", "fishingVillage": "Villaggio di pesca", "five": "Cinque", "four": "Quattro", "giantExcavatorPit": "Grande Fossa di Scavo", "greaterThanSign": "Simbolo Maggiore di", "groupAddSwitchDesc": "Aggiungi Interruttore a {group}", "groupRemoveSwitchDesc": "<PERSON><PERSON><PERSON><PERSON> {group}", "harbor": "Porto", "hasBeenAliveLongest": "{name} è stato vivo per più tempo ({time}).", "hash": "<PERSON><PERSON><PERSON>", "hbhfSensor": "Sensore HBHF", "heart": "<PERSON><PERSON><PERSON>", "heater": "Stufa <PERSON>", "heavyScientistCalledSetting": "Quando gli Scienziati Pesanti vengono chiamati alla Piattaforma Petrolifera, invia una notifica.", "heavyScientistsCalledLarge": "Gli Scienziati Pesanti sono stati chiamati sulla Grande Oil Rig a {location}.", "heavyScientistsCalledSmall": "Gli Scienziati Pesanti sono stati chiamati sulla Piccola Piattaforma Petrolifera a {location}.", "hideTrademark": "Nascondi il marchio.", "hoster": "Hoster", "hp": "HP", "hpExceedMax": "Hp {hp} is exceeding max of {max}.", "hqmQuarry": "Cava di HQM", "ignoreSetAvatar": "setAvatar ignorato", "ignoreSetNickname": "setNickname ignorato", "ignoreSetUsername": "setUsername ignorato", "inGameBotMessagesMuted": "Messaggi del bot in-game mutati.", "inGameBotMessagesUnmuted": "Messaggi del bot in-game smutati.", "inGameCap": "IN-GAME", "inGameEventInfo": "Informazioni sugli eventi in-game", "inGameTeamNotificationsSetting": "Notifiche dei compagni di squadra in-game.", "inGameTime": "Orario in-game: {time}.", "index": "Indice", "infoCap": "INFO", "inside": "Inside", "interactionEditReplyFailed": "Risposta alla modifica dell'interazione fallita: {error}", "interactionInvalidChannel": "Interazione da un canale non valido.", "interactionReplyFailed": "Risposta all'interazione fallita: {error}", "interactionUpdateFailed": "Aggiornamento dell'interazione fallito: {error}", "invalidBattlemetricsId": "Invalid Battlemetrics ID.", "invalidGuildOrChannel": "Server o canale non valido.", "invalidHpInterval": "Invalid HP interval {hp}.", "invalidId": "ID non valido: {id}.", "invalidStructureType": "Invalid Structure type {type}.", "invalidSubcommand": "Sottocomando non valido.", "invalidTimeDistance": "Distanza di tempo non valida: {distance}, precedente: {prevTime}, nuovo: {newTime}", "isDecaying": "{device} sta decadendo!", "isNoLongerConnected": "{device} non è più connesso elettricamente!", "item": "<PERSON><PERSON><PERSON>", "itemAvailableInVendingMachine": "{items} è appena diventato disponibile in un Distributore Automatico in [{location}].", "itemAvailableNotifyInGameSetting": "Quando un oggetto dall'elenco di iscrizione diventa disponibile in un Distributore Automatico, notificare in gioco?", "junkyard": "Discarica", "justSubscribedToItem": "<PERSON><PERSON><PERSON> iscritto all'oggetto {name}.", "languageCode": "Codice lingua: {code}", "languageLangNotSupported": "La lingua {language} non è supportata.", "languageNotSupported": "La lingua non è supportata.", "largeBarn": "Grande Fienile", "largeFishingVillage": "Grande Villaggio di Pesca", "largeOilRig": "Grande Piattaforma Petrolifera", "largeWoodBox": "Grande Contenitore in Legno", "lastTrigger": "Last Trigger", "launchSite": "Sito di lancio", "leaderAlreadyLeader": "{name} è già il leader del team.", "leaderCommandIsDisabled": "Il comando del leader è disabilitato nelle impostazioni.", "leaderCommandOnlyWorks": "Il comando del leader funziona solo se il leader attuale è {name}.", "leaderTransferred": "La leadership del team è stata trasferita a {name}.", "leavingMapAt": "Leaving at {location}.", "lessThanSign": "Segno Inferiore a", "lighthouse": "Faro", "linkCap": "LINK", "location": "Posizione", "lockedCrateLargeOilRigUnlocked": "La cassa chiusa sulla Grande Piattaforma Petrolifera a {location} è stata sbloccata.", "lockedCrateOilRigUnlockedSetting": "Quando una Cassa chiusa sulla piattaforma petrolifera viene sbloccata, invia una notifica.", "lockedCrateSmallOilRigUnlocked": "Cassa chiusa su Piccola piattaforma petrolifera a {location} è stata sbloccata.", "logDiscordCommand": "Discord Command - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logDiscordMessage": "Discord Message - Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "logInGameCommand": "{type} - Command: {command}, User: {user}.", "logInGameMessage": "Message: {message}, User: {user}", "logSmartSwitchGroupValueChange": "Smart Switch Group - Value: {value}.", "logSmartSwitchValueChange": "Smart Switch - Value: {value}.", "loggedInAs": "LOGGATO COME: {name}", "makeSureApplicationsCommandsEnabled": "Assicurati che applications.commands sia selezionato quando crei l'URL di invito.", "map": "Mappa", "mapSalt": "Map Salt", "mapSeed": "<PERSON><PERSON>", "mapSize": "Dimensioni della Mappa", "mapWipeDetectedNotifySetting": "Quando viene rilevato il Wipe della mappa, {group} dovrebbe essere avvisato?", "markerAdded": "Il marcatore {name} in [{location}] è stato aggiunto.", "markerDoesNotExist": "Il marcatore {name} non esiste.", "markerLocation": "Il marcatore {name} in [{location}] è {distance}m da {player} nella direzione {direction}°.", "markerRemoved": "Il marcatore {name} in [{location}] è stato rimosso.", "message": "Messaggio", "messageCap": "MESSAGGIO", "messageDeletedIn30": "<PERSON><PERSON> messaggio verrà eliminato in 30 secondi.", "messageEditFailed": "Modifica del messaggio fallita: {error}", "messageReplyFailed": "Messaggio di risposta fallito: {error}", "messageSendFailed": "Invio del messaggio fallito: {error}", "messageWasSent": "Il messaggio è stato inviato.", "militaryTunnel": "Tunnel Militare", "miningOutpost": "Avamposto Minerario", "missileSilo": "Missile Silo", "missingArguments": "Argomenti mancanti.", "missingPermission": "Non hai il permesso di farlo.", "missingTimerMessage": "Messaggio del timer mancante.", "modalValueChange": "Modal Interaction - VerifyId: {id}, Value: {value}.", "more": "in più", "morePlayers": "{players} ...{number} in più.", "mutedCap": "SILENZIATO", "name": "Nome", "nameChangeHistory": "Storico dei cambi di nome", "new": "New", "newVendingMachine": "Nuovo Distributore Automatico situato in {location}.", "newsCap": "NOTIZIE", "noActiveTimers": "Nessun timer attivo.", "noCommandDelay": "<PERSON><PERSON><PERSON> ritardo di comando.", "noCommunicationSmartSwitch": "Impossibile comunicare con l'Interruttore Intelligente: {name}", "noData": "<PERSON><PERSON><PERSON> da<PERSON>.", "noDataOnLargeOilRig": "<PERSON><PERSON><PERSON> dato attuale sulla Grande Piattaforma Petrolifera.", "noDataOnSmallOilRig": "<PERSON><PERSON><PERSON> dato attuale sulla Piccola Piattaforma Petrolifera.", "noDelayCap": "NESSUN RITARDO", "noItemFound": "L'oggetto non può essere trovato in nessun distributore automatico...", "noItemWithIdFound": "<PERSON><PERSON><PERSON> oggetto con id {id} può essere trovato.", "noItemWithNameFound": "<PERSON><PERSON><PERSON> oggetto con il nome {name} può essere trovato.", "noNameIdGiven": "Nessun 'nome' o 'id' è stato fornito.", "noOneIsAfk": "Nessuno è AFK.", "noOneIsOffline": "Nessuno è offline.", "noOneIsOnline": "<PERSON><PERSON><PERSON> online.", "noRegisteredConnectionEvents": "Non ci sono eventi di connessione registrati ancora.", "noRegisteredConnectionEventsUser": "Non ci sono eventi di connessione registrati per {user}.", "noRegisteredDeathEvents": "Non ci sono eventi di morte registrati ancora.", "noRegisteredDeathEventsUser": "Non ci sono eventi di morte registrati per {user}.", "noRegisteredEvents": "No registered events yet.", "noRegisteredMarkers": "Nessun marcatore registrato.", "noSavedNotes": "Non ci sono note salvate.", "noToolCupboardWereFound": "Non è stato trovato nessun monitor dell'Armadio degli Attrezzi.", "none": "<PERSON><PERSON><PERSON>", "northEast": "Nord-Est", "northOfGrid": "Nord della colonna", "northWest": "Nord-Ovest", "notAValidOrderType": "{order} is not a valid order type.", "notActive": "Non attivo.", "notConnectedToRustServer": "Attualmente non connesso a un server di Rust.", "notExistInSubscription": "<PERSON>'oggetto {name} non esiste nell'elenco delle iscrizioni.", "notFoundCap": "NON TROVATO", "notPartOfRole": "Non fai parte del ruolo {role}, quindi non puoi eseguire comandi del bot.", "notShowingCap": "NON VISIBILE", "noteCap": "NOTA", "noteIdDoesNotExist": "L'ID della Nota: {id} non esiste.", "noteIdInvalid": "L'ID della Nota non è valido.", "noteIdWasRemoved": "L'ID della Nota: {id} è stato rimosso.", "noteSaved": "Nota salvata.", "offCap": "OFF", "offline": "Offline", "offlineTime": "Offline time", "oilRig": "Piattaforma Petrolifera", "old": "Old", "onCap": "ON", "one": "Uno", "online": "Online", "onlineTime": "Online time", "onlyOneInTeam": "Sei l'unico nella squadra.", "outpost": "Avamposto", "outside": "Outside", "oxumsGasStation": "Stazione Gas di Oxum", "pairing": "accoppiam<PERSON>o", "patrolHelicopter": "Elicottero di Pattuglia", "patrolHelicopterDestroyedSetting": "Quando l'Elicottero di Pattuglia viene distrutto, invia una notifica.", "patrolHelicopterDetectedSetting": "Quando l'Elicottero di Pattuglia viene rilevato, invia una notifica.", "patrolHelicopterEntersMap": "L'Elicottero di Pattuglia entra nella mappa da {location}.", "patrolHelicopterLeftMap": "L'Elicottero di Pattuglia ha appena lasciato la mappa a {location}.", "patrolHelicopterLeftSetting": "Quando l'Elicottero di Pattuglia lascia la mappa, invia una notifica.", "patrolHelicopterLocatedAt": "L'Elicottero di Pattuglia si trova a {location}.", "patrolHelicopterNotCurrentlyOnMap": "L'Elicottero di Pattuglia non è attualmente sulla mappa.", "patrolHelicopterTakenDown": "L'Elicottero di Pattuglia è stato abbattuto {location}.", "percentSign": "Segno di Percentuale", "pipe": "Tu<PERSON>", "playerHasBeenAliveFor": "{name} è stato vivo per {time}.", "playerId": "Player ID", "playerJoinedTheTeam": "{name} si è unito alla squadra.", "playerJustConnected": "{name} si è appena connesso.", "playerJustConnectedTo": "{name} si è appena connesso a {server}.", "playerJustConnectedTracker": "{name} just connected from tracker {tracker}.", "playerJustDied": "{name} è appena morto in {location}.", "playerJustDisconnected": "{name} si è appena disconnesso.", "playerJustDisconnectedFrom": "{name} si è appena disconnesso da {server}.", "playerJustDisconnectedTracker": "{name} just disconnected from tracker {tracker}.", "playerJustReturned": "{name} è appena tornato ({time}).", "playerJustWentAfk": "{name} è appena andato AFK.", "playerLeftTheTeam": "{name} ha lasciato la squadra.", "playerNotPairedWithServer": "Il comando Leader non funziona perché {name} non è abbinato al server.", "players": "Giocatori", "playersSearch": "Players Search", "plusSign": "<PERSON><PERSON>", "populationPlayers": "Popolazione: ({current}/{max}) giocatori.", "populationQueue": "{number} giocatori in coda.", "powerPlant": "Centrale Elettrica", "profile": "Profile", "proxLocation": "{name} si trova a {distance}m da {caller} in direzione {direction}° [{location}]", "quantity": "Quantità", "questionMark": "Punto Interrogativo", "ranch": "Ranch", "ratelimited": "LIMITE DI VELOCITÀ", "reconnectingCap": "RICONNESSIONE", "reconnectingToServer": "RICONNESSIONE AL SERVER IN CORSO...", "recycle": "Recycle", "recycleCap": "RICICLA", "recycler": "Recycler", "remain": "rimasto", "removePlayerCap": "RIMUOVI GIOCATORE", "removeSwitchCap": "RIMUOVI INTERRUTTORE", "removedSubscribeItem": "<PERSON>'oggetto {name} è stato rimosso dall'iscrizione.", "research": "Research", "researchTable": "Research Table", "resetSuccess": "Discord ripristinato con successo.", "responseContainError": "La risposta contiene una proprietà di errore con valore: {error}.", "responseIsEmpty": "La risposta è vuota.", "responseIsUndefined": "La risposta è indefinita.", "responseTimeout": "Timeout raggiunto durante l'attesa della risposta.", "resultRecycling": "Risultato del riciclo", "roleCleared": "rustplusplus role has been cleared.", "roleSet": "il ruolo rustplusplus è stato impostato su {name}.", "rustMonument": "Monumento di Rust", "rustplusOperational": "RUSTPLUS OPERATIVO.", "safe-zone-recycler": "Safe Zone Recycler", "samsite": "Torretta SAM", "satelliteDish": "Parabola Satellitare", "scrap": "<PERSON><PERSON><PERSON>", "searchResult": "Risultati di ricerca per l'oggetto: **{name}**", "second": "{second} secondo", "secondCommandDelay": "<PERSON><PERSON> del comando di {second} secondo.", "seconds": "{seconds} secondi", "secondsCommandDelay": "<PERSON><PERSON> del comando di {seconds} secondi.", "selectInGamePrefixSetting": "Seleziona quale prefisso utilizzare per il comando in-game:", "selectLanguageExtendSetting": "Assicurati di eseguire **/reset discord** per caricare correttamente la nuova lingua.", "selectLanguageSetting": "Seleziona la lingua da utilizzare per il bot:", "selectMenuValueChange": "Seleziona interazione menu: IdVerifica: {id}, Valore: {value}.", "selectTrademarkSetting": "Seleziona il marchio che deve essere visualizzato in ogni messaggio in-game.", "sell": "vendi", "semicolon": "Punto e virgola", "sentTextToSpeech": "Inviata la sintesi vocale.", "server": "server", "serverId": "ID Server", "serverInfo": "Informazioni sul Server", "serverInvalid": "La connessione al server sembra essere invalida. Prova a riparare la connessione al server.", "serverJustOffline": "Il server è appena andato offline.", "serverJustOnline": "Il server è appena andato online.", "serverStatus": "Stato del Server", "serviceUnavailable": "Servizio non disponibile: {error}", "setBotLanguage": "Imposta la lingua del bot su: {language}.", "seven": "Set<PERSON>", "sewerBranch": "Rete Fognaria", "shouldBotBeMutedSetting": "Il bot deve essere mutato in-game?", "shouldCommandsEnabledSetting": "I comandi in gioco devono essere abilitati?", "shouldLeaderCommandEnabledSetting": "Il comando leader deve essere abilitato?", "shouldLeaderCommandOnlyForPairedSetting": "Il comando leader deve funzionare solo per le persone accoppiate con il server?", "shouldSmartAlarmNotifyNotConnectedSetting": "Le Sveglie Intelligenti devono inviare una notifica anche se non sono impostate sul server Rust connesso?", "shouldSmartAlarmsNotifyInGameSetting": "Le Sveglie Intelligenti devono avvisare in gioco?", "shouldSmartSwitchNotifyInGameWhenChangedFromDiscord": "Should Smart Switches and Smart Switch Groups notify In-Game when they are changed from discord?", "showingBlacklist": "Mostrando la blacklist.", "showingSubscriptionList": "Mostrando l'elenco di iscrizioni.", "shredder": "Shredder", "sirenLight": "<PERSON>", "six": "<PERSON><PERSON>", "slash": "Barr<PERSON>", "slashCommandInteraction": "Interazione Comando Slash - Guild: {guild}, Canale: {channel}, Utente: {user}, Comando: {command}, VerifyId: {id}.", "slashCommandValueChange": "Interazione Del Comando Slash: IdVerifica: {id}, Valore: {value}.", "slashCommandsSuccessRegister": "Comandi dell'applicazione registrati con successo per il server: {guildId}.", "slots": "Slots", "smallOilRig": "<PERSON><PERSON><PERSON>aforma Petrolifera", "smartAlarm": "Sveglia Intelligente", "smartAlarmEditSuccess": "Sveglia Intelligente {name} modificato con successo.", "smartAlarmNotifyExtendSetting": "- Queste notifiche della Sveglia utilizzeranno il titolo e il messaggio forniti per la Sveglia Intelligente in gioco.\n- Queste Sveglie Intelligenti potrebbero non essere disponibili nel canale testuale delle Sveglie in discord.", "smartDeviceNotFound": "{device} non può essere trovato! È stato distrutto o {user} ha perso l'autorizzazione all'armadio degli attrezzi.", "smartSwitch": "Interruttore Intelligente", "smartSwitchAutoDay": "L'Interruttore Intelligente sarà attivo solo durante il giorno.", "smartSwitchAutoNight": "L'Interruttore Intelligente sarà attivo solo durante la notte.", "smartSwitchAutoOff": "Smart Switch will automatically go inactive during update cycle.", "smartSwitchAutoOffAnyOnline": "Smart Switch will automatically go inactive if any teammate is online.", "smartSwitchAutoOffProximity": "Smart Switch will automatically go inactive if teammate is in proximity.", "smartSwitchAutoOn": "Smart Switch will automatically go active during update cycle.", "smartSwitchAutoOnAnyOnline": "Smart Switch will automatically go active if any teammate is online.", "smartSwitchAutoOnProximity": "Smart Switch will automatically go active if teammate is in proximity.", "smartSwitchEditProximityLabel": "Proximity Setting (meters):", "smartSwitchEditSuccess": "Interruttore Intelligente {name} modificato con successo.", "smartSwitchNormal": "L'Interruttore Intelligente funziona normalmente.", "smilyFace": "Faccina <PERSON>rri<PERSON>e", "somethingWrongWithConnection": "Qualcosa è andato storto con la connessione.", "southEast": "Sud-Est", "southOfGrid": "Sud della colonna", "southWest": "Sud-Ovest", "sprinkler": "Irrigatore", "stackSize": "Stack Size", "stackSizeOfItem": "Stack size of {item} is {quantity}x.", "status": "Stato", "statusNotConnectedToServer": "**STATO** `NON CONNESSO AL SERVER!`", "statusNotElectronicallyConnected": "**STATO** `NON COLLEGATO ELETTRICAMENTE!`", "statusNotFound": "**STATO**: NON TROVATO", "steamId": "SteamID", "stoneQuarry": "Cava di pietra", "storageMonitor": "Monitor di Archiviazione", "storageMonitorEditSuccess": "Modifica del Monitor di Archiviazione {name} effettuata con successo.", "streamerMode": "Streamer Mode", "subscribeToChangesBattlemetrics": "Subscribe to different changes on Battlemetrics.", "subscriptionList": "Elenco iscrizioni", "subscriptionListEmpty": "L'elenco delle iscrizioni degli oggetti è vuoto.", "sulfurQuarry": "Cava di Zolfo", "switches": "Interruttori", "teamMember": "Membro del Team", "teamMemberInfo": "Informazioni sul Membro del Team", "theDome": "La Cupola", "theIdOfTheItem": "The id of the item.", "theNameOfTheItem": "The name of the item.", "theNameOfThePlayer": "The name of the player.", "three": "Tre", "tilde": "<PERSON><PERSON>", "time": "<PERSON>a", "timeBeforeCargoEntersEgress": "{time} before Cargo Ship at {location} enters egress stage.", "timeBeforeCrateAtLargeOilRigUnlocks": "{time} prima che la Cassa Chiusa alla Grande Piattaforma Petrolifera ({location}) venga sbloccata.", "timeBeforeCrateAtSmallOilRigUnlocks": "{time} prima che la Cassa Chiusa alla Piccola Piattaforma Petrolifera ({location}) venga sbloccata.", "timeCap": "TEMPO", "timeFormatInvalid": "Formato orario non valido.", "timeLeftTimer": "{id}: <PERSON><PERSON>: {time}, Messaggio: {message}", "timeSinceAlarmWasTriggered": "The alarm {alarm} was triggered {time} ago.", "timeSinceCargoLeft": "{time} dal momento in cui la Nave Cargo ha lasciato la mappa.", "timeSinceChinook47OnMap": "{time} dal momento in cui l'ultimo Chinook 47 era sulla mappa.", "timeSinceHeavyScientistsOnLarge": "{time} da quando gli Scienziati Pesanti sono stati chiamati alla Grande Piattaforma Petrolifera l'ultima volta.", "timeSinceHeavyScientistsOnSmall": "{time} da quando gli Scienziati Pesanti sono stati chiamati alla Piccola Piattaforma Petrolifera l'ultima volta.", "timeSinceLast": "{time} dall'ultimo.", "timeSinceLastEvent": "{time} dall'ultimo evento.", "timeSinceLastSinceDestroyedLong": "{time1} since the last Patrol Helicopter was on the map, {time2} since it last got downed{location}.", "timeSinceLastSinceDestroyedShort": "{time1} since last.\n{time2} since destroyed{location}.", "timeSincePatrolHelicopterWasOnMap": "{time} dal momento in cui l'Elicottero di Pattuglia era in mappa.", "timeSinceTravelingVendorWasOnMap": "{time} since the Traveling Vendor was on the map.", "timeSinceWipe": "{time} dal momento del wipe.", "timeTill": "Tempo fino a {event}", "timeTillDaylight": "{time} prima dell'alba.", "timeTillNightfall": "{time} prima della notte.", "timeTillStructureDecay": "{time} before {type} wall decay.", "timeUntilUnlocksAt": "{time} prima dell'apertura a {location}.", "timer": "Timer: {message}.", "timerIdDoesNotExist": "L'ID del timer: {id} non esiste.", "timerIdInvalid": "L'ID del timer non è valido.", "timerRemoved": "L'ID del timer: {id} è stato rimosso.", "timerSet": "Timer impostato per {time}.", "tokensDidNotReplenish": "I token non si sono riforniti in tempo.", "toolCupboard": "Armadio de<PERSON>", "total": "Total", "tracker": "Tracker", "trackerAddPlayerDesc": "Aggiungi giocatore a {tracker}", "trackerRemovePlayerDesc": "<PERSON><PERSON><PERSON><PERSON> gio<PERSON>ore da {tracker}", "trademarkShownBeforeMessage": "{trademark} sarà mostrato prima dei messaggi.", "trainYard": "Cantiere dei Treni", "travelingVendor": "Traveling Vendor", "travelingVendorDetectedSetting": "When the Traveling Vendor is detected, send a notification.", "travelingVendorHaltedAt": "The Traveling Vendor stopped at {location}.", "travelingVendorHaltedSetting": "When the Traveling Vendor stops moving, send a notification.", "travelingVendorLeftSetting": "When the Traveling Vendor left the map, send a notification.", "travelingVendorLocatedAt": "The Traveling Vendor is located at {location}.", "travelingVendorLeftMap": "The Traveling Vendor just left the map at {location}.", "travelingVendorNotCurrentlyOnMap": "The Traveling Vendor is not currently on the map.", "travelingVendorResumedAt": "The Traveling Vendor resumed moving at {location}.", "travelingVendorSpawnedAt": "The Traveling Vendor spawned at {location}.", "turnOffCap": "SPEGNI", "turnOnCap": "ACCENDI", "turningGroupOnOff": "Impostando Gruppo {group} {status}.", "two": "Due", "type": "Tipo", "unavailable": "Non disponibile", "underscore": "Sottolineatura", "underwater": "Underwater", "underwaterLab": "Laboratorio Sottomarino", "unhandledRejection": "Rif<PERSON>to non gestito: {error}", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknownInteraction": "Interazione Sconosciuta...", "unmutedCap": "SMUTATO", "updateCap": "UPDATE", "upkeep": "Mantenimento", "upkeepForItem": "Upkeep for {item} is {cost}.", "userAddedToBlacklist": "{user} was added to blacklist.", "userAlreadyInBlacklist": "{user} already in blacklist.", "userButtonInteraction": "Button Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userButtonInteractionSuccess": "Button Interaction - VerifyId: {id} SUCCESS", "userJustConnected": "{name} si è appena connesso.", "userModalInteraction": "Modal Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userModalInteractionSuccess": "Modal Interaction - VerifyId: {id} SUCCESS", "userNotInBlacklist": "{user} not in blacklist.", "userNotRegistered": "{user} non è registrato.", "userPartOfBlacklist": "VerifyId: {id}, {user} is part of the blacklist.", "userPartOfBlacklistDiscord": "Blacklisted User! Guild: {guild}, Channel: {channel}, User: {user}, Message: {message}.", "userPartOfBlacklistInGame": "Blacklisted User! User: {user}, Message: {message}.", "userRemovedFromBlacklist": "{user} was removed from blacklist.", "userSaid": "{user} ha detto, {text}", "userSelectMenuInteraction": "Select Menu Interaction - Guild: {guild}, Channel: {channel}, User: {user}, CustomId: {customid}, VerifyId: {id}.", "userSelectMenuInteractionSuccess": "Select Menu Interaction - VerifyId: {id} SUCCESS", "userTurnedOnOffSmartSwitchFromDiscord": "{user} turned Smart Switch {name} {status} from discord.", "userTurnedOnOffSmartSwitchGroupFromDiscord": "{user} turned Smart Switch Group {name} {status} from discord.", "value": "Value", "vendingMachine": "Distributore automatico", "vendingMachineDetectedSetting": "Quando viene rilevato un nuovo Distributore Automatico, invia una notifica.", "voiceCap": "VOICE", "warningCap": "ATTENZIONE", "waterTreatmentPlant": "Impianto di Trattamento dell'Acqua", "websiteCap": "SITO WEB", "websocketClosedBeforeConnection": "La connessione WebSocket è stata chiusa prima di essere stabilita.", "westOfGrid": "Ovest della riga", "wipe": "Wipe", "wipeDetected": "<PERSON><PERSON><PERSON><PERSON> wipe!", "yield": "Yield", "youAreAlreadyLeader": "<PERSON>i già il leader.", "youAreNotPairedWithServer": "Il comando del leader non funziona perché non sei associato al server."}